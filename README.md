# 🚀 Kapreon Website

Site web officiel de l'agence Kapreon - Une plateforme moderne et multilingue présentant nos services, projets et actualités.

## 🌍 À propos

Ce site présente l'expertise de Kapreon en design, développement et stratégie digitale. Il offre une expérience utilisateur optimisée avec un contenu adapté aux marchés français et anglais.

## ✨ Fonctionnalités principales

- **🌐 Site bilingue** - Navigation fluide entre français et anglais
- **📱 Design responsive** - Expérience optimale sur tous les appareils
- **🎨 Portfolio interactif** - Présentation dynamique de nos projets
- **📝 Blog intégré** - Articles et actualités de l'agence
- **⚡ Performance optimisée** - Chargement rapide et SEO avancé
- **🎭 Animations fluides** - Interface moderne et engageante

## 🗂️ Structure du site

### Pages principales
- **Accueil** - Présentation de l'agence et services
- **Projets** - Portfolio avec filtrage par expertise
- **Blog** - Articles et actualités
- **Contact** - Formulaire et informations de contact
- **FAQ** - Questions fréquentes
- **Mentions légales** - Informations juridiques

### Langues disponibles
- **Français** - Marché principal (Canada/France)
- **Anglais** - Marché international

### Types de contenu
- **Projets** - Études de cas détaillées avec images
- **Articles de blog** - Actualités et conseils
- **Pages informatives** - Présentation des services

## 🚀 Développement local

### Prérequis
- Node.js 18+
- npm ou yarn

### Installation
```bash
# Cloner le repository
git clone [repository-url]
cd kapreon-website

# Installer les dépendances
npm install

# Lancer en développement
npm run dev
```

Ouvrir [http://localhost:3000](http://localhost:3000) dans votre navigateur.

### Scripts disponibles
```bash
npm run dev          # Serveur de développement
npm run build        # Build de production
npm run start        # Serveur de production
npm run lint         # Vérification du code
```

## 🛠️ Technologies

- **Framework** : Next.js (React)
- **Styling** : SCSS
- **Animations** : Framer Motion, GSAP
- **Contenu** : Markdown (MDX)
- **Hébergement** : Vercel

## � Optimisations

- **Performance** - Génération statique et optimisation automatique
- **SEO** - Métadonnées dynamiques et sitemap multilingue
- **Accessibilité** - Standards WCAG respectés
- **Mobile-first** - Design adaptatif sur tous les appareils

## 🚀 Déploiement

### Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/deployment) for more details.

### Deploy on Cloudron

Ensure your have the Docker engine and the [cloudron CLI](https://www.npmjs.com/package/cloudron) installed.

1. Login to the docker registry
```bash
# Use the same credentials than when logging in the Cloudron dashboard.
docker login cr.kapreon.org
```

2. Build the image
```bash
# Replace x.y.z by your version number
docker build -t cr.kapreon.org/kapreon/website-cloudron:x.y.z .
```

3. Push the image
```bash
docker push cr.kapreon.org/kapreon/website-cloudron:x.y.z
```

4. Update the application
```bash
# Replace app.example.com by the domain of your Cloudron application.
cloudron update --image cr.kapreon.org/kapreon/website-cloudron:x.y.z --app app.example.com
```

## 🤝 Contribution

1. Fork le projet
2. Créer une branche feature (`git checkout -b feature/nouvelle-fonctionnalite`)
3. Commit les changements (`git commit -m 'Ajout nouvelle fonctionnalité'`)
4. Push vers la branche (`git push origin feature/nouvelle-fonctionnalite`)
5. Ouvrir une Pull Request

## 📄 License

Ce projet est sous licence privée - voir le fichier LICENSE pour plus de détails.

---

**Développé avec ❤️ par l'équipe Kapreon**
