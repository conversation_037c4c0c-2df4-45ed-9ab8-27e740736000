# ------------------------
# 1. Image de base Cloudron
# ------------------------
FROM cloudron/base:4.2.0@sha256:46da2fffb36353ef714f97ae8e962bd2c212ca091108d768ba473078319a47f4 AS base

# Prépare le dossier d'application en écriture pendant le build
RUN mkdir -p /app/code
WORKDIR /app/code

# ------------------------
# 2. Build de l'app Next.js
# ------------------------
FROM base AS builder
WORKDIR /app/code

COPY . /app/code/

ENV NODE_ENV=production
ENV HUSKY=0

RUN npm install
# Installe sharp pour l'optimisation d'images
RUN npm install sharp --omit=dev

# Génération du build Next.js
RUN npm run build

# Nettoie l'ancien cache (inutile en build)
RUN rm -rf /app/code/.next/cache

# ------------------------
# 3. Image de production
# ------------------------
FROM base AS runner

ENV NODE_ENV=production

# Dépendances système pour sharp
RUN apt-get update \
    && apt-get install -y --no-install-recommends libvips \
    && rm -rf /var/lib/apt/lists/*

# On se place dans le dossier final de l'app
WORKDIR /app/code/website

# Copie de l'app buildée
COPY --from=builder /app/code/package.json ./
COPY --from=builder /app/code/node_modules ./node_modules
COPY --from=builder /app/code/src ./src
COPY --from=builder /app/code/public ./public
COPY --from=builder /app/code/next.config.js ./
COPY --from=builder /app/code/jsconfig.json ./
COPY --from=builder /app/code/.next ./.next

# — Patch pro : rendre .next/cache inscriptible —
RUN mkdir -p /app/data/next-cache \
    && rm -rf /app/code/website/.next/cache \
    && ln -s /app/data/next-cache /app/code/website/.next/cache

# Script de démarrage Cloudron
COPY ./packages/cloudron/src/env.sh.template ./packages/cloudron/src/start.sh /app/pkg/
RUN chmod +x /app/pkg/start.sh

CMD [ "/app/pkg/start.sh" ]
