## [1.23.2](https://github.com/Kapreon/kapreon-website/compare/v1.23.1...v1.23.2) (2025-06-29)


### Bug Fixes

* change Konvergo Allo workflows url ([4238893](https://github.com/Kapreon/kapreon-website/commit/423889315e750d1c49e5c7185268d9b74cfd76c2))


### Reverts

* test dockerfile push autodeploy ([daa907f](https://github.com/Kapreon/kapreon-website/commit/daa907f05a3667e1600d2b6609e2935a7c933d12))

## [1.23.1](https://github.com/Kapreon/kapreon-website/compare/v1.23.0...v1.23.1) (2025-06-25)


### Bug Fixes

* docker deploiement ([6e8bdec](https://github.com/Kapreon/kapreon-website/commit/6e8bdec1787822a274f2ba71e791e504a1fbb860))

# [1.23.0](https://github.com/Kapreon/kapreon-website/compare/v1.22.0...v1.23.0) (2025-06-24)


### Features

* translate published blog posts ([0bd03b5](https://github.com/Kapreon/kapreon-website/commit/0bd03b52f1d22cd67d0a7d8e0d75017a7e836a90))

# [1.22.0](https://github.com/Kapreon/kapreon-website/compare/v1.21.1...v1.22.0) (2025-06-24)


### Features

* add animated links to Plaida ([802a8da](https://github.com/Kapreon/kapreon-website/commit/802a8da277f20f2978567f3d43a82d61f10c4c9b))
* add content and images for Plaida case study ([69913f9](https://github.com/Kapreon/kapreon-website/commit/69913f935bfb28129897a915cb53a2531eafd894))

## [1.21.1](https://github.com/Kapreon/kapreon-website/compare/v1.21.0...v1.21.1) (2025-06-24)


### Bug Fixes

* change active state of project's filter button ([edccf24](https://github.com/Kapreon/kapreon-website/commit/edccf24f3648d33000d007cefd41c8feb5fe45be))

# [1.21.0](https://github.com/Kapreon/kapreon-website/compare/v1.20.0...v1.21.0) (2025-06-24)


### Bug Fixes

* content alignement for legal pages ([d1df088](https://github.com/Kapreon/kapreon-website/commit/d1df088fe41951827c380af8fe01aa91770c65d7))


### Features

* add grid/list filter view ([885e033](https://github.com/Kapreon/kapreon-website/commit/885e03346426031ae48981376aba6e2cde3b2c5b))
* change categories to use taxonomies ([b6b2811](https://github.com/Kapreon/kapreon-website/commit/b6b281162dc18031d048b7b39600a0e7def12917))
* translate statics legal pages ([7523c96](https://github.com/Kapreon/kapreon-website/commit/7523c96aceafff0ea6ada49a322d736e0f8a5846))

# [1.20.0](https://github.com/Kapreon/kapreon-website/compare/v1.19.0...v1.20.0) (2025-06-23)


### Features

* add missing translation for project's hover ([4d1cb77](https://github.com/Kapreon/kapreon-website/commit/4d1cb77c43509b19a2419ef32b2bb37e8b694708))
* add translation to website ([9bc92d9](https://github.com/Kapreon/kapreon-website/commit/9bc92d94b014a2d48eaa02b821cfd654644a773f))

# [1.19.0](https://github.com/Kapreon/kapreon-website/compare/v1.18.0...v1.19.0) (2025-06-23)


### Bug Fixes

* remove unused locomotive scroll ([bb60fc3](https://github.com/Kapreon/kapreon-website/commit/bb60fc3dc3c5b6acf8c203ee2d47a0ea51a6e192))


### Features

* add Deezer blog article ([2830c9f](https://github.com/Kapreon/kapreon-website/commit/2830c9fcc0dd2a4a98c3484b1643b6bb1a37f116))
* add published state to blog's post ([b871f3b](https://github.com/Kapreon/kapreon-website/commit/b871f3b88bcaf8a2608fbbdd7a525681c27a39e0))

# [1.18.0](https://github.com/Kapreon/kapreon-website/compare/v1.17.0...v1.18.0) (2025-05-19)


### Features

* add blog articles images ([29fa356](https://github.com/Kapreon/kapreon-website/commit/29fa356c274f367e5ce3e524aa180a42cfd1177c))

# [1.17.0](https://github.com/Kapreon/kapreon-website/compare/v1.16.0...v1.17.0) (2025-05-18)


### Features

* add images and mdximage component ([0bf9569](https://github.com/Kapreon/kapreon-website/commit/0bf9569848f2ab69bc9d006658186bd4da0c2739))
* add Tweet component ([88c8f77](https://github.com/Kapreon/kapreon-website/commit/88c8f771b43bd3d76efa68caea16840cb4ea9ebc))

# [1.16.0](https://github.com/Kapreon/kapreon-website/compare/v1.15.0...v1.16.0) (2025-05-18)


### Features

* add mask on hover ([da43526](https://github.com/Kapreon/kapreon-website/commit/da4352632f0ade04ba735f8c72048f8bd869790f))

# [1.15.0](https://github.com/Kapreon/kapreon-website/compare/v1.14.0...v1.15.0) (2025-05-18)


### Bug Fixes

* add max width on text hero ([af3216e](https://github.com/Kapreon/kapreon-website/commit/af3216e8ad133af60a39c467df93e82daaa1516d))
* change small container padding on mobile ([db1dba4](https://github.com/Kapreon/kapreon-website/commit/db1dba4edba7b4bbf894bdc25f812fd9137b086d))


### Features

* add breadcrumb ([41fb681](https://github.com/Kapreon/kapreon-website/commit/41fb681330a7b044c4db38f2739452cf0a3814ee))

# [1.14.0](https://github.com/Kapreon/kapreon-website/compare/v1.13.1...v1.14.0) (2025-05-18)


### Features

* add animations ([e50d881](https://github.com/Kapreon/kapreon-website/commit/e50d88173c1c3e1aec5bd1602b94fb2840d82c69))
* add blog configurations ([773ea98](https://github.com/Kapreon/kapreon-website/commit/773ea98fbfd7f5e8bbb494d6333fc82108f1cc6c))

## [1.13.1](https://github.com/Kapreon/kapreon-website/compare/v1.13.0...v1.13.1) (2025-05-17)


### Bug Fixes

* increase cookie consent z-index ([d992162](https://github.com/Kapreon/kapreon-website/commit/d99216247f4a13ad2203fc9738379da7432918a3))

# [1.13.0](https://github.com/Kapreon/kapreon-website/compare/v1.12.2...v1.13.0) (2025-05-17)


### Bug Fixes

* projects delay animation ([17896fd](https://github.com/Kapreon/kapreon-website/commit/17896fda64d67b64f8aecae1c5d645534cde12dd))


### Features

* add split text animation ([56e22a8](https://github.com/Kapreon/kapreon-website/commit/56e22a891174cec8b5dc9634e6ed81afe806e4c9))

## [1.12.2](https://github.com/Kapreon/kapreon-website/compare/v1.12.1...v1.12.2) (2025-05-07)


### Bug Fixes

* search Console and analytics ([5657f66](https://github.com/Kapreon/kapreon-website/commit/5657f6609842dcf490483b048f5abd1b8ebef1d5))

## [1.12.1](https://github.com/Kapreon/kapreon-website/compare/v1.12.0...v1.12.1) (2025-04-30)


### Bug Fixes

* **double scroll step issue:** double scroll step issue ([60bddb4](https://github.com/Kapreon/kapreon-website/commit/60bddb435703ca61c59531dfce2d5a6907383c91))
* issue when hover a project with an active filtr ([0304d9f](https://github.com/Kapreon/kapreon-website/commit/0304d9f25ca7b222fd36f73841d2c69cc78986aa))

# [1.12.0](https://github.com/Kapreon/kapreon-website/compare/v1.11.2...v1.12.0) (2025-04-30)


### Bug Fixes

* make case intro image mask responsive on mobile ([dbe7889](https://github.com/Kapreon/kapreon-website/commit/dbe7889525b4846170fea2df7cf7791775428e34))


### Features

* add Konvergo website project's texts ([2c0d3c3](https://github.com/Kapreon/kapreon-website/commit/2c0d3c30f9717a0b3a1d4f8ecd9ee92a0955fcc6))

## [1.11.2](https://github.com/Kapreon/kapreon-website/compare/v1.11.1...v1.11.2) (2025-04-29)


### Bug Fixes

* add OCA Days color project ([4b0ac00](https://github.com/Kapreon/kapreon-website/commit/4b0ac001b4630d8585cc36aee5f67116ef604a98))

## [1.11.1](https://github.com/Kapreon/kapreon-website/compare/v1.11.0...v1.11.1) (2025-04-29)


### Bug Fixes

* show mask on mobile without hover ([eb3143d](https://github.com/Kapreon/kapreon-website/commit/eb3143da3acb99a35c9b7fbc2c385ce3a8ef5927))

# [1.11.0](https://github.com/Kapreon/kapreon-website/compare/v1.10.0...v1.11.0) (2025-04-29)


### Bug Fixes

* hide magnetic effect on mobile ([906f62a](https://github.com/Kapreon/kapreon-website/commit/906f62a35c5249b2d1dd0c8b2fce8c8ad7728d41))


### Features

* add counter of total projects in filters ([a4bd2f6](https://github.com/Kapreon/kapreon-website/commit/a4bd2f68984b7405cf7d9a0084e0eaa8bf57c49c))

# [1.10.0](https://github.com/Kapreon/kapreon-website/compare/v1.9.0...v1.10.0) (2025-04-28)


### Bug Fixes

* correcting categories ([8f1a52b](https://github.com/Kapreon/kapreon-website/commit/8f1a52b972f2e7e4b361103c11a3046106d03133))


### Features

* add Konvergo Tour 2024 project ([6d0522f](https://github.com/Kapreon/kapreon-website/commit/6d0522f28f0b1f823f59c1fde75f3e682b59e94d))
* add Konvergo Tour 2024 texts ([dc3a153](https://github.com/Kapreon/kapreon-website/commit/dc3a15344fa69b9f345f3d5e1b1692c9079d8feb))
* add Numigi website project's image ([b2e91c7](https://github.com/Kapreon/kapreon-website/commit/b2e91c7aded8ebb7a06da556843d75aa99dcd6d5))
* add OCA Days 2023 project ([f53c2a7](https://github.com/Kapreon/kapreon-website/commit/f53c2a77afd206f3b06c1dc77d51991e683dd8de))
* add OCA Days 2023 project's images ([f363cc0](https://github.com/Kapreon/kapreon-website/commit/f363cc081492a86ec1584fe95b46285f31fe2658))

# [1.9.0](https://github.com/Kapreon/kapreon-website/compare/v1.8.1...v1.9.0) (2025-04-26)


### Bug Fixes

* correcting the image ratio error ([b9eaaac](https://github.com/Kapreon/kapreon-website/commit/b9eaaac8bb25e9576a7b699695045ebcb4d15578))
* fix animated link issue ([05fb113](https://github.com/Kapreon/kapreon-website/commit/05fb11312c8f06a63c96de60c79f60d72b389eb6))
* fix default styles on mobile device button tag ([94be680](https://github.com/Kapreon/kapreon-website/commit/94be680718d1ea630ebe6e629085a76724bc74e9))
* fix image sizes caroussel sliding projects ([b7825d1](https://github.com/Kapreon/kapreon-website/commit/b7825d123396fdacb7c7a88edc209ef6c8b8d662))
* hide FAQ CTA in contact page ([00cff6e](https://github.com/Kapreon/kapreon-website/commit/00cff6e4abadd8397ea5eced1e443d5c3bbd8879))
* locomotive Scroll Provider ([734b509](https://github.com/Kapreon/kapreon-website/commit/734b509a283fe803e0ab529306750d4c7399e101))
* make next cache writable ([47da4f9](https://github.com/Kapreon/kapreon-website/commit/47da4f96d629b0997b1909eef55744e114630306))
* menu max width not centered ([14cdfa9](https://github.com/Kapreon/kapreon-website/commit/14cdfa94298963e132d9f6712eedd27564bd4584))
* remove magnetic effect for mobile ([d5e835f](https://github.com/Kapreon/kapreon-website/commit/d5e835ffec1f2a1e62957c13a1b1c922a5981b8a))
* rename SVG image to camelCase properties ([c4bf416](https://github.com/Kapreon/kapreon-website/commit/c4bf4163541743cf4283fc436237c8ba1f7c4356))
* resolve warning and use correct base URL for social images ([ccd6914](https://github.com/Kapreon/kapreon-website/commit/ccd691405f7225aae38557a1b4b35f52652855c2))
* scroll issue on mobile ([291890e](https://github.com/Kapreon/kapreon-website/commit/291890eff91df1ddf0a32855fea8bbf18a1a2282))
* temporary hide "view website" button in a case study ([933d1df](https://github.com/Kapreon/kapreon-website/commit/933d1dfc58bca83c8cfd5217bcc306ad109766a6))
* temporary hide transition page ([f7961e0](https://github.com/Kapreon/kapreon-website/commit/f7961e0f91e0e0b0a68797714b1fcaf8e2068bde))
* temporary remove magnetic button in header ([e0c4c81](https://github.com/Kapreon/kapreon-website/commit/e0c4c8197c673addaeac0f11d93a61582d240fa7))
* test images optimization fix ([ce5eca7](https://github.com/Kapreon/kapreon-website/commit/ce5eca70614571db6b42f60c43bd2126a9673c6f))


### Features

* add sharp for Next.js images ([93452e1](https://github.com/Kapreon/kapreon-website/commit/93452e171345395f87b6ec985fef81bc34697362))


### Performance Improvements

* convert jpg and png project's images to webp ([fa51b0a](https://github.com/Kapreon/kapreon-website/commit/fa51b0a2a77bcf1f017d5156f1eb23ece8f3e343))

## [1.8.1](https://github.com/Kapreon/kapreon-website/compare/v1.8.0...v1.8.1) (2025-03-11)


### Bug Fixes

* modify texts ([302da65](https://github.com/Kapreon/kapreon-website/commit/302da65e0ee5c8be59c28d2b49629a68265ff104))

# [1.8.0](https://github.com/Kapreon/kapreon-website/compare/v1.7.1...v1.8.0) (2025-03-08)


### Bug Fixes

* add cloudron to deps ([fd016c3](https://github.com/Kapreon/kapreon-website/commit/fd016c388d1c6139a08afa83d88de04b8f1baaea))
* add FAQ link to footer ([95498e8](https://github.com/Kapreon/kapreon-website/commit/95498e8b6a5599adfaefbd82703a170dd9071835))
* add global install of cloudron package ([bf77de2](https://github.com/Kapreon/kapreon-website/commit/bf77de2cd2c1f53715356ffb9f5780ff6ecf92e4))
* add missing env variable to skip husky ([9b283da](https://github.com/Kapreon/kapreon-website/commit/9b283da892192e8815759ec61f16915267a30e77))
* add padding bottom and change background color ([faeb731](https://github.com/Kapreon/kapreon-website/commit/faeb73107a2a81f4866dbb2f40b45e309bbe0457))
* add quotes ([d93273e](https://github.com/Kapreon/kapreon-website/commit/d93273ebdec4a1f5cd10a640bf46bff6a1582dd0))
* change build action output ([7ca4a9a](https://github.com/Kapreon/kapreon-website/commit/7ca4a9a6651b2c9c3aba265c7e1e25f0d8b391b3))
* change npm installation command in Cloudron's package ([ac072c1](https://github.com/Kapreon/kapreon-website/commit/ac072c1e8d9750f9f64232ba0be2f4bbf7642112))
* change project's cover images ([2355789](https://github.com/Kapreon/kapreon-website/commit/235578959c2129f81f5e237b05da04ad75c679e4))
* change short-hand flags ([0b0564f](https://github.com/Kapreon/kapreon-website/commit/0b0564f8b41e24618059090d53775eeaf2a7bf8c))
* deploy workflow npm installation ([b314f6f](https://github.com/Kapreon/kapreon-website/commit/b314f6f35796073bc3f55cf01960861362715d67))
* fix button z-index intro image on case page ([b36e976](https://github.com/Kapreon/kapreon-website/commit/b36e97664f9e006ac2aba9eda8437df2dfac877a))
* force prepare script to never fail ([1ba944f](https://github.com/Kapreon/kapreon-website/commit/1ba944f4ce0d5cb26ce709c107d258e73c11225e))
* hide CTA and add padding ([d67fe74](https://github.com/Kapreon/kapreon-website/commit/d67fe74047a794f5637359cf6069c1d44a5852e0))
* modify arrow icon when deploying accordion ([f0b3a7d](https://github.com/Kapreon/kapreon-website/commit/f0b3a7dc1901e46f7964850c5ccedbc62a4bd06b))
* remove locomotive scroll individual script ([f2d3197](https://github.com/Kapreon/kapreon-website/commit/f2d319714f7ade46b3bc3a60646ce06c379db03e))
* remove Lomotive Scroll individual script ([a1a8583](https://github.com/Kapreon/kapreon-website/commit/a1a85830008839cf05b5e7096a5a8beed2615493))
* replace apostrophe ([583663f](https://github.com/Kapreon/kapreon-website/commit/583663f307454664396fc1ad7380b87477f5b40a))
* replace icon with material icon Google ([ebc1ac0](https://github.com/Kapreon/kapreon-website/commit/ebc1ac0a7e9b49b1d7c9972a58775fe1f8f6d57c))
* reverse original dockerfile ([cb445a8](https://github.com/Kapreon/kapreon-website/commit/cb445a814bd19a5bdd299789f63b9c7d35ee3ffe))
* revert npm install command change ([302bbbc](https://github.com/Kapreon/kapreon-website/commit/302bbbcddda2c658bcd6d37367ccfd1f85610971))
* temporary hide nav menu button ([74f4076](https://github.com/Kapreon/kapreon-website/commit/74f4076c6f163beb8075e05136b1b7b8193bbd16))
* test fix deploy workflow ([f582ca8](https://github.com/Kapreon/kapreon-website/commit/f582ca80e4386c4b198833c7362a112e4c324bdc))
* update cloudron package ([a42e665](https://github.com/Kapreon/kapreon-website/commit/a42e665f5954e04417fc41e27ee44ad362647bad))
* upgrade deploy workflow actions ([f2307ec](https://github.com/Kapreon/kapreon-website/commit/f2307ec1a982f053fb162552f341a179db246fea))


### Features

* add MDBank case study ([47816f2](https://github.com/Kapreon/kapreon-website/commit/47816f264ae17ab1f3f0f426d072b46490d2a4ac))
* add questions to FAQ ([c1cc94d](https://github.com/Kapreon/kapreon-website/commit/c1cc94d90bfd19896c7fc12154efd6f4a036bbf7))
* move Locomotive Scroll to a component ([992dd9a](https://github.com/Kapreon/kapreon-website/commit/992dd9ad80f36355c69627326bdd2baeef7425df))

## [1.7.2](https://github.com/Kapreon/kapreon-website/compare/v1.7.1...v1.7.2) (2025-03-04)


### Bug Fixes

* add missing env variable to skip husky ([9b283da](https://github.com/Kapreon/kapreon-website/commit/9b283da892192e8815759ec61f16915267a30e77))
* change build action output ([7ca4a9a](https://github.com/Kapreon/kapreon-website/commit/7ca4a9a6651b2c9c3aba265c7e1e25f0d8b391b3))
* change npm installation command in Cloudron's package ([ac072c1](https://github.com/Kapreon/kapreon-website/commit/ac072c1e8d9750f9f64232ba0be2f4bbf7642112))
* deploy workflow npm installation ([b314f6f](https://github.com/Kapreon/kapreon-website/commit/b314f6f35796073bc3f55cf01960861362715d67))
* force prepare script to never fail ([1ba944f](https://github.com/Kapreon/kapreon-website/commit/1ba944f4ce0d5cb26ce709c107d258e73c11225e))
* reverse original dockerfile ([cb445a8](https://github.com/Kapreon/kapreon-website/commit/cb445a814bd19a5bdd299789f63b9c7d35ee3ffe))
* revert npm install command change ([302bbbc](https://github.com/Kapreon/kapreon-website/commit/302bbbcddda2c658bcd6d37367ccfd1f85610971))
* test fix deploy workflow ([f582ca8](https://github.com/Kapreon/kapreon-website/commit/f582ca80e4386c4b198833c7362a112e4c324bdc))
* upgrade deploy workflow actions ([f2307ec](https://github.com/Kapreon/kapreon-website/commit/f2307ec1a982f053fb162552f341a179db246fea))

## [1.7.2](https://github.com/Kapreon/kapreon-website/compare/v1.7.1...v1.7.2) (2025-03-04)


### Bug Fixes

* add missing env variable to skip husky ([9b283da](https://github.com/Kapreon/kapreon-website/commit/9b283da892192e8815759ec61f16915267a30e77))
* change npm installation command in Cloudron's package ([ac072c1](https://github.com/Kapreon/kapreon-website/commit/ac072c1e8d9750f9f64232ba0be2f4bbf7642112))
* deploy workflow npm installation ([b314f6f](https://github.com/Kapreon/kapreon-website/commit/b314f6f35796073bc3f55cf01960861362715d67))
* force prepare script to never fail ([1ba944f](https://github.com/Kapreon/kapreon-website/commit/1ba944f4ce0d5cb26ce709c107d258e73c11225e))
* reverse original dockerfile ([cb445a8](https://github.com/Kapreon/kapreon-website/commit/cb445a814bd19a5bdd299789f63b9c7d35ee3ffe))
* revert npm install command change ([302bbbc](https://github.com/Kapreon/kapreon-website/commit/302bbbcddda2c658bcd6d37367ccfd1f85610971))
* test fix deploy workflow ([f582ca8](https://github.com/Kapreon/kapreon-website/commit/f582ca80e4386c4b198833c7362a112e4c324bdc))
* upgrade deploy workflow actions ([f2307ec](https://github.com/Kapreon/kapreon-website/commit/f2307ec1a982f053fb162552f341a179db246fea))

## [1.7.2](https://github.com/Kapreon/kapreon-website/compare/v1.7.1...v1.7.2) (2025-03-04)


### Bug Fixes

* add missing env variable to skip husky ([9b283da](https://github.com/Kapreon/kapreon-website/commit/9b283da892192e8815759ec61f16915267a30e77))
* change npm installation command in Cloudron's package ([ac072c1](https://github.com/Kapreon/kapreon-website/commit/ac072c1e8d9750f9f64232ba0be2f4bbf7642112))
* deploy workflow npm installation ([b314f6f](https://github.com/Kapreon/kapreon-website/commit/b314f6f35796073bc3f55cf01960861362715d67))
* force prepare script to never fail ([1ba944f](https://github.com/Kapreon/kapreon-website/commit/1ba944f4ce0d5cb26ce709c107d258e73c11225e))
* reverse original dockerfile ([cb445a8](https://github.com/Kapreon/kapreon-website/commit/cb445a814bd19a5bdd299789f63b9c7d35ee3ffe))
* revert npm install command change ([302bbbc](https://github.com/Kapreon/kapreon-website/commit/302bbbcddda2c658bcd6d37367ccfd1f85610971))
* test fix deploy workflow ([f582ca8](https://github.com/Kapreon/kapreon-website/commit/f582ca80e4386c4b198833c7362a112e4c324bdc))

## [1.7.2](https://github.com/Kapreon/kapreon-website/compare/v1.7.1...v1.7.2) (2025-03-04)


### Bug Fixes

* add missing env variable to skip husky ([9b283da](https://github.com/Kapreon/kapreon-website/commit/9b283da892192e8815759ec61f16915267a30e77))
* change npm installation command in Cloudron's package ([ac072c1](https://github.com/Kapreon/kapreon-website/commit/ac072c1e8d9750f9f64232ba0be2f4bbf7642112))
* deploy workflow npm installation ([b314f6f](https://github.com/Kapreon/kapreon-website/commit/b314f6f35796073bc3f55cf01960861362715d67))
* reverse original dockerfile ([cb445a8](https://github.com/Kapreon/kapreon-website/commit/cb445a814bd19a5bdd299789f63b9c7d35ee3ffe))
* revert npm install command change ([302bbbc](https://github.com/Kapreon/kapreon-website/commit/302bbbcddda2c658bcd6d37367ccfd1f85610971))
* test fix deploy workflow ([f582ca8](https://github.com/Kapreon/kapreon-website/commit/f582ca80e4386c4b198833c7362a112e4c324bdc))

## [1.7.2](https://github.com/Kapreon/kapreon-website/compare/v1.7.1...v1.7.2) (2025-03-04)


### Bug Fixes

* add missing env variable to skip husky ([9b283da](https://github.com/Kapreon/kapreon-website/commit/9b283da892192e8815759ec61f16915267a30e77))
* change npm installation command in Cloudron's package ([ac072c1](https://github.com/Kapreon/kapreon-website/commit/ac072c1e8d9750f9f64232ba0be2f4bbf7642112))
* deploy workflow npm installation ([b314f6f](https://github.com/Kapreon/kapreon-website/commit/b314f6f35796073bc3f55cf01960861362715d67))
* reverse original dockerfile ([cb445a8](https://github.com/Kapreon/kapreon-website/commit/cb445a814bd19a5bdd299789f63b9c7d35ee3ffe))
* test fix deploy workflow ([f582ca8](https://github.com/Kapreon/kapreon-website/commit/f582ca80e4386c4b198833c7362a112e4c324bdc))

## [1.7.2](https://github.com/Kapreon/kapreon-website/compare/v1.7.1...v1.7.2) (2025-03-04)


### Bug Fixes

* change npm installation command in Cloudron's package ([ac072c1](https://github.com/Kapreon/kapreon-website/commit/ac072c1e8d9750f9f64232ba0be2f4bbf7642112))
* deploy workflow npm installation ([b314f6f](https://github.com/Kapreon/kapreon-website/commit/b314f6f35796073bc3f55cf01960861362715d67))
* reverse original dockerfile ([cb445a8](https://github.com/Kapreon/kapreon-website/commit/cb445a814bd19a5bdd299789f63b9c7d35ee3ffe))
* test fix deploy workflow ([f582ca8](https://github.com/Kapreon/kapreon-website/commit/f582ca80e4386c4b198833c7362a112e4c324bdc))

## [1.7.2](https://github.com/Kapreon/kapreon-website/compare/v1.7.1...v1.7.2) (2025-03-04)


### Bug Fixes

* deploy workflow npm installation ([b314f6f](https://github.com/Kapreon/kapreon-website/commit/b314f6f35796073bc3f55cf01960861362715d67))
* reverse original dockerfile ([cb445a8](https://github.com/Kapreon/kapreon-website/commit/cb445a814bd19a5bdd299789f63b9c7d35ee3ffe))
* test fix deploy workflow ([f582ca8](https://github.com/Kapreon/kapreon-website/commit/f582ca80e4386c4b198833c7362a112e4c324bdc))

## [1.7.2](https://github.com/Kapreon/kapreon-website/compare/v1.7.1...v1.7.2) (2025-03-04)


### Bug Fixes

* reverse original dockerfile ([cb445a8](https://github.com/Kapreon/kapreon-website/commit/cb445a814bd19a5bdd299789f63b9c7d35ee3ffe))
* test fix deploy workflow ([f582ca8](https://github.com/Kapreon/kapreon-website/commit/f582ca80e4386c4b198833c7362a112e4c324bdc))

## [1.7.1](https://github.com/Kapreon/kapreon-website/compare/v1.7.0...v1.7.1) (2025-03-04)


### Bug Fixes

* test to fix deploy workflow ([9211b3f](https://github.com/Kapreon/kapreon-website/commit/9211b3fc623d5d83aecb8051dcaf334d274af43d))

# [1.7.0](https://github.com/Kapreon/kapreon-website/compare/v1.6.0...v1.7.0) (2025-03-04)


### Bug Fixes

* background color and bottom padding ([292e52a](https://github.com/Kapreon/kapreon-website/commit/292e52ae62037d1507c979404aef3e0c7102fda7))
* fix terms and conditions page margin ([11f46a3](https://github.com/Kapreon/kapreon-website/commit/11f46a34b96582965fae772f6caf77df5a63b673))


### Features

* redirect old website links to new one ([0370801](https://github.com/Kapreon/kapreon-website/commit/0370801841159af935878a38f28a768475318679))

# [1.6.0](https://github.com/Kapreon/kapreon-website/compare/v1.5.0...v1.6.0) (2025-02-20)


### Bug Fixes

* fix footer scroll detection and logo animation state ([7801293](https://github.com/Kapreon/kapreon-website/commit/78012937ce8393b8e27176200a880b2765f87958))


### Features

* add dynamic year copyright ([c3be458](https://github.com/Kapreon/kapreon-website/commit/c3be4588a9c685afcbd76bb7fa29b4796a64cd03))

# [1.5.0](https://github.com/Kapreon/kapreon-website/compare/v1.4.3...v1.5.0) (2025-02-19)


### Features

* add cookie consent banner ([42bab7f](https://github.com/Kapreon/kapreon-website/commit/42bab7f67eca42cff6e3c5e74feb0613a2965aca))

## [1.4.3](https://github.com/Kapreon/kapreon-website/compare/v1.4.2...v1.4.3) (2025-02-19)


### Bug Fixes

* detect and add fallback when Konvergo Bot doesn't work ([a37a94c](https://github.com/Kapreon/kapreon-website/commit/a37a94cedbc7b96ae1d8096420c2cbba3693d9a7))

## [1.4.2](https://github.com/Kapreon/kapreon-website/compare/v1.4.1...v1.4.2) (2025-02-18)


### Bug Fixes

* remove Husky on dockerfile while building prod ([5769f06](https://github.com/Kapreon/kapreon-website/commit/5769f065cb60439c3908036fc28df1a8e15fae93))

## [1.4.1](https://github.com/Kapreon/kapreon-website/compare/v1.4.0...v1.4.1) (2025-02-18)


### Bug Fixes

* deactivate Husky in production before npm install ([b5ee439](https://github.com/Kapreon/kapreon-website/commit/b5ee43964038eee149d2430d2c1ed4866a335431))

# [1.4.0](https://github.com/Kapreon/kapreon-website/compare/v1.3.0...v1.4.0) (2025-02-18)


### Bug Fixes

* move useTransform call to the top of the component ([6aabe37](https://github.com/Kapreon/kapreon-website/commit/6aabe378649bf7d59ffeb51aa543ecdb1f69b4d3))


### Features

* add mask wrap around images ([812465e](https://github.com/Kapreon/kapreon-website/commit/812465e0c0104550df304ad1a11de118e5060b7a))
* add next project section to project page ([55e5988](https://github.com/Kapreon/kapreon-website/commit/55e59882bcf7f5f4e0f269e9ca8278f577befa32))
* add next projet component to project page ([3f274d6](https://github.com/Kapreon/kapreon-website/commit/3f274d6edc3815a370c74e552ced09e49ab1252b))
* animate Kapreon logo based on scroll trigger ([037a05b](https://github.com/Kapreon/kapreon-website/commit/037a05b45d9f2905c5182449550c168707e8005f))

# [1.3.0](https://github.com/Kapreon/kapreon-website/compare/v1.2.0...v1.3.0) (2025-02-14)


### Bug Fixes

* change background color on main tag ([8235337](https://github.com/Kapreon/kapreon-website/commit/82353376741eca595370127b26d33a67b84c404d))
* fix text selection colors ([3be2881](https://github.com/Kapreon/kapreon-website/commit/3be2881e5be4e80d565bc82e92039f97fe0c2ea7))


### Features

* add Google Analytics Tag ([bf4252a](https://github.com/Kapreon/kapreon-website/commit/bf4252ad121c7138507cfb938c20cc849229f9d9))

# [1.2.0](https://github.com/Kapreon/kapreon-website/compare/v1.1.0...v1.2.0) (2025-02-13)


### Bug Fixes

* add registry uri to tag in build workflow ([6cbebf6](https://github.com/Kapreon/kapreon-website/commit/6cbebf6055cfcfbd228476a2e56568780beea65e))


### Features

* update build of application ([6b27aa5](https://github.com/Kapreon/kapreon-website/commit/6b27aa56a1c71b2912d7552882eb2c90c2039f51))

# [1.1.0](https://github.com/Kapreon/kapreon-website/compare/v1.0.0...v1.1.0) (2025-02-13)


### Features

* cloudron release workflow ([66cf80d](https://github.com/Kapreon/kapreon-website/commit/66cf80d286cda2a231da05f649eff6d608581189))

# 1.0.0 (2025-02-13)


### Features

* add automatic releases ([dbb1b8f](https://github.com/Kapreon/kapreon-website/commit/dbb1b8f1b3433eb5cdddb03e4aaa976bd39c873a))
