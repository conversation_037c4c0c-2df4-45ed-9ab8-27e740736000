name: Push release to production

on:
  push:
    tags: ["v*"]
  workflow_dispatch:

jobs:
  push:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Setup Cloudron CLI
        uses: actions/setup-node@v4
        with:
          node-version: 20
      - run: npm install
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to Docker Hub
        uses: docker/login-action@v3
        with:
          registry: cr.kapreon.org
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      - name: Build image
        uses: docker/build-push-action@v6
        with:
          build-args: |
            DEPLOY_KEY=${{ secrets.DEPLOY_KEY }}
          platforms: linux/amd64
          context: .
          push: true
          outputs: type=image,name=cr.kapreon.org/kapreon/website-cloudron,push-by-digest=true,name-canonical=true,push=true
          # tags: cr.kapreon.org/kapreon/website-cloudron:latest

      - name: Login to Cloudron CLI
        run: |
          npm install -g cloudron
          cloudron login ${{ secrets.CLOUDRON_URL }} --username ${{ secrets.CLOUDRON_USERNAME }} --password "${{ secrets.CLOUDRON_TOKEN }}"
      - name: Update Cloudron application
        run: cloudron update --image cr.kapreon.org/kapreon/website-cloudron:latest --app ${{ secrets.PRODUCTION_URL }}
