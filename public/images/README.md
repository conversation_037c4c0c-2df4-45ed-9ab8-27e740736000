# Converting images to WebP

This command finds all image files under a specified directory (e.g., `path/to/your/images`) that are not already in WebP format and converts them to WebP with 100% quality.

## Prerequisites

- [ImageMagick](https://imagemagick.org) installed (provides `mogrify`).
- Unix-like shell (bash, zsh).

## Usage

From your project root, replace `path/to/your/images` with your target folder and run:

```sh
find path/to/your/images -type f ! -iname '*.webp' -print0 \
  | xargs -0 mogrify -quality 100 -format webp
```

- `-type f ! -iname '*.webp'` selects files that do not end with `.webp`.
- `-print0` and `xargs -0` handle filenames with spaces or special characters.
- `mogrify -format webp` creates a `.webp` file alongside each original image.
- Adjust `-quality` (0–100) to change the compression level.

## Notes

- Original files remain unchanged; new `.webp` files appear next to source images.
- The command recurses into subdirectories automatically.
