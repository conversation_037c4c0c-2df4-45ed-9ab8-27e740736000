// lib/gtag.js
export const GA_TRACKING_ID = process.env.NEXT_PUBLIC_GOOGLE_ID

// enregistre un pageview
export const pageview = (url) => {
  if (!GA_TRACKING_ID || typeof window === 'undefined') return
  window.gtag?.('config', GA_TRACKING_ID, {
    page_path: url,
  })
}

// enregistre un événement custom
export const event = ({ action, category, label, value }) => {
  if (!GA_TRACKING_ID || typeof window === 'undefined') return
  window.gtag?.('event', action, {
    event_category: category,
    event_label: label,
    value,
  })
}