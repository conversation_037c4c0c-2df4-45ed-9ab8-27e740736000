{"name": "com.kapreon", "version": "1.23.2", "repository": {"type": "git", "url": "**************:Kapreon/kapreon-website.git"}, "private": true, "scripts": {"dev": "next dev", "dev:ip": "next dev -H 0.0.0.0", "build": "next build", "export": "next export", "start": "next start", "lint": "next lint", "prepare": "husky || true && git config --local core.editor cat"}, "dependencies": {"@emotion/is-prop-valid": "^1.3.1", "@heroui/accordion": "^2.2.8", "@next/mdx": "^15.3.2", "date-fns": "^4.1.0", "embla-carousel": "^8.6.0", "embla-carousel-react": "^8.6.0", "eslint": "8.45.0", "eslint-config-next": "13.4.10", "framer-motion": "^12.4.0", "gray-matter": "^4.0.3", "gsap": "^3.12.7", "lenis": "^1.1.20", "material-symbols": "^0.29.0", "next": "13.4.10", "next-compose-plugins": "^2.2.1", "react": "18.2.0", "react-dom": "18.2.0", "react-icons": "^5.4.0", "react-masonry-css": "^1.0.16", "rehype-prism-plus": "^2.0.1", "remark-gfm": "^4.0.1", "sass": "^1.64.0", "sharp": "^0.34.1", "split-type": "^0.3.4"}, "devDependencies": {"@semantic-release/changelog": "^6.0.3", "@semantic-release/git": "^10.0.1", "@semantic-release/github": "^10.0.7", "@semantic-release/npm": "^12.0.1", "cloudron": "5.14.0", "commitizen": "^4.3.0", "contentlayer": "^0.3.4", "cz-conventional-changelog": "^3.3.0", "husky": "^9.0.11", "next-contentlayer": "^0.3.4", "semantic-release": "^24.0.0"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}}