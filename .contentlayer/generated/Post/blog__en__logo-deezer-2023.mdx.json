{"title": "Deezer unveils its new logo in 2023", "excerpt": "The year 2023 marks a new era for Deezer, the music streaming giant. With its bold rebranding embodied by a logo featuring a vibrant heart.", "date": "2023-11-15T00:00:00.000Z", "updated": "2023-11-15T00:00:00.000Z", "category": "Brand identity", "cover": "/images/blog/logo-deezer-2023/main-logo-deezer-2023.png", "published": true, "body": {"raw": "\nThis new purple heart splits opinion, but the community-centered approach aligns perfectly with <PERSON><PERSON>’s promise. We explain why—and how the logo could earn its place.\n\n## Why change?\nIn 2023, <PERSON><PERSON> was looking for two things:\n1. Clarify its mission to become “the home of music” rather than just another streaming service.\n2. Differentiate itself in a saturated market (Spotify and Apple Music) with a strong, easily adaptable symbol.\n\nSo, the redesign is less about aesthetics and more about repositioning.\n\n<video\n  src=\"/images/blog/logo-deezer-2023/affiches-animees-deezer.webm\"\n  playsInline\n  autoPlay\n  muted\n  loop\n  style={{ width: '100%' }}\n/>\n\n## A pulsing heart\n\nDee<PERSON>’s new symbol, an animated purple heart, literally embodies their message: “Feel the music.”\n\nThe animation syncs to the tempo of the current track and immerses the user in the platform’s universe. Visually, this heart is instantly memorable and contrasts with the dominant hues used by other streaming services, like Spotify’s green.\n\n<video\n  src=\"/images/blog/logo-deezer-2023/animation-logo-deezer.webm\"\n  playsInline\n  autoPlay\n  muted\n  loop\n  style={{ width: '100%' }}\n/>\n\nTo accompany this icon, <PERSON><PERSON> introduces a **custom typeface**: <PERSON><PERSON>.\n\nIts curves mirror the logo’s lines—narrow enough for dense interfaces, wide enough to support a strong message on a poster.\n\n<Image\n    src='/images/blog/logo-deezer-2023/affiches-deezer.jpg'\n    alt=\"Posters of the new <PERSON>zer logo\"\n/>\n\n<video\n  src=\"/images/blog/logo-deezer-2023/animation-video-deezer.webm\"\n  playsInline\n  autoPlay\n  muted\n  loop\n  style={{ width: '100%' }}\n/>\n\n## An identity to attract a younger audience\n\nAmong creatives, the risk-taking is appealing: abandoning the old equalizer logo for an animated heart was not an obvious choice. In forums, some applaud the boldness; others, more skeptical, find the visual too close to **dating app conventions**.\n\nSubscribers are divided: many appreciate the warmer approach, while nostalgic fans miss the old logo.\n\nIn terms of memorability, the wager pays off: the icon works as well on a smartwatch as on a city billboard. The remaining challenge is maintaining recognition without animation—on print, favicons, or merchandise—by relying on a coherent visual universe: waves, purple halo, micro-motions.\n\n<Image\n    src='/images/blog/logo-deezer-2023/illustrations-deezer.webp'\n    alt=\"Illustrations of Deezer’s visual identity\"\n/>", "code": "var Component=(()=>{var sr=Object.create;var F=Object.defineProperty;var ur=Object.getOwnPropertyDescriptor;var cr=Object.getOwnPropertyNames;var fr=Object.getPrototypeOf,dr=Object.prototype.hasOwnProperty;var q=(u,a)=>()=>(a||u((a={exports:{}}).exports,a),a.exports),mr=(u,a)=>{for(var m in a)F(u,m,{get:a[m],enumerable:!0})},we=(u,a,m,y)=>{if(a&&typeof a==\"object\"||typeof a==\"function\")for(let _ of cr(a))!dr.call(u,_)&&_!==m&&F(u,_,{get:()=>a[_],enumerable:!(y=ur(a,_))||y.enumerable});return u};var br=(u,a,m)=>(m=u!=null?sr(fr(u)):{},we(a||!u||!u.__esModule?F(m,\"default\",{value:u,enumerable:!0}):m,u)),pr=u=>we(F({},\"__esModule\",{value:!0}),u);var xe=q((Er,Ee)=>{Ee.exports=React});var Ne=q(G=>{\"use strict\";(function(){\"use strict\";var u=xe(),a=Symbol.for(\"react.element\"),m=Symbol.for(\"react.portal\"),y=Symbol.for(\"react.fragment\"),_=Symbol.for(\"react.strict_mode\"),X=Symbol.for(\"react.profiler\"),K=Symbol.for(\"react.provider\"),H=Symbol.for(\"react.context\"),T=Symbol.for(\"react.forward_ref\"),A=Symbol.for(\"react.suspense\"),I=Symbol.for(\"react.suspense_list\"),C=Symbol.for(\"react.memo\"),z=Symbol.for(\"react.lazy\"),Ce=Symbol.for(\"react.offscreen\"),J=Symbol.iterator,Pe=\"@@iterator\";function Se(e){if(e===null||typeof e!=\"object\")return null;var r=J&&e[J]||e[Pe];return typeof r==\"function\"?r:null}var E=u.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;function b(e){{for(var r=arguments.length,n=new Array(r>1?r-1:0),t=1;t<r;t++)n[t-1]=arguments[t];Oe(\"error\",e,n)}}function Oe(e,r,n){{var t=E.ReactDebugCurrentFrame,l=t.getStackAddendum();l!==\"\"&&(r+=\"%s\",n=n.concat([l]));var c=n.map(function(o){return String(o)});c.unshift(\"Warning: \"+r),Function.prototype.apply.call(console[e],console,c)}}var je=!1,De=!1,Fe=!1,Ae=!1,Ie=!1,Z;Z=Symbol.for(\"react.module.reference\");function ze(e){return!!(typeof e==\"string\"||typeof e==\"function\"||e===y||e===X||Ie||e===_||e===A||e===I||Ae||e===Ce||je||De||Fe||typeof e==\"object\"&&e!==null&&(e.$$typeof===z||e.$$typeof===C||e.$$typeof===K||e.$$typeof===H||e.$$typeof===T||e.$$typeof===Z||e.getModuleId!==void 0))}function We(e,r,n){var t=e.displayName;if(t)return t;var l=r.displayName||r.name||\"\";return l!==\"\"?n+\"(\"+l+\")\":n}function Q(e){return e.displayName||\"Context\"}function g(e){if(e==null)return null;if(typeof e.tag==\"number\"&&b(\"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"),typeof e==\"function\")return e.displayName||e.name||null;if(typeof e==\"string\")return e;switch(e){case y:return\"Fragment\";case m:return\"Portal\";case X:return\"Profiler\";case _:return\"StrictMode\";case A:return\"Suspense\";case I:return\"SuspenseList\"}if(typeof e==\"object\")switch(e.$$typeof){case H:var r=e;return Q(r)+\".Consumer\";case K:var n=e;return Q(n._context)+\".Provider\";case T:return We(e,e.render,\"ForwardRef\");case C:var t=e.displayName||null;return t!==null?t:g(e.type)||\"Memo\";case z:{var l=e,c=l._payload,o=l._init;try{return g(o(c))}catch{return null}}}return null}var w=Object.assign,R=0,ee,re,ne,te,ae,ie,oe;function le(){}le.__reactDisabledLog=!0;function Ye(){{if(R===0){ee=console.log,re=console.info,ne=console.warn,te=console.error,ae=console.group,ie=console.groupCollapsed,oe=console.groupEnd;var e={configurable:!0,enumerable:!0,value:le,writable:!0};Object.defineProperties(console,{info:e,log:e,warn:e,error:e,group:e,groupCollapsed:e,groupEnd:e})}R++}}function $e(){{if(R--,R===0){var e={configurable:!0,enumerable:!0,writable:!0};Object.defineProperties(console,{log:w({},e,{value:ee}),info:w({},e,{value:re}),warn:w({},e,{value:ne}),error:w({},e,{value:te}),group:w({},e,{value:ae}),groupCollapsed:w({},e,{value:ie}),groupEnd:w({},e,{value:oe})})}R<0&&b(\"disabledDepth fell below zero. This is a bug in React. Please file an issue.\")}}var W=E.ReactCurrentDispatcher,Y;function P(e,r,n){{if(Y===void 0)try{throw Error()}catch(l){var t=l.stack.trim().match(/\\n( *(at )?)/);Y=t&&t[1]||\"\"}return`\n`+Y+e}}var $=!1,S;{var Me=typeof WeakMap==\"function\"?WeakMap:Map;S=new Me}function se(e,r){if(!e||$)return\"\";{var n=S.get(e);if(n!==void 0)return n}var t;$=!0;var l=Error.prepareStackTrace;Error.prepareStackTrace=void 0;var c;c=W.current,W.current=null,Ye();try{if(r){var o=function(){throw Error()};if(Object.defineProperty(o.prototype,\"props\",{set:function(){throw Error()}}),typeof Reflect==\"object\"&&Reflect.construct){try{Reflect.construct(o,[])}catch(v){t=v}Reflect.construct(e,[],o)}else{try{o.call()}catch(v){t=v}e.call(o.prototype)}}else{try{throw Error()}catch(v){t=v}e()}}catch(v){if(v&&t&&typeof v.stack==\"string\"){for(var i=v.stack.split(`\n`),p=t.stack.split(`\n`),f=i.length-1,d=p.length-1;f>=1&&d>=0&&i[f]!==p[d];)d--;for(;f>=1&&d>=0;f--,d--)if(i[f]!==p[d]){if(f!==1||d!==1)do if(f--,d--,d<0||i[f]!==p[d]){var h=`\n`+i[f].replace(\" at new \",\" at \");return e.displayName&&h.includes(\"<anonymous>\")&&(h=h.replace(\"<anonymous>\",e.displayName)),typeof e==\"function\"&&S.set(e,h),h}while(f>=1&&d>=0);break}}}finally{$=!1,W.current=c,$e(),Error.prepareStackTrace=l}var N=e?e.displayName||e.name:\"\",ye=N?P(N):\"\";return typeof e==\"function\"&&S.set(e,ye),ye}function Ve(e,r,n){return se(e,!1)}function Le(e){var r=e.prototype;return!!(r&&r.isReactComponent)}function O(e,r,n){if(e==null)return\"\";if(typeof e==\"function\")return se(e,Le(e));if(typeof e==\"string\")return P(e);switch(e){case A:return P(\"Suspense\");case I:return P(\"SuspenseList\")}if(typeof e==\"object\")switch(e.$$typeof){case T:return Ve(e.render);case C:return O(e.type,r,n);case z:{var t=e,l=t._payload,c=t._init;try{return O(c(l),r,n)}catch{}}}return\"\"}var j=Object.prototype.hasOwnProperty,ue={},ce=E.ReactDebugCurrentFrame;function D(e){if(e){var r=e._owner,n=O(e.type,e._source,r?r.type:null);ce.setExtraStackFrame(n)}else ce.setExtraStackFrame(null)}function Ue(e,r,n,t,l){{var c=Function.call.bind(j);for(var o in e)if(c(e,o)){var i=void 0;try{if(typeof e[o]!=\"function\"){var p=Error((t||\"React class\")+\": \"+n+\" type `\"+o+\"` is invalid; it must be a function, usually from the `prop-types` package, but received `\"+typeof e[o]+\"`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.\");throw p.name=\"Invariant Violation\",p}i=e[o](r,o,t,n,null,\"SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED\")}catch(f){i=f}i&&!(i instanceof Error)&&(D(l),b(\"%s: type specification of %s `%s` is invalid; the type checker function must return `null` or an `Error` but returned a %s. You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument).\",t||\"React class\",n,o,typeof i),D(null)),i instanceof Error&&!(i.message in ue)&&(ue[i.message]=!0,D(l),b(\"Failed %s type: %s\",n,i.message),D(null))}}}var Be=Array.isArray;function M(e){return Be(e)}function qe(e){{var r=typeof Symbol==\"function\"&&Symbol.toStringTag,n=r&&e[Symbol.toStringTag]||e.constructor.name||\"Object\";return n}}function Ge(e){try{return fe(e),!1}catch{return!0}}function fe(e){return\"\"+e}function de(e){if(Ge(e))return b(\"The provided key is an unsupported type %s. This value must be coerced to a string before before using it here.\",qe(e)),fe(e)}var k=E.ReactCurrentOwner,Xe={key:!0,ref:!0,__self:!0,__source:!0},me,be,V;V={};function Ke(e){if(j.call(e,\"ref\")){var r=Object.getOwnPropertyDescriptor(e,\"ref\").get;if(r&&r.isReactWarning)return!1}return e.ref!==void 0}function He(e){if(j.call(e,\"key\")){var r=Object.getOwnPropertyDescriptor(e,\"key\").get;if(r&&r.isReactWarning)return!1}return e.key!==void 0}function Je(e,r){if(typeof e.ref==\"string\"&&k.current&&r&&k.current.stateNode!==r){var n=g(k.current.type);V[n]||(b('Component \"%s\" contains the string ref \"%s\". Support for string refs will be removed in a future major release. This case cannot be automatically converted to an arrow function. We ask you to manually fix this case by using useRef() or createRef() instead. Learn more about using refs safely here: https://reactjs.org/link/strict-mode-string-ref',g(k.current.type),e.ref),V[n]=!0)}}function Ze(e,r){{var n=function(){me||(me=!0,b(\"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)\",r))};n.isReactWarning=!0,Object.defineProperty(e,\"key\",{get:n,configurable:!0})}}function Qe(e,r){{var n=function(){be||(be=!0,b(\"%s: `ref` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)\",r))};n.isReactWarning=!0,Object.defineProperty(e,\"ref\",{get:n,configurable:!0})}}var er=function(e,r,n,t,l,c,o){var i={$$typeof:a,type:e,key:r,ref:n,props:o,_owner:c};return i._store={},Object.defineProperty(i._store,\"validated\",{configurable:!1,enumerable:!1,writable:!0,value:!1}),Object.defineProperty(i,\"_self\",{configurable:!1,enumerable:!1,writable:!1,value:t}),Object.defineProperty(i,\"_source\",{configurable:!1,enumerable:!1,writable:!1,value:l}),Object.freeze&&(Object.freeze(i.props),Object.freeze(i)),i};function rr(e,r,n,t,l){{var c,o={},i=null,p=null;n!==void 0&&(de(n),i=\"\"+n),He(r)&&(de(r.key),i=\"\"+r.key),Ke(r)&&(p=r.ref,Je(r,l));for(c in r)j.call(r,c)&&!Xe.hasOwnProperty(c)&&(o[c]=r[c]);if(e&&e.defaultProps){var f=e.defaultProps;for(c in f)o[c]===void 0&&(o[c]=f[c])}if(i||p){var d=typeof e==\"function\"?e.displayName||e.name||\"Unknown\":e;i&&Ze(o,d),p&&Qe(o,d)}return er(e,i,p,l,t,k.current,o)}}var L=E.ReactCurrentOwner,pe=E.ReactDebugCurrentFrame;function x(e){if(e){var r=e._owner,n=O(e.type,e._source,r?r.type:null);pe.setExtraStackFrame(n)}else pe.setExtraStackFrame(null)}var U;U=!1;function B(e){return typeof e==\"object\"&&e!==null&&e.$$typeof===a}function he(){{if(L.current){var e=g(L.current.type);if(e)return`\n\nCheck the render method of \\``+e+\"`.\"}return\"\"}}function nr(e){{if(e!==void 0){var r=e.fileName.replace(/^.*[\\\\\\/]/,\"\"),n=e.lineNumber;return`\n\nCheck your code at `+r+\":\"+n+\".\"}return\"\"}}var ge={};function tr(e){{var r=he();if(!r){var n=typeof e==\"string\"?e:e.displayName||e.name;n&&(r=`\n\nCheck the top-level render call using <`+n+\">.\")}return r}}function ve(e,r){{if(!e._store||e._store.validated||e.key!=null)return;e._store.validated=!0;var n=tr(r);if(ge[n])return;ge[n]=!0;var t=\"\";e&&e._owner&&e._owner!==L.current&&(t=\" It was passed a child from \"+g(e._owner.type)+\".\"),x(e),b('Each child in a list should have a unique \"key\" prop.%s%s See https://reactjs.org/link/warning-keys for more information.',n,t),x(null)}}function _e(e,r){{if(typeof e!=\"object\")return;if(M(e))for(var n=0;n<e.length;n++){var t=e[n];B(t)&&ve(t,r)}else if(B(e))e._store&&(e._store.validated=!0);else if(e){var l=Se(e);if(typeof l==\"function\"&&l!==e.entries)for(var c=l.call(e),o;!(o=c.next()).done;)B(o.value)&&ve(o.value,r)}}}function ar(e){{var r=e.type;if(r==null||typeof r==\"string\")return;var n;if(typeof r==\"function\")n=r.propTypes;else if(typeof r==\"object\"&&(r.$$typeof===T||r.$$typeof===C))n=r.propTypes;else return;if(n){var t=g(r);Ue(n,e.props,\"prop\",t,e)}else if(r.PropTypes!==void 0&&!U){U=!0;var l=g(r);b(\"Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?\",l||\"Unknown\")}typeof r.getDefaultProps==\"function\"&&!r.getDefaultProps.isReactClassApproved&&b(\"getDefaultProps is only used on classic React.createClass definitions. Use a static property named `defaultProps` instead.\")}}function ir(e){{for(var r=Object.keys(e.props),n=0;n<r.length;n++){var t=r[n];if(t!==\"children\"&&t!==\"key\"){x(e),b(\"Invalid prop `%s` supplied to `React.Fragment`. React.Fragment can only have `key` and `children` props.\",t),x(null);break}}e.ref!==null&&(x(e),b(\"Invalid attribute `ref` supplied to `React.Fragment`.\"),x(null))}}function or(e,r,n,t,l,c){{var o=ze(e);if(!o){var i=\"\";(e===void 0||typeof e==\"object\"&&e!==null&&Object.keys(e).length===0)&&(i+=\" You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.\");var p=nr(l);p?i+=p:i+=he();var f;e===null?f=\"null\":M(e)?f=\"array\":e!==void 0&&e.$$typeof===a?(f=\"<\"+(g(e.type)||\"Unknown\")+\" />\",i=\" Did you accidentally export a JSX literal instead of a component?\"):f=typeof e,b(\"React.jsx: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: %s.%s\",f,i)}var d=rr(e,r,n,l,c);if(d==null)return d;if(o){var h=r.children;if(h!==void 0)if(t)if(M(h)){for(var N=0;N<h.length;N++)_e(h[N],e);Object.freeze&&Object.freeze(h)}else b(\"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\");else _e(h,e)}return e===y?ir(d):ar(d),d}}var lr=or;G.Fragment=y,G.jsxDEV=lr})()});var ke=q((Nr,Re)=>{\"use strict\";Re.exports=Ne()});var yr={};mr(yr,{default:()=>vr,frontmatter:()=>hr});var s=br(ke()),hr={title:\"Deezer unveils its new logo in 2023\",excerpt:\"The year 2023 marks a new era for Deezer, the music streaming giant. With its bold rebranding embodied by a logo featuring a vibrant heart.\",date:\"2023-11-15\",updated:\"2023-11-15\",category:\"Brand identity\",cover:\"/images/blog/logo-deezer-2023/main-logo-deezer-2023.png\"};function Te(u){let a=Object.assign({p:\"p\",h2:\"h2\",ol:\"ol\",li:\"li\",strong:\"strong\"},u.components),{Image:m}=a;return m||_r(\"Image\",!0,\"47:1-50:3\"),(0,s.jsxDEV)(s.Fragment,{children:[(0,s.jsxDEV)(a.p,{children:\"This new purple heart splits opinion, but the community-centered approach aligns perfectly with Deezer\\u2019s promise. We explain why\\u2014and how the logo could earn its place.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-6a89e259-7d15-456f-b839-389f800a6589.mdx\",lineNumber:10,columnNumber:1},this),`\n`,(0,s.jsxDEV)(a.h2,{children:\"Why change?\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-6a89e259-7d15-456f-b839-389f800a6589.mdx\",lineNumber:12,columnNumber:1},this),`\n`,(0,s.jsxDEV)(a.p,{children:\"In 2023, Deezer was looking for two things:\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-6a89e259-7d15-456f-b839-389f800a6589.mdx\",lineNumber:13,columnNumber:1},this),`\n`,(0,s.jsxDEV)(a.ol,{children:[`\n`,(0,s.jsxDEV)(a.li,{children:\"Clarify its mission to become \\u201Cthe home of music\\u201D rather than just another streaming service.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-6a89e259-7d15-456f-b839-389f800a6589.mdx\",lineNumber:14,columnNumber:1},this),`\n`,(0,s.jsxDEV)(a.li,{children:\"Differentiate itself in a saturated market (Spotify and Apple Music) with a strong, easily adaptable symbol.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-6a89e259-7d15-456f-b839-389f800a6589.mdx\",lineNumber:15,columnNumber:1},this),`\n`]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-6a89e259-7d15-456f-b839-389f800a6589.mdx\",lineNumber:14,columnNumber:1},this),`\n`,(0,s.jsxDEV)(a.p,{children:\"So, the redesign is less about aesthetics and more about repositioning.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-6a89e259-7d15-456f-b839-389f800a6589.mdx\",lineNumber:17,columnNumber:1},this),`\n`,(0,s.jsxDEV)(\"video\",{src:\"/images/blog/logo-deezer-2023/affiches-animees-deezer.webm\",playsInline:!0,autoPlay:!0,muted:!0,loop:!0,style:{width:\"100%\"}},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-6a89e259-7d15-456f-b839-389f800a6589.mdx\",lineNumber:19,columnNumber:1},this),`\n`,(0,s.jsxDEV)(a.h2,{children:\"A pulsing heart\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-6a89e259-7d15-456f-b839-389f800a6589.mdx\",lineNumber:28,columnNumber:1},this),`\n`,(0,s.jsxDEV)(a.p,{children:\"Deezer\\u2019s new symbol, an animated purple heart, literally embodies their message: \\u201CFeel the music.\\u201D\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-6a89e259-7d15-456f-b839-389f800a6589.mdx\",lineNumber:30,columnNumber:1},this),`\n`,(0,s.jsxDEV)(a.p,{children:\"The animation syncs to the tempo of the current track and immerses the user in the platform\\u2019s universe. Visually, this heart is instantly memorable and contrasts with the dominant hues used by other streaming services, like Spotify\\u2019s green.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-6a89e259-7d15-456f-b839-389f800a6589.mdx\",lineNumber:32,columnNumber:1},this),`\n`,(0,s.jsxDEV)(\"video\",{src:\"/images/blog/logo-deezer-2023/animation-logo-deezer.webm\",playsInline:!0,autoPlay:!0,muted:!0,loop:!0,style:{width:\"100%\"}},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-6a89e259-7d15-456f-b839-389f800a6589.mdx\",lineNumber:34,columnNumber:1},this),`\n`,(0,s.jsxDEV)(a.p,{children:[\"To accompany this icon, Deezer introduces a \",(0,s.jsxDEV)(a.strong,{children:\"custom typeface\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-6a89e259-7d15-456f-b839-389f800a6589.mdx\",lineNumber:43,columnNumber:45},this),\": Deezer Sans.\"]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-6a89e259-7d15-456f-b839-389f800a6589.mdx\",lineNumber:43,columnNumber:1},this),`\n`,(0,s.jsxDEV)(a.p,{children:\"Its curves mirror the logo\\u2019s lines\\u2014narrow enough for dense interfaces, wide enough to support a strong message on a poster.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-6a89e259-7d15-456f-b839-389f800a6589.mdx\",lineNumber:45,columnNumber:1},this),`\n`,(0,s.jsxDEV)(m,{src:\"/images/blog/logo-deezer-2023/affiches-deezer.jpg\",alt:\"Posters of the new Deezer logo\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-6a89e259-7d15-456f-b839-389f800a6589.mdx\",lineNumber:47,columnNumber:1},this),`\n`,(0,s.jsxDEV)(\"video\",{src:\"/images/blog/logo-deezer-2023/animation-video-deezer.webm\",playsInline:!0,autoPlay:!0,muted:!0,loop:!0,style:{width:\"100%\"}},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-6a89e259-7d15-456f-b839-389f800a6589.mdx\",lineNumber:52,columnNumber:1},this),`\n`,(0,s.jsxDEV)(a.h2,{children:\"An identity to attract a younger audience\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-6a89e259-7d15-456f-b839-389f800a6589.mdx\",lineNumber:61,columnNumber:1},this),`\n`,(0,s.jsxDEV)(a.p,{children:[\"Among creatives, the risk-taking is appealing: abandoning the old equalizer logo for an animated heart was not an obvious choice. In forums, some applaud the boldness; others, more skeptical, find the visual too close to \",(0,s.jsxDEV)(a.strong,{children:\"dating app conventions\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-6a89e259-7d15-456f-b839-389f800a6589.mdx\",lineNumber:63,columnNumber:222},this),\".\"]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-6a89e259-7d15-456f-b839-389f800a6589.mdx\",lineNumber:63,columnNumber:1},this),`\n`,(0,s.jsxDEV)(a.p,{children:\"Subscribers are divided: many appreciate the warmer approach, while nostalgic fans miss the old logo.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-6a89e259-7d15-456f-b839-389f800a6589.mdx\",lineNumber:65,columnNumber:1},this),`\n`,(0,s.jsxDEV)(a.p,{children:\"In terms of memorability, the wager pays off: the icon works as well on a smartwatch as on a city billboard. The remaining challenge is maintaining recognition without animation\\u2014on print, favicons, or merchandise\\u2014by relying on a coherent visual universe: waves, purple halo, micro-motions.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-6a89e259-7d15-456f-b839-389f800a6589.mdx\",lineNumber:67,columnNumber:1},this),`\n`,(0,s.jsxDEV)(m,{src:\"/images/blog/logo-deezer-2023/illustrations-deezer.webp\",alt:\"Illustrations of Deezer\\u2019s visual identity\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-6a89e259-7d15-456f-b839-389f800a6589.mdx\",lineNumber:69,columnNumber:1},this)]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-6a89e259-7d15-456f-b839-389f800a6589.mdx\",lineNumber:1,columnNumber:1},this)}function gr(u={}){let{wrapper:a}=u.components||{};return a?(0,s.jsxDEV)(a,Object.assign({},u,{children:(0,s.jsxDEV)(Te,u,void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-6a89e259-7d15-456f-b839-389f800a6589.mdx\"},this)}),void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-6a89e259-7d15-456f-b839-389f800a6589.mdx\"},this):Te(u)}var vr=gr;function _r(u,a,m){throw new Error(\"Expected \"+(a?\"component\":\"object\")+\" `\"+u+\"` to be defined: you likely forgot to import, pass, or provide it.\"+(m?\"\\nIt\\u2019s referenced in your code at `\"+m+\"` in `/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-6a89e259-7d15-456f-b839-389f800a6589.mdx`\":\"\"))}return pr(yr);})();\n/*! Bundled license information:\n\nreact/cjs/react-jsx-dev-runtime.development.js:\n  (**\n   * @license React\n   * react-jsx-dev-runtime.development.js\n   *\n   * Copyright (c) Facebook, Inc. and its affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *)\n*/\n;return Component;"}, "_id": "blog/en/logo-deezer-2023.mdx", "_raw": {"sourceFilePath": "blog/en/logo-deezer-2023.mdx", "sourceFileName": "logo-deezer-2023.mdx", "sourceFileDir": "blog/en", "contentType": "mdx", "flattenedPath": "blog/en/logo-deezer-2023"}, "type": "Post", "locale": "en", "slug": "logo-deezer-2023"}