{"title": "Deezer dévoile son nouveau logo en 2023", "excerpt": "L’année 2023 marque une ère nouvelle pour Deezer, le géant du streaming musical. Avec son audacieux rebranding incarné par un logo au cœur vibrant.", "date": "2023-11-15T00:00:00.000Z", "updated": "2023-11-15T00:00:00.000Z", "category": "Image de marque", "cover": "/images/blog/logo-deezer-2023/main-logo-deezer-2023.png", "published": true, "body": {"raw": "\nCe nouveau cœur violet divise, mais l’approche centrée-communauté colle bien à la promesse de <PERSON><PERSON>. On vous explique pourquoi—et comment le logo pourrait gagner ses galons.\n\n## Pourquoi changer ?\nEn 2023, Deezer cherchait deux choses :\n1. Clarifier sa mission pour devenir « la maison de la musique » plutôt qu’un simple service de streaming.\n2. Se différencier d’un marché saturé (Spotify et Apple Music) grâce à un symbole fort et facile à décliner.\n\nLa refonte s’inscrit donc moins dans un mouvement esthétique que dans une redéfinition de positionnement.\n\n<video\n  src=\"/images/blog/logo-deezer-2023/affiches-animees-deezer.webm\"\n  playsInline\n  autoPlay\n  muted\n  loop\n  style={{ width: '100%' }}\n/>\n\n## Un cœur qui pulse\n\nLe nouveau symbole de Deezer, un cœur violet animé, incarne littéralement leur message : « Feel the music ».\n\nL’animation se cale sur le tempo du morceau en cours et plonge l’utilisateur dans l’univers de la plateforme. Visuellement, ce cœur se mémorise d’un coup d’œil et tranche avec les teintes dominantes chez les autres services de streaming, comme pour le vert chez Spotify.\n\n<video\n  src=\"/images/blog/logo-deezer-2023/animation-logo-deezer.webm\"\n  playsInline\n  autoPlay\n  muted\n  loop\n  style={{ width: '100%' }}\n/>\n\nPour accompagner ce pictogramme, Deezer incarne une **nouvelle police** sur mesure : Deezer Sans. \n\nLes courbes reprennent les lignes du logo : suffisamment étroites pour les interfaces denses, assez larges pour soutenir un message fort sur une affiche.\n\n<Image\n    src='/images/blog/logo-deezer-2023/affiches-deezer.jpg'\n    alt=\"Affiches du nouveau logo Deezer\"\n/>\n\n<video\n  src=\"/images/blog/logo-deezer-2023/animation-video-deezer.webm\"\n  playsInline\n  autoPlay\n  muted\n  loop\n  style={{ width: '100%' }}\n/>\n\n## Une identité pour séduire un public plus jeune\n\nDu côté des créatifs, la prise de risque séduit : délaisser l'ancien logo avec l’égaliseur pour un cœur animé n’allait pas de soi. Dans les forums, certains applaudissent l’audace ; d’autres, plus sceptiques, trouvent le visuel trop proche des **codes d’une appli de rencontre**.\n\nLes abonnés sont partagés : beaucoup apprécient l’approche plus chaleureuse, tandis que les nostalgiques regrettent l’ancien logo.\n\nSur le plan de la mémorisation, le pari est gagnant : l’icône fonctionne aussi bien sur une montre connectée que sur un panneau d’affichage urbain. Le défi reste de préserver la reconnaissance hors animation — sur imprimé, favicon ou merchandise — en s’appuyant sur un univers visuel cohérent : ondes, halo violet, micro‑motions.\n\n<Image\n    src='/images/blog/logo-deezer-2023/illustrations-deezer.webp'\n    alt=\"Illustrations de l'identité visuelle de Deezer\"\n/>", "code": "var Component=(()=>{var lr=Object.create;var F=Object.defineProperty;var sr=Object.getOwnPropertyDescriptor;var cr=Object.getOwnPropertyNames;var fr=Object.getPrototypeOf,dr=Object.prototype.hasOwnProperty;var B=(s,a)=>()=>(a||s((a={exports:{}}).exports,a),a.exports),mr=(s,a)=>{for(var m in a)F(s,m,{get:a[m],enumerable:!0})},Ee=(s,a,m,y)=>{if(a&&typeof a==\"object\"||typeof a==\"function\")for(let _ of cr(a))!dr.call(s,_)&&_!==m&&F(s,_,{get:()=>a[_],enumerable:!(y=sr(a,_))||y.enumerable});return s};var br=(s,a,m)=>(m=s!=null?lr(fr(s)):{},Ee(a||!s||!s.__esModule?F(m,\"default\",{value:s,enumerable:!0}):m,s)),pr=s=>Ee(F({},\"__esModule\",{value:!0}),s);var we=B((xr,xe)=>{xe.exports=React});var Ne=B(G=>{\"use strict\";(function(){\"use strict\";var s=we(),a=Symbol.for(\"react.element\"),m=Symbol.for(\"react.portal\"),y=Symbol.for(\"react.fragment\"),_=Symbol.for(\"react.strict_mode\"),X=Symbol.for(\"react.profiler\"),K=Symbol.for(\"react.provider\"),H=Symbol.for(\"react.context\"),k=Symbol.for(\"react.forward_ref\"),A=Symbol.for(\"react.suspense\"),I=Symbol.for(\"react.suspense_list\"),C=Symbol.for(\"react.memo\"),z=Symbol.for(\"react.lazy\"),Ce=Symbol.for(\"react.offscreen\"),J=Symbol.iterator,Pe=\"@@iterator\";function Oe(e){if(e===null||typeof e!=\"object\")return null;var r=J&&e[J]||e[Pe];return typeof r==\"function\"?r:null}var x=s.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;function b(e){{for(var r=arguments.length,n=new Array(r>1?r-1:0),t=1;t<r;t++)n[t-1]=arguments[t];Se(\"error\",e,n)}}function Se(e,r,n){{var t=x.ReactDebugCurrentFrame,u=t.getStackAddendum();u!==\"\"&&(r+=\"%s\",n=n.concat([u]));var c=n.map(function(o){return String(o)});c.unshift(\"Warning: \"+r),Function.prototype.apply.call(console[e],console,c)}}var je=!1,De=!1,Fe=!1,Ae=!1,Ie=!1,Z;Z=Symbol.for(\"react.module.reference\");function ze(e){return!!(typeof e==\"string\"||typeof e==\"function\"||e===y||e===X||Ie||e===_||e===A||e===I||Ae||e===Ce||je||De||Fe||typeof e==\"object\"&&e!==null&&(e.$$typeof===z||e.$$typeof===C||e.$$typeof===K||e.$$typeof===H||e.$$typeof===k||e.$$typeof===Z||e.getModuleId!==void 0))}function Le(e,r,n){var t=e.displayName;if(t)return t;var u=r.displayName||r.name||\"\";return u!==\"\"?n+\"(\"+u+\")\":n}function Q(e){return e.displayName||\"Context\"}function v(e){if(e==null)return null;if(typeof e.tag==\"number\"&&b(\"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"),typeof e==\"function\")return e.displayName||e.name||null;if(typeof e==\"string\")return e;switch(e){case y:return\"Fragment\";case m:return\"Portal\";case X:return\"Profiler\";case _:return\"StrictMode\";case A:return\"Suspense\";case I:return\"SuspenseList\"}if(typeof e==\"object\")switch(e.$$typeof){case H:var r=e;return Q(r)+\".Consumer\";case K:var n=e;return Q(n._context)+\".Provider\";case k:return Le(e,e.render,\"ForwardRef\");case C:var t=e.displayName||null;return t!==null?t:v(e.type)||\"Memo\";case z:{var u=e,c=u._payload,o=u._init;try{return v(o(c))}catch{return null}}}return null}var E=Object.assign,R=0,ee,re,ne,te,ae,ie,oe;function ue(){}ue.__reactDisabledLog=!0;function Ye(){{if(R===0){ee=console.log,re=console.info,ne=console.warn,te=console.error,ae=console.group,ie=console.groupCollapsed,oe=console.groupEnd;var e={configurable:!0,enumerable:!0,value:ue,writable:!0};Object.defineProperties(console,{info:e,log:e,warn:e,error:e,group:e,groupCollapsed:e,groupEnd:e})}R++}}function $e(){{if(R--,R===0){var e={configurable:!0,enumerable:!0,writable:!0};Object.defineProperties(console,{log:E({},e,{value:ee}),info:E({},e,{value:re}),warn:E({},e,{value:ne}),error:E({},e,{value:te}),group:E({},e,{value:ae}),groupCollapsed:E({},e,{value:ie}),groupEnd:E({},e,{value:oe})})}R<0&&b(\"disabledDepth fell below zero. This is a bug in React. Please file an issue.\")}}var L=x.ReactCurrentDispatcher,Y;function P(e,r,n){{if(Y===void 0)try{throw Error()}catch(u){var t=u.stack.trim().match(/\\n( *(at )?)/);Y=t&&t[1]||\"\"}return`\n`+Y+e}}var $=!1,O;{var Me=typeof WeakMap==\"function\"?WeakMap:Map;O=new Me}function le(e,r){if(!e||$)return\"\";{var n=O.get(e);if(n!==void 0)return n}var t;$=!0;var u=Error.prepareStackTrace;Error.prepareStackTrace=void 0;var c;c=L.current,L.current=null,Ye();try{if(r){var o=function(){throw Error()};if(Object.defineProperty(o.prototype,\"props\",{set:function(){throw Error()}}),typeof Reflect==\"object\"&&Reflect.construct){try{Reflect.construct(o,[])}catch(h){t=h}Reflect.construct(e,[],o)}else{try{o.call()}catch(h){t=h}e.call(o.prototype)}}else{try{throw Error()}catch(h){t=h}e()}}catch(h){if(h&&t&&typeof h.stack==\"string\"){for(var i=h.stack.split(`\n`),p=t.stack.split(`\n`),f=i.length-1,d=p.length-1;f>=1&&d>=0&&i[f]!==p[d];)d--;for(;f>=1&&d>=0;f--,d--)if(i[f]!==p[d]){if(f!==1||d!==1)do if(f--,d--,d<0||i[f]!==p[d]){var g=`\n`+i[f].replace(\" at new \",\" at \");return e.displayName&&g.includes(\"<anonymous>\")&&(g=g.replace(\"<anonymous>\",e.displayName)),typeof e==\"function\"&&O.set(e,g),g}while(f>=1&&d>=0);break}}}finally{$=!1,L.current=c,$e(),Error.prepareStackTrace=u}var N=e?e.displayName||e.name:\"\",ye=N?P(N):\"\";return typeof e==\"function\"&&O.set(e,ye),ye}function We(e,r,n){return le(e,!1)}function qe(e){var r=e.prototype;return!!(r&&r.isReactComponent)}function S(e,r,n){if(e==null)return\"\";if(typeof e==\"function\")return le(e,qe(e));if(typeof e==\"string\")return P(e);switch(e){case A:return P(\"Suspense\");case I:return P(\"SuspenseList\")}if(typeof e==\"object\")switch(e.$$typeof){case k:return We(e.render);case C:return S(e.type,r,n);case z:{var t=e,u=t._payload,c=t._init;try{return S(c(u),r,n)}catch{}}}return\"\"}var j=Object.prototype.hasOwnProperty,se={},ce=x.ReactDebugCurrentFrame;function D(e){if(e){var r=e._owner,n=S(e.type,e._source,r?r.type:null);ce.setExtraStackFrame(n)}else ce.setExtraStackFrame(null)}function Ve(e,r,n,t,u){{var c=Function.call.bind(j);for(var o in e)if(c(e,o)){var i=void 0;try{if(typeof e[o]!=\"function\"){var p=Error((t||\"React class\")+\": \"+n+\" type `\"+o+\"` is invalid; it must be a function, usually from the `prop-types` package, but received `\"+typeof e[o]+\"`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.\");throw p.name=\"Invariant Violation\",p}i=e[o](r,o,t,n,null,\"SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED\")}catch(f){i=f}i&&!(i instanceof Error)&&(D(u),b(\"%s: type specification of %s `%s` is invalid; the type checker function must return `null` or an `Error` but returned a %s. You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument).\",t||\"React class\",n,o,typeof i),D(null)),i instanceof Error&&!(i.message in se)&&(se[i.message]=!0,D(u),b(\"Failed %s type: %s\",n,i.message),D(null))}}}var Ue=Array.isArray;function M(e){return Ue(e)}function Be(e){{var r=typeof Symbol==\"function\"&&Symbol.toStringTag,n=r&&e[Symbol.toStringTag]||e.constructor.name||\"Object\";return n}}function Ge(e){try{return fe(e),!1}catch{return!0}}function fe(e){return\"\"+e}function de(e){if(Ge(e))return b(\"The provided key is an unsupported type %s. This value must be coerced to a string before before using it here.\",Be(e)),fe(e)}var T=x.ReactCurrentOwner,Xe={key:!0,ref:!0,__self:!0,__source:!0},me,be,W;W={};function Ke(e){if(j.call(e,\"ref\")){var r=Object.getOwnPropertyDescriptor(e,\"ref\").get;if(r&&r.isReactWarning)return!1}return e.ref!==void 0}function He(e){if(j.call(e,\"key\")){var r=Object.getOwnPropertyDescriptor(e,\"key\").get;if(r&&r.isReactWarning)return!1}return e.key!==void 0}function Je(e,r){if(typeof e.ref==\"string\"&&T.current&&r&&T.current.stateNode!==r){var n=v(T.current.type);W[n]||(b('Component \"%s\" contains the string ref \"%s\". Support for string refs will be removed in a future major release. This case cannot be automatically converted to an arrow function. We ask you to manually fix this case by using useRef() or createRef() instead. Learn more about using refs safely here: https://reactjs.org/link/strict-mode-string-ref',v(T.current.type),e.ref),W[n]=!0)}}function Ze(e,r){{var n=function(){me||(me=!0,b(\"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)\",r))};n.isReactWarning=!0,Object.defineProperty(e,\"key\",{get:n,configurable:!0})}}function Qe(e,r){{var n=function(){be||(be=!0,b(\"%s: `ref` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)\",r))};n.isReactWarning=!0,Object.defineProperty(e,\"ref\",{get:n,configurable:!0})}}var er=function(e,r,n,t,u,c,o){var i={$$typeof:a,type:e,key:r,ref:n,props:o,_owner:c};return i._store={},Object.defineProperty(i._store,\"validated\",{configurable:!1,enumerable:!1,writable:!0,value:!1}),Object.defineProperty(i,\"_self\",{configurable:!1,enumerable:!1,writable:!1,value:t}),Object.defineProperty(i,\"_source\",{configurable:!1,enumerable:!1,writable:!1,value:u}),Object.freeze&&(Object.freeze(i.props),Object.freeze(i)),i};function rr(e,r,n,t,u){{var c,o={},i=null,p=null;n!==void 0&&(de(n),i=\"\"+n),He(r)&&(de(r.key),i=\"\"+r.key),Ke(r)&&(p=r.ref,Je(r,u));for(c in r)j.call(r,c)&&!Xe.hasOwnProperty(c)&&(o[c]=r[c]);if(e&&e.defaultProps){var f=e.defaultProps;for(c in f)o[c]===void 0&&(o[c]=f[c])}if(i||p){var d=typeof e==\"function\"?e.displayName||e.name||\"Unknown\":e;i&&Ze(o,d),p&&Qe(o,d)}return er(e,i,p,u,t,T.current,o)}}var q=x.ReactCurrentOwner,pe=x.ReactDebugCurrentFrame;function w(e){if(e){var r=e._owner,n=S(e.type,e._source,r?r.type:null);pe.setExtraStackFrame(n)}else pe.setExtraStackFrame(null)}var V;V=!1;function U(e){return typeof e==\"object\"&&e!==null&&e.$$typeof===a}function ge(){{if(q.current){var e=v(q.current.type);if(e)return`\n\nCheck the render method of \\``+e+\"`.\"}return\"\"}}function nr(e){{if(e!==void 0){var r=e.fileName.replace(/^.*[\\\\\\/]/,\"\"),n=e.lineNumber;return`\n\nCheck your code at `+r+\":\"+n+\".\"}return\"\"}}var ve={};function tr(e){{var r=ge();if(!r){var n=typeof e==\"string\"?e:e.displayName||e.name;n&&(r=`\n\nCheck the top-level render call using <`+n+\">.\")}return r}}function he(e,r){{if(!e._store||e._store.validated||e.key!=null)return;e._store.validated=!0;var n=tr(r);if(ve[n])return;ve[n]=!0;var t=\"\";e&&e._owner&&e._owner!==q.current&&(t=\" It was passed a child from \"+v(e._owner.type)+\".\"),w(e),b('Each child in a list should have a unique \"key\" prop.%s%s See https://reactjs.org/link/warning-keys for more information.',n,t),w(null)}}function _e(e,r){{if(typeof e!=\"object\")return;if(M(e))for(var n=0;n<e.length;n++){var t=e[n];U(t)&&he(t,r)}else if(U(e))e._store&&(e._store.validated=!0);else if(e){var u=Oe(e);if(typeof u==\"function\"&&u!==e.entries)for(var c=u.call(e),o;!(o=c.next()).done;)U(o.value)&&he(o.value,r)}}}function ar(e){{var r=e.type;if(r==null||typeof r==\"string\")return;var n;if(typeof r==\"function\")n=r.propTypes;else if(typeof r==\"object\"&&(r.$$typeof===k||r.$$typeof===C))n=r.propTypes;else return;if(n){var t=v(r);Ve(n,e.props,\"prop\",t,e)}else if(r.PropTypes!==void 0&&!V){V=!0;var u=v(r);b(\"Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?\",u||\"Unknown\")}typeof r.getDefaultProps==\"function\"&&!r.getDefaultProps.isReactClassApproved&&b(\"getDefaultProps is only used on classic React.createClass definitions. Use a static property named `defaultProps` instead.\")}}function ir(e){{for(var r=Object.keys(e.props),n=0;n<r.length;n++){var t=r[n];if(t!==\"children\"&&t!==\"key\"){w(e),b(\"Invalid prop `%s` supplied to `React.Fragment`. React.Fragment can only have `key` and `children` props.\",t),w(null);break}}e.ref!==null&&(w(e),b(\"Invalid attribute `ref` supplied to `React.Fragment`.\"),w(null))}}function or(e,r,n,t,u,c){{var o=ze(e);if(!o){var i=\"\";(e===void 0||typeof e==\"object\"&&e!==null&&Object.keys(e).length===0)&&(i+=\" You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.\");var p=nr(u);p?i+=p:i+=ge();var f;e===null?f=\"null\":M(e)?f=\"array\":e!==void 0&&e.$$typeof===a?(f=\"<\"+(v(e.type)||\"Unknown\")+\" />\",i=\" Did you accidentally export a JSX literal instead of a component?\"):f=typeof e,b(\"React.jsx: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: %s.%s\",f,i)}var d=rr(e,r,n,u,c);if(d==null)return d;if(o){var g=r.children;if(g!==void 0)if(t)if(M(g)){for(var N=0;N<g.length;N++)_e(g[N],e);Object.freeze&&Object.freeze(g)}else b(\"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\");else _e(g,e)}return e===y?ir(d):ar(d),d}}var ur=or;G.Fragment=y,G.jsxDEV=ur})()});var Te=B((Nr,Re)=>{\"use strict\";Re.exports=Ne()});var yr={};mr(yr,{default:()=>hr,frontmatter:()=>gr});var l=br(Te()),gr={title:\"Deezer d\\xE9voile son nouveau logo en 2023\",excerpt:\"L\\u2019ann\\xE9e 2023 marque une \\xE8re nouvelle pour Deezer, le g\\xE9ant du streaming musical. Avec son audacieux rebranding incarn\\xE9 par un logo au c\\u0153ur vibrant.\",date:\"2023-11-15\",updated:\"2023-11-15\",category:\"Image de marque\",cover:\"/images/blog/logo-deezer-2023/main-logo-deezer-2023.png\"};function ke(s){let a=Object.assign({p:\"p\",h2:\"h2\",ol:\"ol\",li:\"li\",strong:\"strong\"},s.components),{Image:m}=a;return m||_r(\"Image\",!0,\"47:1-50:3\"),(0,l.jsxDEV)(l.Fragment,{children:[(0,l.jsxDEV)(a.p,{children:\"Ce nouveau c\\u0153ur violet divise, mais l\\u2019approche centr\\xE9e-communaut\\xE9 colle bien \\xE0 la promesse de Deezer. On vous explique pourquoi\\u2014et comment le logo pourrait gagner ses galons.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-b0bf40d6-38c9-44a8-822d-ca492c96deb3.mdx\",lineNumber:10,columnNumber:1},this),`\n`,(0,l.jsxDEV)(a.h2,{children:\"Pourquoi changer\\xA0?\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-b0bf40d6-38c9-44a8-822d-ca492c96deb3.mdx\",lineNumber:12,columnNumber:1},this),`\n`,(0,l.jsxDEV)(a.p,{children:\"En 2023, Deezer cherchait deux choses\\xA0:\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-b0bf40d6-38c9-44a8-822d-ca492c96deb3.mdx\",lineNumber:13,columnNumber:1},this),`\n`,(0,l.jsxDEV)(a.ol,{children:[`\n`,(0,l.jsxDEV)(a.li,{children:\"Clarifier sa mission pour devenir \\xAB\\u202Fla maison de la musique\\u202F\\xBB plut\\xF4t qu\\u2019un simple service de streaming.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-b0bf40d6-38c9-44a8-822d-ca492c96deb3.mdx\",lineNumber:14,columnNumber:1},this),`\n`,(0,l.jsxDEV)(a.li,{children:\"Se diff\\xE9rencier d\\u2019un march\\xE9 satur\\xE9 (Spotify et Apple Music) gr\\xE2ce \\xE0 un symbole fort et facile \\xE0 d\\xE9cliner.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-b0bf40d6-38c9-44a8-822d-ca492c96deb3.mdx\",lineNumber:15,columnNumber:1},this),`\n`]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-b0bf40d6-38c9-44a8-822d-ca492c96deb3.mdx\",lineNumber:14,columnNumber:1},this),`\n`,(0,l.jsxDEV)(a.p,{children:\"La refonte s\\u2019inscrit donc moins dans un mouvement esth\\xE9tique que dans une red\\xE9finition de positionnement.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-b0bf40d6-38c9-44a8-822d-ca492c96deb3.mdx\",lineNumber:17,columnNumber:1},this),`\n`,(0,l.jsxDEV)(\"video\",{src:\"/images/blog/logo-deezer-2023/affiches-animees-deezer.webm\",playsInline:!0,autoPlay:!0,muted:!0,loop:!0,style:{width:\"100%\"}},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-b0bf40d6-38c9-44a8-822d-ca492c96deb3.mdx\",lineNumber:19,columnNumber:1},this),`\n`,(0,l.jsxDEV)(a.h2,{children:\"Un c\\u0153ur qui pulse\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-b0bf40d6-38c9-44a8-822d-ca492c96deb3.mdx\",lineNumber:28,columnNumber:1},this),`\n`,(0,l.jsxDEV)(a.p,{children:\"Le nouveau symbole de Deezer, un c\\u0153ur violet anim\\xE9, incarne litt\\xE9ralement leur message\\xA0: \\xAB\\xA0Feel the music\\xA0\\xBB.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-b0bf40d6-38c9-44a8-822d-ca492c96deb3.mdx\",lineNumber:30,columnNumber:1},this),`\n`,(0,l.jsxDEV)(a.p,{children:\"L\\u2019animation se cale sur le tempo du morceau en cours et plonge l\\u2019utilisateur dans l\\u2019univers de la plateforme. Visuellement, ce c\\u0153ur se m\\xE9morise d\\u2019un coup d\\u2019\\u0153il et tranche avec les teintes dominantes chez les autres services de streaming, comme pour le vert chez Spotify.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-b0bf40d6-38c9-44a8-822d-ca492c96deb3.mdx\",lineNumber:32,columnNumber:1},this),`\n`,(0,l.jsxDEV)(\"video\",{src:\"/images/blog/logo-deezer-2023/animation-logo-deezer.webm\",playsInline:!0,autoPlay:!0,muted:!0,loop:!0,style:{width:\"100%\"}},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-b0bf40d6-38c9-44a8-822d-ca492c96deb3.mdx\",lineNumber:34,columnNumber:1},this),`\n`,(0,l.jsxDEV)(a.p,{children:[\"Pour accompagner ce pictogramme, Deezer incarne une \",(0,l.jsxDEV)(a.strong,{children:\"nouvelle police\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-b0bf40d6-38c9-44a8-822d-ca492c96deb3.mdx\",lineNumber:43,columnNumber:53},this),\" sur\\u202Fmesure\\xA0: Deezer Sans.\"]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-b0bf40d6-38c9-44a8-822d-ca492c96deb3.mdx\",lineNumber:43,columnNumber:1},this),`\n`,(0,l.jsxDEV)(a.p,{children:\"Les courbes reprennent les lignes du logo\\xA0: suffisamment \\xE9troites pour les interfaces denses, assez larges pour soutenir un message fort sur une affiche.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-b0bf40d6-38c9-44a8-822d-ca492c96deb3.mdx\",lineNumber:45,columnNumber:1},this),`\n`,(0,l.jsxDEV)(m,{src:\"/images/blog/logo-deezer-2023/affiches-deezer.jpg\",alt:\"Affiches du nouveau logo Deezer\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-b0bf40d6-38c9-44a8-822d-ca492c96deb3.mdx\",lineNumber:47,columnNumber:1},this),`\n`,(0,l.jsxDEV)(\"video\",{src:\"/images/blog/logo-deezer-2023/animation-video-deezer.webm\",playsInline:!0,autoPlay:!0,muted:!0,loop:!0,style:{width:\"100%\"}},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-b0bf40d6-38c9-44a8-822d-ca492c96deb3.mdx\",lineNumber:52,columnNumber:1},this),`\n`,(0,l.jsxDEV)(a.h2,{children:\"Une identit\\xE9 pour s\\xE9duire un public plus jeune\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-b0bf40d6-38c9-44a8-822d-ca492c96deb3.mdx\",lineNumber:61,columnNumber:1},this),`\n`,(0,l.jsxDEV)(a.p,{children:[\"Du c\\xF4t\\xE9 des cr\\xE9atifs, la prise de risque s\\xE9duit\\u202F: d\\xE9laisser l'ancien logo avec l\\u2019\\xE9galiseur pour un c\\u0153ur anim\\xE9 n\\u2019allait pas de soi. Dans les forums, certains applaudissent l\\u2019audace\\u202F; d\\u2019autres, plus sceptiques, trouvent le visuel trop proche des \",(0,l.jsxDEV)(a.strong,{children:\"codes d\\u2019une appli de rencontre\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-b0bf40d6-38c9-44a8-822d-ca492c96deb3.mdx\",lineNumber:63,columnNumber:245},this),\".\"]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-b0bf40d6-38c9-44a8-822d-ca492c96deb3.mdx\",lineNumber:63,columnNumber:1},this),`\n`,(0,l.jsxDEV)(a.p,{children:\"Les abonn\\xE9s sont partag\\xE9s\\xA0: beaucoup appr\\xE9cient l\\u2019approche plus chaleureuse, tandis que les nostalgiques regrettent l\\u2019ancien logo.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-b0bf40d6-38c9-44a8-822d-ca492c96deb3.mdx\",lineNumber:65,columnNumber:1},this),`\n`,(0,l.jsxDEV)(a.p,{children:\"Sur le plan de la m\\xE9morisation, le pari est gagnant\\u202F: l\\u2019ic\\xF4ne fonctionne aussi bien sur une montre connect\\xE9e que sur un panneau d\\u2019affichage urbain. Le d\\xE9fi reste de pr\\xE9server la reconnaissance hors animation\\xA0\\u2014 sur imprim\\xE9, favicon ou merchandise \\u2014 en s\\u2019appuyant sur un univers visuel coh\\xE9rent\\u202F: ondes, halo violet, micro\\u2011motions.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-b0bf40d6-38c9-44a8-822d-ca492c96deb3.mdx\",lineNumber:67,columnNumber:1},this),`\n`,(0,l.jsxDEV)(m,{src:\"/images/blog/logo-deezer-2023/illustrations-deezer.webp\",alt:\"Illustrations de l'identit\\xE9 visuelle de Deezer\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-b0bf40d6-38c9-44a8-822d-ca492c96deb3.mdx\",lineNumber:69,columnNumber:1},this)]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-b0bf40d6-38c9-44a8-822d-ca492c96deb3.mdx\",lineNumber:1,columnNumber:1},this)}function vr(s={}){let{wrapper:a}=s.components||{};return a?(0,l.jsxDEV)(a,Object.assign({},s,{children:(0,l.jsxDEV)(ke,s,void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-b0bf40d6-38c9-44a8-822d-ca492c96deb3.mdx\"},this)}),void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-b0bf40d6-38c9-44a8-822d-ca492c96deb3.mdx\"},this):ke(s)}var hr=vr;function _r(s,a,m){throw new Error(\"Expected \"+(a?\"component\":\"object\")+\" `\"+s+\"` to be defined: you likely forgot to import, pass, or provide it.\"+(m?\"\\nIt\\u2019s referenced in your code at `\"+m+\"` in `/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-b0bf40d6-38c9-44a8-822d-ca492c96deb3.mdx`\":\"\"))}return pr(yr);})();\n/*! Bundled license information:\n\nreact/cjs/react-jsx-dev-runtime.development.js:\n  (**\n   * @license React\n   * react-jsx-dev-runtime.development.js\n   *\n   * Copyright (c) Facebook, Inc. and its affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *)\n*/\n;return Component;"}, "_id": "blog/fr/logo-deezer-2023.mdx", "_raw": {"sourceFilePath": "blog/fr/logo-deezer-2023.mdx", "sourceFileName": "logo-deezer-2023.mdx", "sourceFileDir": "blog/fr", "contentType": "mdx", "flattenedPath": "blog/fr/logo-deezer-2023"}, "type": "Post", "locale": "fr", "slug": "logo-deezer-2023"}