{"title": "11 erreurs fréquentes sur un site web", "excerpt": "De nos jours, un site web est un outil indispensable pour toute entreprise souhaitant se démarquer et atteindre ses objectifs. Cependant, créer un site web performant peut être un véritable challenge. Il existe des erreurs fréquentes qui peuvent nuire à votre site.", "date": "2025-05-17T00:00:00.000Z", "updated": "2025-05-20T00:00:00.000Z", "category": "Développement web", "cover": "/images/blog/11-erreurs-site-web/main-erreurs-site-web.webp", "published": false, "body": {"raw": "\n\nDe nos jours, un site web est un outil indispensable pour toute entreprise souhaitant se démarquer et atteindre ses objectifs. Cependant, créer un site web performant peut être un véritable challenge. Il existe des erreurs fréquentes qui peuvent nuire à votre site.\n\nDans cet article, nous vous présenterons 11 fréquentes sur un site web et nous vous donnerons des conseils pour les éviter.\n\n## 1. Utiliser des polices d’écriture peu lisibles\n\nIl est tentant d’utiliser des polices d’écriture originales pour se démarquer, mais cela peut **nuire à la lisibilité** de votre site. La lisibilité est cruciale pour permettre à vos visiteurs de comprendre rapidement et facilement le message que vous souhaitez transmettre. En utilisant des polices extravagantes, vous risquez non seulement de perdre l’attention de vos visiteurs, mais également de donner une image peu professionnelle de votre entreprise. \n\n", "code": "var Component=(()=>{var sr=Object.create;var N=Object.defineProperty;var lr=Object.getOwnPropertyDescriptor;var cr=Object.getOwnPropertyNames;var fr=Object.getPrototypeOf,dr=Object.prototype.hasOwnProperty;var z=(l,u)=>()=>(u||l((u={exports:{}}).exports,u),u.exports),vr=(l,u)=>{for(var m in u)N(l,m,{get:u[m],enumerable:!0})},ye=(l,u,m,E)=>{if(u&&typeof u==\"object\"||typeof u==\"function\")for(let _ of cr(u))!dr.call(l,_)&&_!==m&&N(l,_,{get:()=>u[_],enumerable:!(E=lr(u,_))||E.enumerable});return l};var br=(l,u,m)=>(m=l!=null?sr(fr(l)):{},ye(u||!l||!l.__esModule?N(m,\"default\",{value:l,enumerable:!0}):m,l)),pr=l=>ye(N({},\"__esModule\",{value:!0}),l);var we=z((yr,Re)=>{Re.exports=React});var Te=z(G=>{\"use strict\";(function(){\"use strict\";var l=we(),u=Symbol.for(\"react.element\"),m=Symbol.for(\"react.portal\"),E=Symbol.for(\"react.fragment\"),_=Symbol.for(\"react.strict_mode\"),X=Symbol.for(\"react.profiler\"),K=Symbol.for(\"react.provider\"),H=Symbol.for(\"react.context\"),O=Symbol.for(\"react.forward_ref\"),A=Symbol.for(\"react.suspense\"),I=Symbol.for(\"react.suspense_list\"),P=Symbol.for(\"react.memo\"),Y=Symbol.for(\"react.lazy\"),Pe=Symbol.for(\"react.offscreen\"),J=Symbol.iterator,Se=\"@@iterator\";function je(e){if(e===null||typeof e!=\"object\")return null;var r=J&&e[J]||e[Se];return typeof r==\"function\"?r:null}var R=l.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;function d(e){{for(var r=arguments.length,t=new Array(r>1?r-1:0),n=1;n<r;n++)t[n-1]=arguments[n];ke(\"error\",e,t)}}function ke(e,r,t){{var n=R.ReactDebugCurrentFrame,o=n.getStackAddendum();o!==\"\"&&(r+=\"%s\",t=t.concat([o]));var s=t.map(function(i){return String(i)});s.unshift(\"Warning: \"+r),Function.prototype.apply.call(console[e],console,s)}}var De=!1,Fe=!1,Ne=!1,Ae=!1,Ie=!1,Z;Z=Symbol.for(\"react.module.reference\");function Ye(e){return!!(typeof e==\"string\"||typeof e==\"function\"||e===E||e===X||Ie||e===_||e===A||e===I||Ae||e===Pe||De||Fe||Ne||typeof e==\"object\"&&e!==null&&(e.$$typeof===Y||e.$$typeof===P||e.$$typeof===K||e.$$typeof===H||e.$$typeof===O||e.$$typeof===Z||e.getModuleId!==void 0))}function $e(e,r,t){var n=e.displayName;if(n)return n;var o=r.displayName||r.name||\"\";return o!==\"\"?t+\"(\"+o+\")\":t}function Q(e){return e.displayName||\"Context\"}function g(e){if(e==null)return null;if(typeof e.tag==\"number\"&&d(\"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"),typeof e==\"function\")return e.displayName||e.name||null;if(typeof e==\"string\")return e;switch(e){case E:return\"Fragment\";case m:return\"Portal\";case X:return\"Profiler\";case _:return\"StrictMode\";case A:return\"Suspense\";case I:return\"SuspenseList\"}if(typeof e==\"object\")switch(e.$$typeof){case H:var r=e;return Q(r)+\".Consumer\";case K:var t=e;return Q(t._context)+\".Provider\";case O:return $e(e,e.render,\"ForwardRef\");case P:var n=e.displayName||null;return n!==null?n:g(e.type)||\"Memo\";case Y:{var o=e,s=o._payload,i=o._init;try{return g(i(s))}catch{return null}}}return null}var y=Object.assign,C=0,ee,re,te,ne,ae,ie,oe;function ue(){}ue.__reactDisabledLog=!0;function We(){{if(C===0){ee=console.log,re=console.info,te=console.warn,ne=console.error,ae=console.group,ie=console.groupCollapsed,oe=console.groupEnd;var e={configurable:!0,enumerable:!0,value:ue,writable:!0};Object.defineProperties(console,{info:e,log:e,warn:e,error:e,group:e,groupCollapsed:e,groupEnd:e})}C++}}function Me(){{if(C--,C===0){var e={configurable:!0,enumerable:!0,writable:!0};Object.defineProperties(console,{log:y({},e,{value:ee}),info:y({},e,{value:re}),warn:y({},e,{value:te}),error:y({},e,{value:ne}),group:y({},e,{value:ae}),groupCollapsed:y({},e,{value:ie}),groupEnd:y({},e,{value:oe})})}C<0&&d(\"disabledDepth fell below zero. This is a bug in React. Please file an issue.\")}}var $=R.ReactCurrentDispatcher,W;function S(e,r,t){{if(W===void 0)try{throw Error()}catch(o){var n=o.stack.trim().match(/\\n( *(at )?)/);W=n&&n[1]||\"\"}return`\n`+W+e}}var M=!1,j;{var Ve=typeof WeakMap==\"function\"?WeakMap:Map;j=new Ve}function se(e,r){if(!e||M)return\"\";{var t=j.get(e);if(t!==void 0)return t}var n;M=!0;var o=Error.prepareStackTrace;Error.prepareStackTrace=void 0;var s;s=$.current,$.current=null,We();try{if(r){var i=function(){throw Error()};if(Object.defineProperty(i.prototype,\"props\",{set:function(){throw Error()}}),typeof Reflect==\"object\"&&Reflect.construct){try{Reflect.construct(i,[])}catch(h){n=h}Reflect.construct(e,[],i)}else{try{i.call()}catch(h){n=h}e.call(i.prototype)}}else{try{throw Error()}catch(h){n=h}e()}}catch(h){if(h&&n&&typeof h.stack==\"string\"){for(var a=h.stack.split(`\n`),v=n.stack.split(`\n`),c=a.length-1,f=v.length-1;c>=1&&f>=0&&a[c]!==v[f];)f--;for(;c>=1&&f>=0;c--,f--)if(a[c]!==v[f]){if(c!==1||f!==1)do if(c--,f--,f<0||a[c]!==v[f]){var b=`\n`+a[c].replace(\" at new \",\" at \");return e.displayName&&b.includes(\"<anonymous>\")&&(b=b.replace(\"<anonymous>\",e.displayName)),typeof e==\"function\"&&j.set(e,b),b}while(c>=1&&f>=0);break}}}finally{M=!1,$.current=s,Me(),Error.prepareStackTrace=o}var T=e?e.displayName||e.name:\"\",Ee=T?S(T):\"\";return typeof e==\"function\"&&j.set(e,Ee),Ee}function Le(e,r,t){return se(e,!1)}function qe(e){var r=e.prototype;return!!(r&&r.isReactComponent)}function k(e,r,t){if(e==null)return\"\";if(typeof e==\"function\")return se(e,qe(e));if(typeof e==\"string\")return S(e);switch(e){case A:return S(\"Suspense\");case I:return S(\"SuspenseList\")}if(typeof e==\"object\")switch(e.$$typeof){case O:return Le(e.render);case P:return k(e.type,r,t);case Y:{var n=e,o=n._payload,s=n._init;try{return k(s(o),r,t)}catch{}}}return\"\"}var D=Object.prototype.hasOwnProperty,le={},ce=R.ReactDebugCurrentFrame;function F(e){if(e){var r=e._owner,t=k(e.type,e._source,r?r.type:null);ce.setExtraStackFrame(t)}else ce.setExtraStackFrame(null)}function Ue(e,r,t,n,o){{var s=Function.call.bind(D);for(var i in e)if(s(e,i)){var a=void 0;try{if(typeof e[i]!=\"function\"){var v=Error((n||\"React class\")+\": \"+t+\" type `\"+i+\"` is invalid; it must be a function, usually from the `prop-types` package, but received `\"+typeof e[i]+\"`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.\");throw v.name=\"Invariant Violation\",v}a=e[i](r,i,n,t,null,\"SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED\")}catch(c){a=c}a&&!(a instanceof Error)&&(F(o),d(\"%s: type specification of %s `%s` is invalid; the type checker function must return `null` or an `Error` but returned a %s. You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument).\",n||\"React class\",t,i,typeof a),F(null)),a instanceof Error&&!(a.message in le)&&(le[a.message]=!0,F(o),d(\"Failed %s type: %s\",t,a.message),F(null))}}}var Be=Array.isArray;function V(e){return Be(e)}function ze(e){{var r=typeof Symbol==\"function\"&&Symbol.toStringTag,t=r&&e[Symbol.toStringTag]||e.constructor.name||\"Object\";return t}}function Ge(e){try{return fe(e),!1}catch{return!0}}function fe(e){return\"\"+e}function de(e){if(Ge(e))return d(\"The provided key is an unsupported type %s. This value must be coerced to a string before before using it here.\",ze(e)),fe(e)}var x=R.ReactCurrentOwner,Xe={key:!0,ref:!0,__self:!0,__source:!0},ve,be,L;L={};function Ke(e){if(D.call(e,\"ref\")){var r=Object.getOwnPropertyDescriptor(e,\"ref\").get;if(r&&r.isReactWarning)return!1}return e.ref!==void 0}function He(e){if(D.call(e,\"key\")){var r=Object.getOwnPropertyDescriptor(e,\"key\").get;if(r&&r.isReactWarning)return!1}return e.key!==void 0}function Je(e,r){if(typeof e.ref==\"string\"&&x.current&&r&&x.current.stateNode!==r){var t=g(x.current.type);L[t]||(d('Component \"%s\" contains the string ref \"%s\". Support for string refs will be removed in a future major release. This case cannot be automatically converted to an arrow function. We ask you to manually fix this case by using useRef() or createRef() instead. Learn more about using refs safely here: https://reactjs.org/link/strict-mode-string-ref',g(x.current.type),e.ref),L[t]=!0)}}function Ze(e,r){{var t=function(){ve||(ve=!0,d(\"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)\",r))};t.isReactWarning=!0,Object.defineProperty(e,\"key\",{get:t,configurable:!0})}}function Qe(e,r){{var t=function(){be||(be=!0,d(\"%s: `ref` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)\",r))};t.isReactWarning=!0,Object.defineProperty(e,\"ref\",{get:t,configurable:!0})}}var er=function(e,r,t,n,o,s,i){var a={$$typeof:u,type:e,key:r,ref:t,props:i,_owner:s};return a._store={},Object.defineProperty(a._store,\"validated\",{configurable:!1,enumerable:!1,writable:!0,value:!1}),Object.defineProperty(a,\"_self\",{configurable:!1,enumerable:!1,writable:!1,value:n}),Object.defineProperty(a,\"_source\",{configurable:!1,enumerable:!1,writable:!1,value:o}),Object.freeze&&(Object.freeze(a.props),Object.freeze(a)),a};function rr(e,r,t,n,o){{var s,i={},a=null,v=null;t!==void 0&&(de(t),a=\"\"+t),He(r)&&(de(r.key),a=\"\"+r.key),Ke(r)&&(v=r.ref,Je(r,o));for(s in r)D.call(r,s)&&!Xe.hasOwnProperty(s)&&(i[s]=r[s]);if(e&&e.defaultProps){var c=e.defaultProps;for(s in c)i[s]===void 0&&(i[s]=c[s])}if(a||v){var f=typeof e==\"function\"?e.displayName||e.name||\"Unknown\":e;a&&Ze(i,f),v&&Qe(i,f)}return er(e,a,v,o,n,x.current,i)}}var q=R.ReactCurrentOwner,pe=R.ReactDebugCurrentFrame;function w(e){if(e){var r=e._owner,t=k(e.type,e._source,r?r.type:null);pe.setExtraStackFrame(t)}else pe.setExtraStackFrame(null)}var U;U=!1;function B(e){return typeof e==\"object\"&&e!==null&&e.$$typeof===u}function me(){{if(q.current){var e=g(q.current.type);if(e)return`\n\nCheck the render method of \\``+e+\"`.\"}return\"\"}}function tr(e){{if(e!==void 0){var r=e.fileName.replace(/^.*[\\\\\\/]/,\"\"),t=e.lineNumber;return`\n\nCheck your code at `+r+\":\"+t+\".\"}return\"\"}}var ge={};function nr(e){{var r=me();if(!r){var t=typeof e==\"string\"?e:e.displayName||e.name;t&&(r=`\n\nCheck the top-level render call using <`+t+\">.\")}return r}}function he(e,r){{if(!e._store||e._store.validated||e.key!=null)return;e._store.validated=!0;var t=nr(r);if(ge[t])return;ge[t]=!0;var n=\"\";e&&e._owner&&e._owner!==q.current&&(n=\" It was passed a child from \"+g(e._owner.type)+\".\"),w(e),d('Each child in a list should have a unique \"key\" prop.%s%s See https://reactjs.org/link/warning-keys for more information.',t,n),w(null)}}function _e(e,r){{if(typeof e!=\"object\")return;if(V(e))for(var t=0;t<e.length;t++){var n=e[t];B(n)&&he(n,r)}else if(B(e))e._store&&(e._store.validated=!0);else if(e){var o=je(e);if(typeof o==\"function\"&&o!==e.entries)for(var s=o.call(e),i;!(i=s.next()).done;)B(i.value)&&he(i.value,r)}}}function ar(e){{var r=e.type;if(r==null||typeof r==\"string\")return;var t;if(typeof r==\"function\")t=r.propTypes;else if(typeof r==\"object\"&&(r.$$typeof===O||r.$$typeof===P))t=r.propTypes;else return;if(t){var n=g(r);Ue(t,e.props,\"prop\",n,e)}else if(r.PropTypes!==void 0&&!U){U=!0;var o=g(r);d(\"Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?\",o||\"Unknown\")}typeof r.getDefaultProps==\"function\"&&!r.getDefaultProps.isReactClassApproved&&d(\"getDefaultProps is only used on classic React.createClass definitions. Use a static property named `defaultProps` instead.\")}}function ir(e){{for(var r=Object.keys(e.props),t=0;t<r.length;t++){var n=r[t];if(n!==\"children\"&&n!==\"key\"){w(e),d(\"Invalid prop `%s` supplied to `React.Fragment`. React.Fragment can only have `key` and `children` props.\",n),w(null);break}}e.ref!==null&&(w(e),d(\"Invalid attribute `ref` supplied to `React.Fragment`.\"),w(null))}}function or(e,r,t,n,o,s){{var i=Ye(e);if(!i){var a=\"\";(e===void 0||typeof e==\"object\"&&e!==null&&Object.keys(e).length===0)&&(a+=\" You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.\");var v=tr(o);v?a+=v:a+=me();var c;e===null?c=\"null\":V(e)?c=\"array\":e!==void 0&&e.$$typeof===u?(c=\"<\"+(g(e.type)||\"Unknown\")+\" />\",a=\" Did you accidentally export a JSX literal instead of a component?\"):c=typeof e,d(\"React.jsx: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: %s.%s\",c,a)}var f=rr(e,r,t,o,s);if(f==null)return f;if(i){var b=r.children;if(b!==void 0)if(n)if(V(b)){for(var T=0;T<b.length;T++)_e(b[T],e);Object.freeze&&Object.freeze(b)}else d(\"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\");else _e(b,e)}return e===E?ir(f):ar(f),f}}var ur=or;G.Fragment=E,G.jsxDEV=ur})()});var xe=z((wr,Ce)=>{\"use strict\";Ce.exports=Te()});var _r={};vr(_r,{default:()=>hr,frontmatter:()=>mr});var p=br(xe()),mr={title:\"11 erreurs fr\\xE9quentes sur un site web\",excerpt:\"De nos jours, un site web est un outil indispensable pour toute entreprise souhaitant se d\\xE9marquer et atteindre ses objectifs. Cependant, cr\\xE9er un site web performant peut \\xEAtre un v\\xE9ritable challenge. Il existe des erreurs fr\\xE9quentes qui peuvent nuire \\xE0 votre site.\",date:\"2025-05-17\",updated:\"2025-05-20\",category:\"D\\xE9veloppement web\",cover:\"/images/blog/11-erreurs-site-web/main-erreurs-site-web.webp\",published:!1};function Oe(l){let u=Object.assign({p:\"p\",h2:\"h2\",strong:\"strong\"},l.components);return(0,p.jsxDEV)(p.Fragment,{children:[(0,p.jsxDEV)(u.p,{children:\"De nos jours, un site web est un outil indispensable pour toute entreprise souhaitant se d\\xE9marquer et atteindre ses objectifs. Cependant, cr\\xE9er un site web performant peut \\xEAtre un v\\xE9ritable challenge. Il existe des erreurs fr\\xE9quentes qui peuvent nuire \\xE0 votre site.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-1031abaf-b6c1-4686-946d-22493bc29c07.mdx\",lineNumber:13,columnNumber:1},this),`\n`,(0,p.jsxDEV)(u.p,{children:\"Dans cet article, nous vous pr\\xE9senterons 11 fr\\xE9quentes sur un site web et nous vous donnerons des conseils pour les \\xE9viter.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-1031abaf-b6c1-4686-946d-22493bc29c07.mdx\",lineNumber:15,columnNumber:1},this),`\n`,(0,p.jsxDEV)(u.h2,{children:\"1. Utiliser des polices d\\u2019\\xE9criture peu lisibles\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-1031abaf-b6c1-4686-946d-22493bc29c07.mdx\",lineNumber:17,columnNumber:1},this),`\n`,(0,p.jsxDEV)(u.p,{children:[\"Il est tentant d\\u2019utiliser des polices d\\u2019\\xE9criture originales pour se d\\xE9marquer, mais cela peut \",(0,p.jsxDEV)(u.strong,{children:\"nuire \\xE0 la lisibilit\\xE9\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-1031abaf-b6c1-4686-946d-22493bc29c07.mdx\",lineNumber:19,columnNumber:95},this),\" de votre site. La lisibilit\\xE9 est cruciale pour permettre \\xE0 vos visiteurs de comprendre rapidement et facilement le message que vous souhaitez transmettre. En utilisant des polices extravagantes, vous risquez non seulement de perdre l\\u2019attention de vos visiteurs, mais \\xE9galement de donner une image peu professionnelle de votre entreprise.\"]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-1031abaf-b6c1-4686-946d-22493bc29c07.mdx\",lineNumber:19,columnNumber:1},this)]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-1031abaf-b6c1-4686-946d-22493bc29c07.mdx\",lineNumber:1,columnNumber:1},this)}function gr(l={}){let{wrapper:u}=l.components||{};return u?(0,p.jsxDEV)(u,Object.assign({},l,{children:(0,p.jsxDEV)(Oe,l,void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-1031abaf-b6c1-4686-946d-22493bc29c07.mdx\"},this)}),void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-1031abaf-b6c1-4686-946d-22493bc29c07.mdx\"},this):Oe(l)}var hr=gr;return pr(_r);})();\n/*! Bundled license information:\n\nreact/cjs/react-jsx-dev-runtime.development.js:\n  (**\n   * @license React\n   * react-jsx-dev-runtime.development.js\n   *\n   * Copyright (c) Facebook, Inc. and its affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *)\n*/\n;return Component;"}, "_id": "blog/fr/11-erreurs-site-web.mdx", "_raw": {"sourceFilePath": "blog/fr/11-erreurs-site-web.mdx", "sourceFileName": "11-erreurs-site-web.mdx", "sourceFileDir": "blog/fr", "contentType": "mdx", "flattenedPath": "blog/fr/11-erreurs-site-web"}, "type": "Post", "locale": "fr", "slug": "11-erreurs-site-web"}