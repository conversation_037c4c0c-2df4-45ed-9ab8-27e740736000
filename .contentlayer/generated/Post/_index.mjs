// NOTE This file is auto-generated by Contentlayer

import blog__en__11WebsiteErrorsMdx from './blog__en__11-website-errors.mdx.json' assert { type: 'json' }
import blog__en__6ReasonsWebsiteRedesignMdx from './blog__en__6-reasons-website-redesign.mdx.json' assert { type: 'json' }
import blog__en__logoCfMontreal_2023Mdx from './blog__en__logo-cf-montreal-2023.mdx.json' assert { type: 'json' }
import blog__en__logoDeezer_2023Mdx from './blog__en__logo-deezer-2023.mdx.json' assert { type: 'json' }
import blog__fr__11ErreursSiteWebMdx from './blog__fr__11-erreurs-site-web.mdx.json' assert { type: 'json' }
import blog__fr__6RaisonsRefonteSiteWebMdx from './blog__fr__6-raisons-refonte-site-web.mdx.json' assert { type: 'json' }
import blog__fr__logoCfMontreal_2023Mdx from './blog__fr__logo-cf-montreal-2023.mdx.json' assert { type: 'json' }
import blog__fr__logoDeezer_2023Mdx from './blog__fr__logo-deezer-2023.mdx.json' assert { type: 'json' }

export const allPosts = [blog__en__11WebsiteErrorsMdx, blog__en__6ReasonsWebsiteRedesignMdx, blog__en__logoCfMontreal_2023Mdx, blog__en__logoDeezer_2023Mdx, blog__fr__11ErreursSiteWebMdx, blog__fr__6RaisonsRefonteSiteWebMdx, blog__fr__logoCfMontreal_2023Mdx, blog__fr__logoDeezer_2023Mdx]
