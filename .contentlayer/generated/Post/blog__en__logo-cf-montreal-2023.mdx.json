{"title": "New CF Montréal logo: A symbol of renewal for 2023", "excerpt": "In 2023, CF Montréal announced the replacement of its snowflake-shaped logo with a new blue and black logo, featuring a fleur-de-lys at its center and a reference to the club's founding year, 1993.", "date": "2023-05-04T00:00:00.000Z", "updated": "2023-05-04T00:00:00.000Z", "category": "Branding", "cover": "/images/blog/logo-cf-montreal-2023/main-logo-cf-montreal-2023.png", "published": true, "body": {"raw": "\nCF Montréal supporters were waiting for it. The Montreal-based soccer club, which plays in Major League Soccer (MLS), unveiled a new logo in 2023, marking a major turning point in its visual identity. After the controversy sparked by the 2021 rebranding, this change is accompanied by the return of iconic symbols like the fleur-de-lys.\n\nWhy this change? What does this new logo symbolize? And how was it received by the fans? Let's break it down.\n\n## Back to the roots: A logo inspired by the Club's history\n\nThe **new CF Montréal** logo pays homage to the club's heritage by reintroducing elements familiar to long-time supporters. Designed as a traditional crest, it incorporates strong historical symbols of the Montreal Impact (the club's original name).\n\n<Image\n    src='/images/blog/logo-cf-montreal-2023/explication-logo-cf-montreal.webp'\n    alt=\"Explication du logo CF Montréal\"\n/>\n\n- The shield, reminiscent of the club's former logos\n- The fleur-de-lys, a symbol of Quebec that was once featured on the Impact's emblem\n- \"Impact\" blue returns as the logo's predominant color, accompanied by black\n- The black and blue stripes, recalling the club's first jerseys and its professional debut in the 1990s\n- \"1993,\" the year of the club's inaugural season\n\nIn addition to the official name **CF Montréal** being prominently displayed, all these elements give the crest a meaningful identity, rooted in the club's history and culture while still looking toward the future.\n\n\n## Why did CF Montréal changed its logo in 2023?\nThe change of the CF Montréal logo in 2023 is primarily explained by the desire to **course-correct** after the controversial rebranding of 2021.\n\nAs a reminder, on January 14, 2021, the management at the time announced that the Montreal Impact would be changing its name and visual identity to become \"Club de Foot Montréal.\" This radical rebranding—a new name and a snowflake-shaped logo—sparked strong opposition from a segment of supporters and the Quebec soccer community.\n\nMany long-standing fans did not identify with this new emblem, considering it too disconnected from the club's history and Montreal's identity.\n\nFaced with this negative reaction, the leadership decided to listen to the voice of the supporters. The arrival of a new president in 2022, Gabriel Gervais (himself a former player for the club), marked a turning point. Gervais publicly acknowledged that it was **difficult to identify with the former logo**: \"Where is the fleur-de-lys? Where is the blue color? How are we represented?\" he questioned, highlighting the absence of traditional symbols. This admission brought to light the **branding mistake** made in 2021. Thus, less than two years after the previous redesign, CF Montréal chose to change its logo again in order to **realign its visual identity with its original DNA**.\n<Tweet id=\"1521993350240473091\" />\n\nIn practical terms, this reversal was a direct response to supporter expectations. \"We heard them loud and clear,\" declared owner Joey Saputo, referring to the fans.\n\n\nThe club, therefore, accepted the need to **walk back** certain aesthetic choices to **mend the bond** with its fanbase. This 2023 logo change aims to unite the community after a period of division and controversy. It is a humble step from the organization, admitting that the previous identity was not unanimously accepted and attempting to \"mend fences\" with its most loyal fans.\n<Image\n    src='/images/blog/logo-cf-montreal-2023/comparaison-logo-cf-montreal.webp'\n    alt=\"Comparaisons de l'évolution du logo CF Montréal\"\n/>\n\n## Generally positive reactions\n\nThe unveiling of the new logo was well-received. On social media, in specialized media outlets, and within supporter groups, the return of the blue and the fleur-de-lys was praised.\n\n\nHowever, some fans were hoping for a complete return to the \"Impact\" name. This will not be the case. Gabriel Gervais confirmed: **the name CF Montréal will remain.**\n\nNevertheless, the club is open to honoring the Impact's legacy in other ways: a symbolic name in the stadium, visual references, and nods in its communications.\n\nOverall, this new identity is seen as a balanced compromise between the old and the new. It gives the club an image that is more aligned with its community.\n## Conclusion\n\nThis identity change is possibly part of a broader trend in MLS, where several clubs have recently undergone logo redesigns to better reflect their identity (one might think of the rebrands for the Columbus Crew, Chicago Fire, etc.). But in Montreal's case, the move was primarily guided by a local need: to reconnect the club with its fanbase and its heritage.", "code": "var Component=(()=>{var cn=Object.create;var D=Object.defineProperty;var fn=Object.getOwnPropertyDescriptor;var dn=Object.getOwnPropertyNames;var bn=Object.getPrototypeOf,mn=Object.prototype.hasOwnProperty;var z=(u,t)=>()=>(t||u((t={exports:{}}).exports,t),t.exports),hn=(u,t)=>{for(var b in t)D(u,b,{get:t[b],enumerable:!0})},we=(u,t,b,p)=>{if(t&&typeof t==\"object\"||typeof t==\"function\")for(let y of dn(t))!mn.call(u,y)&&y!==b&&D(u,y,{get:()=>t[y],enumerable:!(p=fn(t,y))||p.enumerable});return u};var gn=(u,t,b)=>(b=u!=null?cn(bn(u)):{},we(t||!u||!u.__esModule?D(b,\"default\",{value:u,enumerable:!0}):b,u)),pn=u=>we(D({},\"__esModule\",{value:!0}),u);var xe=z((xn,Ne)=>{Ne.exports=React});var Ee=z(q=>{\"use strict\";(function(){\"use strict\";var u=xe(),t=Symbol.for(\"react.element\"),b=Symbol.for(\"react.portal\"),p=Symbol.for(\"react.fragment\"),y=Symbol.for(\"react.strict_mode\"),H=Symbol.for(\"react.profiler\"),X=Symbol.for(\"react.provider\"),K=Symbol.for(\"react.context\"),T=Symbol.for(\"react.forward_ref\"),M=Symbol.for(\"react.suspense\"),A=Symbol.for(\"react.suspense_list\"),C=Symbol.for(\"react.memo\"),I=Symbol.for(\"react.lazy\"),Oe=Symbol.for(\"react.offscreen\"),J=Symbol.iterator,Se=\"@@iterator\";function Pe(e){if(e===null||typeof e!=\"object\")return null;var n=J&&e[J]||e[Se];return typeof n==\"function\"?n:null}var N=u.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;function m(e){{for(var n=arguments.length,r=new Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];je(\"error\",e,r)}}function je(e,n,r){{var i=N.ReactDebugCurrentFrame,s=i.getStackAddendum();s!==\"\"&&(n+=\"%s\",r=r.concat([s]));var c=r.map(function(l){return String(l)});c.unshift(\"Warning: \"+n),Function.prototype.apply.call(console[e],console,c)}}var Fe=!1,De=!1,Me=!1,Ae=!1,Ie=!1,Q;Q=Symbol.for(\"react.module.reference\");function We(e){return!!(typeof e==\"string\"||typeof e==\"function\"||e===p||e===H||Ie||e===y||e===M||e===A||Ae||e===Oe||Fe||De||Me||typeof e==\"object\"&&e!==null&&(e.$$typeof===I||e.$$typeof===C||e.$$typeof===X||e.$$typeof===K||e.$$typeof===T||e.$$typeof===Q||e.getModuleId!==void 0))}function Ye(e,n,r){var i=e.displayName;if(i)return i;var s=n.displayName||n.name||\"\";return s!==\"\"?r+\"(\"+s+\")\":r}function Z(e){return e.displayName||\"Context\"}function _(e){if(e==null)return null;if(typeof e.tag==\"number\"&&m(\"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"),typeof e==\"function\")return e.displayName||e.name||null;if(typeof e==\"string\")return e;switch(e){case p:return\"Fragment\";case b:return\"Portal\";case H:return\"Profiler\";case y:return\"StrictMode\";case M:return\"Suspense\";case A:return\"SuspenseList\"}if(typeof e==\"object\")switch(e.$$typeof){case K:var n=e;return Z(n)+\".Consumer\";case X:var r=e;return Z(r._context)+\".Provider\";case T:return Ye(e,e.render,\"ForwardRef\");case C:var i=e.displayName||null;return i!==null?i:_(e.type)||\"Memo\";case I:{var s=e,c=s._payload,l=s._init;try{return _(l(c))}catch{return null}}}return null}var w=Object.assign,k=0,ee,ne,re,te,ie,ae,oe;function le(){}le.__reactDisabledLog=!0;function $e(){{if(k===0){ee=console.log,ne=console.info,re=console.warn,te=console.error,ie=console.group,ae=console.groupCollapsed,oe=console.groupEnd;var e={configurable:!0,enumerable:!0,value:le,writable:!0};Object.defineProperties(console,{info:e,log:e,warn:e,error:e,group:e,groupCollapsed:e,groupEnd:e})}k++}}function Le(){{if(k--,k===0){var e={configurable:!0,enumerable:!0,writable:!0};Object.defineProperties(console,{log:w({},e,{value:ee}),info:w({},e,{value:ne}),warn:w({},e,{value:re}),error:w({},e,{value:te}),group:w({},e,{value:ie}),groupCollapsed:w({},e,{value:ae}),groupEnd:w({},e,{value:oe})})}k<0&&m(\"disabledDepth fell below zero. This is a bug in React. Please file an issue.\")}}var W=N.ReactCurrentDispatcher,Y;function O(e,n,r){{if(Y===void 0)try{throw Error()}catch(s){var i=s.stack.trim().match(/\\n( *(at )?)/);Y=i&&i[1]||\"\"}return`\n`+Y+e}}var $=!1,S;{var Ve=typeof WeakMap==\"function\"?WeakMap:Map;S=new Ve}function se(e,n){if(!e||$)return\"\";{var r=S.get(e);if(r!==void 0)return r}var i;$=!0;var s=Error.prepareStackTrace;Error.prepareStackTrace=void 0;var c;c=W.current,W.current=null,$e();try{if(n){var l=function(){throw Error()};if(Object.defineProperty(l.prototype,\"props\",{set:function(){throw Error()}}),typeof Reflect==\"object\"&&Reflect.construct){try{Reflect.construct(l,[])}catch(v){i=v}Reflect.construct(e,[],l)}else{try{l.call()}catch(v){i=v}e.call(l.prototype)}}else{try{throw Error()}catch(v){i=v}e()}}catch(v){if(v&&i&&typeof v.stack==\"string\"){for(var o=v.stack.split(`\n`),h=i.stack.split(`\n`),f=o.length-1,d=h.length-1;f>=1&&d>=0&&o[f]!==h[d];)d--;for(;f>=1&&d>=0;f--,d--)if(o[f]!==h[d]){if(f!==1||d!==1)do if(f--,d--,d<0||o[f]!==h[d]){var g=`\n`+o[f].replace(\" at new \",\" at \");return e.displayName&&g.includes(\"<anonymous>\")&&(g=g.replace(\"<anonymous>\",e.displayName)),typeof e==\"function\"&&S.set(e,g),g}while(f>=1&&d>=0);break}}}finally{$=!1,W.current=c,Le(),Error.prepareStackTrace=s}var E=e?e.displayName||e.name:\"\",ye=E?O(E):\"\";return typeof e==\"function\"&&S.set(e,ye),ye}function Ue(e,n,r){return se(e,!1)}function Ge(e){var n=e.prototype;return!!(n&&n.isReactComponent)}function P(e,n,r){if(e==null)return\"\";if(typeof e==\"function\")return se(e,Ge(e));if(typeof e==\"string\")return O(e);switch(e){case M:return O(\"Suspense\");case A:return O(\"SuspenseList\")}if(typeof e==\"object\")switch(e.$$typeof){case T:return Ue(e.render);case C:return P(e.type,n,r);case I:{var i=e,s=i._payload,c=i._init;try{return P(c(s),n,r)}catch{}}}return\"\"}var j=Object.prototype.hasOwnProperty,ue={},ce=N.ReactDebugCurrentFrame;function F(e){if(e){var n=e._owner,r=P(e.type,e._source,n?n.type:null);ce.setExtraStackFrame(r)}else ce.setExtraStackFrame(null)}function Be(e,n,r,i,s){{var c=Function.call.bind(j);for(var l in e)if(c(e,l)){var o=void 0;try{if(typeof e[l]!=\"function\"){var h=Error((i||\"React class\")+\": \"+r+\" type `\"+l+\"` is invalid; it must be a function, usually from the `prop-types` package, but received `\"+typeof e[l]+\"`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.\");throw h.name=\"Invariant Violation\",h}o=e[l](n,l,i,r,null,\"SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED\")}catch(f){o=f}o&&!(o instanceof Error)&&(F(s),m(\"%s: type specification of %s `%s` is invalid; the type checker function must return `null` or an `Error` but returned a %s. You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument).\",i||\"React class\",r,l,typeof o),F(null)),o instanceof Error&&!(o.message in ue)&&(ue[o.message]=!0,F(s),m(\"Failed %s type: %s\",r,o.message),F(null))}}}var ze=Array.isArray;function L(e){return ze(e)}function qe(e){{var n=typeof Symbol==\"function\"&&Symbol.toStringTag,r=n&&e[Symbol.toStringTag]||e.constructor.name||\"Object\";return r}}function He(e){try{return fe(e),!1}catch{return!0}}function fe(e){return\"\"+e}function de(e){if(He(e))return m(\"The provided key is an unsupported type %s. This value must be coerced to a string before before using it here.\",qe(e)),fe(e)}var R=N.ReactCurrentOwner,Xe={key:!0,ref:!0,__self:!0,__source:!0},be,me,V;V={};function Ke(e){if(j.call(e,\"ref\")){var n=Object.getOwnPropertyDescriptor(e,\"ref\").get;if(n&&n.isReactWarning)return!1}return e.ref!==void 0}function Je(e){if(j.call(e,\"key\")){var n=Object.getOwnPropertyDescriptor(e,\"key\").get;if(n&&n.isReactWarning)return!1}return e.key!==void 0}function Qe(e,n){if(typeof e.ref==\"string\"&&R.current&&n&&R.current.stateNode!==n){var r=_(R.current.type);V[r]||(m('Component \"%s\" contains the string ref \"%s\". Support for string refs will be removed in a future major release. This case cannot be automatically converted to an arrow function. We ask you to manually fix this case by using useRef() or createRef() instead. Learn more about using refs safely here: https://reactjs.org/link/strict-mode-string-ref',_(R.current.type),e.ref),V[r]=!0)}}function Ze(e,n){{var r=function(){be||(be=!0,m(\"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)\",n))};r.isReactWarning=!0,Object.defineProperty(e,\"key\",{get:r,configurable:!0})}}function en(e,n){{var r=function(){me||(me=!0,m(\"%s: `ref` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)\",n))};r.isReactWarning=!0,Object.defineProperty(e,\"ref\",{get:r,configurable:!0})}}var nn=function(e,n,r,i,s,c,l){var o={$$typeof:t,type:e,key:n,ref:r,props:l,_owner:c};return o._store={},Object.defineProperty(o._store,\"validated\",{configurable:!1,enumerable:!1,writable:!0,value:!1}),Object.defineProperty(o,\"_self\",{configurable:!1,enumerable:!1,writable:!1,value:i}),Object.defineProperty(o,\"_source\",{configurable:!1,enumerable:!1,writable:!1,value:s}),Object.freeze&&(Object.freeze(o.props),Object.freeze(o)),o};function rn(e,n,r,i,s){{var c,l={},o=null,h=null;r!==void 0&&(de(r),o=\"\"+r),Je(n)&&(de(n.key),o=\"\"+n.key),Ke(n)&&(h=n.ref,Qe(n,s));for(c in n)j.call(n,c)&&!Xe.hasOwnProperty(c)&&(l[c]=n[c]);if(e&&e.defaultProps){var f=e.defaultProps;for(c in f)l[c]===void 0&&(l[c]=f[c])}if(o||h){var d=typeof e==\"function\"?e.displayName||e.name||\"Unknown\":e;o&&Ze(l,d),h&&en(l,d)}return nn(e,o,h,s,i,R.current,l)}}var U=N.ReactCurrentOwner,he=N.ReactDebugCurrentFrame;function x(e){if(e){var n=e._owner,r=P(e.type,e._source,n?n.type:null);he.setExtraStackFrame(r)}else he.setExtraStackFrame(null)}var G;G=!1;function B(e){return typeof e==\"object\"&&e!==null&&e.$$typeof===t}function ge(){{if(U.current){var e=_(U.current.type);if(e)return`\n\nCheck the render method of \\``+e+\"`.\"}return\"\"}}function tn(e){{if(e!==void 0){var n=e.fileName.replace(/^.*[\\\\\\/]/,\"\"),r=e.lineNumber;return`\n\nCheck your code at `+n+\":\"+r+\".\"}return\"\"}}var pe={};function an(e){{var n=ge();if(!n){var r=typeof e==\"string\"?e:e.displayName||e.name;r&&(n=`\n\nCheck the top-level render call using <`+r+\">.\")}return n}}function _e(e,n){{if(!e._store||e._store.validated||e.key!=null)return;e._store.validated=!0;var r=an(n);if(pe[r])return;pe[r]=!0;var i=\"\";e&&e._owner&&e._owner!==U.current&&(i=\" It was passed a child from \"+_(e._owner.type)+\".\"),x(e),m('Each child in a list should have a unique \"key\" prop.%s%s See https://reactjs.org/link/warning-keys for more information.',r,i),x(null)}}function ve(e,n){{if(typeof e!=\"object\")return;if(L(e))for(var r=0;r<e.length;r++){var i=e[r];B(i)&&_e(i,n)}else if(B(e))e._store&&(e._store.validated=!0);else if(e){var s=Pe(e);if(typeof s==\"function\"&&s!==e.entries)for(var c=s.call(e),l;!(l=c.next()).done;)B(l.value)&&_e(l.value,n)}}}function on(e){{var n=e.type;if(n==null||typeof n==\"string\")return;var r;if(typeof n==\"function\")r=n.propTypes;else if(typeof n==\"object\"&&(n.$$typeof===T||n.$$typeof===C))r=n.propTypes;else return;if(r){var i=_(n);Be(r,e.props,\"prop\",i,e)}else if(n.PropTypes!==void 0&&!G){G=!0;var s=_(n);m(\"Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?\",s||\"Unknown\")}typeof n.getDefaultProps==\"function\"&&!n.getDefaultProps.isReactClassApproved&&m(\"getDefaultProps is only used on classic React.createClass definitions. Use a static property named `defaultProps` instead.\")}}function ln(e){{for(var n=Object.keys(e.props),r=0;r<n.length;r++){var i=n[r];if(i!==\"children\"&&i!==\"key\"){x(e),m(\"Invalid prop `%s` supplied to `React.Fragment`. React.Fragment can only have `key` and `children` props.\",i),x(null);break}}e.ref!==null&&(x(e),m(\"Invalid attribute `ref` supplied to `React.Fragment`.\"),x(null))}}function sn(e,n,r,i,s,c){{var l=We(e);if(!l){var o=\"\";(e===void 0||typeof e==\"object\"&&e!==null&&Object.keys(e).length===0)&&(o+=\" You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.\");var h=tn(s);h?o+=h:o+=ge();var f;e===null?f=\"null\":L(e)?f=\"array\":e!==void 0&&e.$$typeof===t?(f=\"<\"+(_(e.type)||\"Unknown\")+\" />\",o=\" Did you accidentally export a JSX literal instead of a component?\"):f=typeof e,m(\"React.jsx: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: %s.%s\",f,o)}var d=rn(e,n,r,s,c);if(d==null)return d;if(l){var g=n.children;if(g!==void 0)if(i)if(L(g)){for(var E=0;E<g.length;E++)ve(g[E],e);Object.freeze&&Object.freeze(g)}else m(\"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\");else ve(g,e)}return e===p?ln(d):on(d),d}}var un=sn;q.Fragment=p,q.jsxDEV=un})()});var Re=z((kn,ke)=>{\"use strict\";ke.exports=Ee()});var wn={};hn(wn,{default:()=>yn,frontmatter:()=>_n});var a=gn(Re()),_n={title:\"New CF Montr\\xE9al logo: A symbol of renewal for 2023\",excerpt:\"In 2023, CF Montr\\xE9al announced the replacement of its snowflake-shaped logo with a new blue and black logo, featuring a fleur-de-lys at its center and a reference to the club's founding year, 1993.\",date:\"2023-05-04\",updated:\"2023-05-04\",category:\"Branding\",cover:\"/images/blog/logo-cf-montreal-2023/main-logo-cf-montreal-2023.png\"};function Te(u){let t=Object.assign({p:\"p\",h2:\"h2\",strong:\"strong\",ul:\"ul\",li:\"li\"},u.components),{Image:b,Tweet:p}=t;return b||Ce(\"Image\",!0,\"18:1-21:3\"),p||Ce(\"Tweet\",!0,\"40:1-40:35\"),(0,a.jsxDEV)(a.Fragment,{children:[(0,a.jsxDEV)(t.p,{children:\"CF Montr\\xE9al supporters were waiting for it. The Montreal-based soccer club, which plays in Major League Soccer (MLS), unveiled a new logo in 2023, marking a major turning point in its visual identity. After the controversy sparked by the 2021 rebranding, this change is accompanied by the return of iconic symbols like the fleur-de-lys.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-f935c489-a3db-4e48-9835-e693b5bf9082.mdx\",lineNumber:10,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:\"Why this change? What does this new logo symbolize? And how was it received by the fans? Let's break it down.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-f935c489-a3db-4e48-9835-e693b5bf9082.mdx\",lineNumber:12,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.h2,{children:\"Back to the roots: A logo inspired by the Club's history\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-f935c489-a3db-4e48-9835-e693b5bf9082.mdx\",lineNumber:14,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:[\"The \",(0,a.jsxDEV)(t.strong,{children:\"new CF Montr\\xE9al\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-f935c489-a3db-4e48-9835-e693b5bf9082.mdx\",lineNumber:16,columnNumber:5},this),\" logo pays homage to the club's heritage by reintroducing elements familiar to long-time supporters. Designed as a traditional crest, it incorporates strong historical symbols of the Montreal Impact (the club's original name).\"]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-f935c489-a3db-4e48-9835-e693b5bf9082.mdx\",lineNumber:16,columnNumber:1},this),`\n`,(0,a.jsxDEV)(b,{src:\"/images/blog/logo-cf-montreal-2023/explication-logo-cf-montreal.webp\",alt:\"Explication du logo CF Montr\\xE9al\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-f935c489-a3db-4e48-9835-e693b5bf9082.mdx\",lineNumber:18,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.ul,{children:[`\n`,(0,a.jsxDEV)(t.li,{children:\"The shield, reminiscent of the club's former logos\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-f935c489-a3db-4e48-9835-e693b5bf9082.mdx\",lineNumber:23,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.li,{children:\"The fleur-de-lys, a symbol of Quebec that was once featured on the Impact's emblem\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-f935c489-a3db-4e48-9835-e693b5bf9082.mdx\",lineNumber:24,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.li,{children:`\"Impact\" blue returns as the logo's predominant color, accompanied by black`},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-f935c489-a3db-4e48-9835-e693b5bf9082.mdx\",lineNumber:25,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.li,{children:\"The black and blue stripes, recalling the club's first jerseys and its professional debut in the 1990s\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-f935c489-a3db-4e48-9835-e693b5bf9082.mdx\",lineNumber:26,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.li,{children:`\"1993,\" the year of the club's inaugural season`},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-f935c489-a3db-4e48-9835-e693b5bf9082.mdx\",lineNumber:27,columnNumber:1},this),`\n`]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-f935c489-a3db-4e48-9835-e693b5bf9082.mdx\",lineNumber:23,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:[\"In addition to the official name \",(0,a.jsxDEV)(t.strong,{children:\"CF Montr\\xE9al\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-f935c489-a3db-4e48-9835-e693b5bf9082.mdx\",lineNumber:29,columnNumber:34},this),\" being prominently displayed, all these elements give the crest a meaningful identity, rooted in the club's history and culture while still looking toward the future.\"]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-f935c489-a3db-4e48-9835-e693b5bf9082.mdx\",lineNumber:29,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.h2,{children:\"Why did CF Montr\\xE9al changed its logo in 2023?\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-f935c489-a3db-4e48-9835-e693b5bf9082.mdx\",lineNumber:32,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:[\"The change of the CF Montr\\xE9al logo in 2023 is primarily explained by the desire to \",(0,a.jsxDEV)(t.strong,{children:\"course-correct\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-f935c489-a3db-4e48-9835-e693b5bf9082.mdx\",lineNumber:33,columnNumber:84},this),\" after the controversial rebranding of 2021.\"]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-f935c489-a3db-4e48-9835-e693b5bf9082.mdx\",lineNumber:33,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:'As a reminder, on January 14, 2021, the management at the time announced that the Montreal Impact would be changing its name and visual identity to become \"Club de Foot Montr\\xE9al.\" This radical rebranding\\u2014a new name and a snowflake-shaped logo\\u2014sparked strong opposition from a segment of supporters and the Quebec soccer community.'},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-f935c489-a3db-4e48-9835-e693b5bf9082.mdx\",lineNumber:35,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:\"Many long-standing fans did not identify with this new emblem, considering it too disconnected from the club's history and Montreal's identity.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-f935c489-a3db-4e48-9835-e693b5bf9082.mdx\",lineNumber:37,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:[\"Faced with this negative reaction, the leadership decided to listen to the voice of the supporters. The arrival of a new president in 2022, Gabriel Gervais (himself a former player for the club), marked a turning point. Gervais publicly acknowledged that it was \",(0,a.jsxDEV)(t.strong,{children:\"difficult to identify with the former logo\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-f935c489-a3db-4e48-9835-e693b5bf9082.mdx\",lineNumber:39,columnNumber:263},this),': \"Where is the fleur-de-lys? Where is the blue color? How are we represented?\" he questioned, highlighting the absence of traditional symbols. This admission brought to light the ',(0,a.jsxDEV)(t.strong,{children:\"branding mistake\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-f935c489-a3db-4e48-9835-e693b5bf9082.mdx\",lineNumber:39,columnNumber:489},this),\" made in 2021. Thus, less than two years after the previous redesign, CF Montr\\xE9al chose to change its logo again in order to \",(0,a.jsxDEV)(t.strong,{children:\"realign its visual identity with its original DNA\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-f935c489-a3db-4e48-9835-e693b5bf9082.mdx\",lineNumber:39,columnNumber:634},this),\".\"]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-f935c489-a3db-4e48-9835-e693b5bf9082.mdx\",lineNumber:39,columnNumber:1},this),`\n`,(0,a.jsxDEV)(p,{id:\"1521993350240473091\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-f935c489-a3db-4e48-9835-e693b5bf9082.mdx\",lineNumber:40,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:'In practical terms, this reversal was a direct response to supporter expectations. \"We heard them loud and clear,\" declared owner Joey Saputo, referring to the fans.'},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-f935c489-a3db-4e48-9835-e693b5bf9082.mdx\",lineNumber:42,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:[\"The club, therefore, accepted the need to \",(0,a.jsxDEV)(t.strong,{children:\"walk back\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-f935c489-a3db-4e48-9835-e693b5bf9082.mdx\",lineNumber:45,columnNumber:43},this),\" certain aesthetic choices to \",(0,a.jsxDEV)(t.strong,{children:\"mend the bond\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-f935c489-a3db-4e48-9835-e693b5bf9082.mdx\",lineNumber:45,columnNumber:86},this),' with its fanbase. This 2023 logo change aims to unite the community after a period of division and controversy. It is a humble step from the organization, admitting that the previous identity was not unanimously accepted and attempting to \"mend fences\" with its most loyal fans.']},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-f935c489-a3db-4e48-9835-e693b5bf9082.mdx\",lineNumber:45,columnNumber:1},this),`\n`,(0,a.jsxDEV)(b,{src:\"/images/blog/logo-cf-montreal-2023/comparaison-logo-cf-montreal.webp\",alt:\"Comparaisons de l'\\xE9volution du logo CF Montr\\xE9al\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-f935c489-a3db-4e48-9835-e693b5bf9082.mdx\",lineNumber:46,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.h2,{children:\"Generally positive reactions\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-f935c489-a3db-4e48-9835-e693b5bf9082.mdx\",lineNumber:51,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:\"The unveiling of the new logo was well-received. On social media, in specialized media outlets, and within supporter groups, the return of the blue and the fleur-de-lys was praised.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-f935c489-a3db-4e48-9835-e693b5bf9082.mdx\",lineNumber:53,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:['However, some fans were hoping for a complete return to the \"Impact\" name. This will not be the case. Gabriel Gervais confirmed: ',(0,a.jsxDEV)(t.strong,{children:\"the name CF Montr\\xE9al will remain.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-f935c489-a3db-4e48-9835-e693b5bf9082.mdx\",lineNumber:56,columnNumber:130},this)]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-f935c489-a3db-4e48-9835-e693b5bf9082.mdx\",lineNumber:56,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:\"Nevertheless, the club is open to honoring the Impact's legacy in other ways: a symbolic name in the stadium, visual references, and nods in its communications.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-f935c489-a3db-4e48-9835-e693b5bf9082.mdx\",lineNumber:58,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:\"Overall, this new identity is seen as a balanced compromise between the old and the new. It gives the club an image that is more aligned with its community.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-f935c489-a3db-4e48-9835-e693b5bf9082.mdx\",lineNumber:60,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.h2,{children:\"Conclusion\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-f935c489-a3db-4e48-9835-e693b5bf9082.mdx\",lineNumber:61,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:\"This identity change is possibly part of a broader trend in MLS, where several clubs have recently undergone logo redesigns to better reflect their identity (one might think of the rebrands for the Columbus Crew, Chicago Fire, etc.). But in Montreal's case, the move was primarily guided by a local need: to reconnect the club with its fanbase and its heritage.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-f935c489-a3db-4e48-9835-e693b5bf9082.mdx\",lineNumber:63,columnNumber:1},this)]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-f935c489-a3db-4e48-9835-e693b5bf9082.mdx\",lineNumber:1,columnNumber:1},this)}function vn(u={}){let{wrapper:t}=u.components||{};return t?(0,a.jsxDEV)(t,Object.assign({},u,{children:(0,a.jsxDEV)(Te,u,void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-f935c489-a3db-4e48-9835-e693b5bf9082.mdx\"},this)}),void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-f935c489-a3db-4e48-9835-e693b5bf9082.mdx\"},this):Te(u)}var yn=vn;function Ce(u,t,b){throw new Error(\"Expected \"+(t?\"component\":\"object\")+\" `\"+u+\"` to be defined: you likely forgot to import, pass, or provide it.\"+(b?\"\\nIt\\u2019s referenced in your code at `\"+b+\"` in `/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-f935c489-a3db-4e48-9835-e693b5bf9082.mdx`\":\"\"))}return pn(wn);})();\n/*! Bundled license information:\n\nreact/cjs/react-jsx-dev-runtime.development.js:\n  (**\n   * @license React\n   * react-jsx-dev-runtime.development.js\n   *\n   * Copyright (c) Facebook, Inc. and its affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *)\n*/\n;return Component;"}, "_id": "blog/en/logo-cf-montreal-2023.mdx", "_raw": {"sourceFilePath": "blog/en/logo-cf-montreal-2023.mdx", "sourceFileName": "logo-cf-montreal-2023.mdx", "sourceFileDir": "blog/en", "contentType": "mdx", "flattenedPath": "blog/en/logo-cf-montreal-2023"}, "type": "Post", "locale": "en", "slug": "logo-cf-montreal-2023"}