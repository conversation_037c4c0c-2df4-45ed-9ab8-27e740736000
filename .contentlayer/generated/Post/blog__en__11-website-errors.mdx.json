{"title": "11 Common Website Errors", "excerpt": "Nowadays, a website is an essential tool for any business looking to stand out and achieve its goals. However, creating a high-performing website can be a real challenge. There are common errors that can harm your site.", "date": "2025-05-17T00:00:00.000Z", "updated": "2025-05-20T00:00:00.000Z", "category": "Web Development", "cover": "/images/blog/11-erreurs-site-web/main-erreurs-site-web.webp", "published": false, "body": {"raw": "\nNowadays, a website is an essential tool for any business looking to stand out and achieve its goals. However, creating a high-performing website can be a real challenge. There are common errors that can harm your site.\n\nIn this article, we'll present 11 common website errors and give you tips to avoid them.\n\n## 1. Using Hard-to-Read Fonts\n\nIt's tempting to use original fonts to stand out, but this can **harm your site's readability**. Readability is crucial for allowing your visitors to quickly and easily understand the message you want to convey. By using extravagant fonts, you risk not only losing your visitors' attention but also giving an unprofessional image of your business.\n", "code": "var Component=(()=>{var ur=Object.create;var N=Object.defineProperty;var lr=Object.getOwnPropertyDescriptor;var cr=Object.getOwnPropertyNames;var fr=Object.getPrototypeOf,dr=Object.prototype.hasOwnProperty;var G=(l,s)=>()=>(s||l((s={exports:{}}).exports,s),s.exports),vr=(l,s)=>{for(var g in s)N(l,g,{get:s[g],enumerable:!0})},Ee=(l,s,g,_)=>{if(s&&typeof s==\"object\"||typeof s==\"function\")for(let y of cr(s))!dr.call(l,y)&&y!==g&&N(l,y,{get:()=>s[y],enumerable:!(_=lr(s,y))||_.enumerable});return l};var br=(l,s,g)=>(g=l!=null?ur(fr(l)):{},Ee(s||!l||!l.__esModule?N(g,\"default\",{value:l,enumerable:!0}):g,l)),mr=l=>Ee(N({},\"__esModule\",{value:!0}),l);var we=G((Er,Re)=>{Re.exports=React});var Te=G(H=>{\"use strict\";(function(){\"use strict\";var l=we(),s=Symbol.for(\"react.element\"),g=Symbol.for(\"react.portal\"),_=Symbol.for(\"react.fragment\"),y=Symbol.for(\"react.strict_mode\"),z=Symbol.for(\"react.profiler\"),X=Symbol.for(\"react.provider\"),K=Symbol.for(\"react.context\"),O=Symbol.for(\"react.forward_ref\"),A=Symbol.for(\"react.suspense\"),I=Symbol.for(\"react.suspense_list\"),P=Symbol.for(\"react.memo\"),W=Symbol.for(\"react.lazy\"),Pe=Symbol.for(\"react.offscreen\"),J=Symbol.iterator,Se=\"@@iterator\";function ke(e){if(e===null||typeof e!=\"object\")return null;var r=J&&e[J]||e[Se];return typeof r==\"function\"?r:null}var R=l.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;function d(e){{for(var r=arguments.length,t=new Array(r>1?r-1:0),n=1;n<r;n++)t[n-1]=arguments[n];je(\"error\",e,t)}}function je(e,r,t){{var n=R.ReactDebugCurrentFrame,i=n.getStackAddendum();i!==\"\"&&(r+=\"%s\",t=t.concat([i]));var u=t.map(function(o){return String(o)});u.unshift(\"Warning: \"+r),Function.prototype.apply.call(console[e],console,u)}}var De=!1,Fe=!1,Ne=!1,Ae=!1,Ie=!1,Z;Z=Symbol.for(\"react.module.reference\");function We(e){return!!(typeof e==\"string\"||typeof e==\"function\"||e===_||e===z||Ie||e===y||e===A||e===I||Ae||e===Pe||De||Fe||Ne||typeof e==\"object\"&&e!==null&&(e.$$typeof===W||e.$$typeof===P||e.$$typeof===X||e.$$typeof===K||e.$$typeof===O||e.$$typeof===Z||e.getModuleId!==void 0))}function Ye(e,r,t){var n=e.displayName;if(n)return n;var i=r.displayName||r.name||\"\";return i!==\"\"?t+\"(\"+i+\")\":t}function Q(e){return e.displayName||\"Context\"}function p(e){if(e==null)return null;if(typeof e.tag==\"number\"&&d(\"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"),typeof e==\"function\")return e.displayName||e.name||null;if(typeof e==\"string\")return e;switch(e){case _:return\"Fragment\";case g:return\"Portal\";case z:return\"Profiler\";case y:return\"StrictMode\";case A:return\"Suspense\";case I:return\"SuspenseList\"}if(typeof e==\"object\")switch(e.$$typeof){case K:var r=e;return Q(r)+\".Consumer\";case X:var t=e;return Q(t._context)+\".Provider\";case O:return Ye(e,e.render,\"ForwardRef\");case P:var n=e.displayName||null;return n!==null?n:p(e.type)||\"Memo\";case W:{var i=e,u=i._payload,o=i._init;try{return p(o(u))}catch{return null}}}return null}var E=Object.assign,C=0,ee,re,te,ne,ae,oe,ie;function se(){}se.__reactDisabledLog=!0;function $e(){{if(C===0){ee=console.log,re=console.info,te=console.warn,ne=console.error,ae=console.group,oe=console.groupCollapsed,ie=console.groupEnd;var e={configurable:!0,enumerable:!0,value:se,writable:!0};Object.defineProperties(console,{info:e,log:e,warn:e,error:e,group:e,groupCollapsed:e,groupEnd:e})}C++}}function Me(){{if(C--,C===0){var e={configurable:!0,enumerable:!0,writable:!0};Object.defineProperties(console,{log:E({},e,{value:ee}),info:E({},e,{value:re}),warn:E({},e,{value:te}),error:E({},e,{value:ne}),group:E({},e,{value:ae}),groupCollapsed:E({},e,{value:oe}),groupEnd:E({},e,{value:ie})})}C<0&&d(\"disabledDepth fell below zero. This is a bug in React. Please file an issue.\")}}var Y=R.ReactCurrentDispatcher,$;function S(e,r,t){{if($===void 0)try{throw Error()}catch(i){var n=i.stack.trim().match(/\\n( *(at )?)/);$=n&&n[1]||\"\"}return`\n`+$+e}}var M=!1,k;{var Ve=typeof WeakMap==\"function\"?WeakMap:Map;k=new Ve}function ue(e,r){if(!e||M)return\"\";{var t=k.get(e);if(t!==void 0)return t}var n;M=!0;var i=Error.prepareStackTrace;Error.prepareStackTrace=void 0;var u;u=Y.current,Y.current=null,$e();try{if(r){var o=function(){throw Error()};if(Object.defineProperty(o.prototype,\"props\",{set:function(){throw Error()}}),typeof Reflect==\"object\"&&Reflect.construct){try{Reflect.construct(o,[])}catch(h){n=h}Reflect.construct(e,[],o)}else{try{o.call()}catch(h){n=h}e.call(o.prototype)}}else{try{throw Error()}catch(h){n=h}e()}}catch(h){if(h&&n&&typeof h.stack==\"string\"){for(var a=h.stack.split(`\n`),v=n.stack.split(`\n`),c=a.length-1,f=v.length-1;c>=1&&f>=0&&a[c]!==v[f];)f--;for(;c>=1&&f>=0;c--,f--)if(a[c]!==v[f]){if(c!==1||f!==1)do if(c--,f--,f<0||a[c]!==v[f]){var b=`\n`+a[c].replace(\" at new \",\" at \");return e.displayName&&b.includes(\"<anonymous>\")&&(b=b.replace(\"<anonymous>\",e.displayName)),typeof e==\"function\"&&k.set(e,b),b}while(c>=1&&f>=0);break}}}finally{M=!1,Y.current=u,Me(),Error.prepareStackTrace=i}var T=e?e.displayName||e.name:\"\",_e=T?S(T):\"\";return typeof e==\"function\"&&k.set(e,_e),_e}function Le(e,r,t){return ue(e,!1)}function Ue(e){var r=e.prototype;return!!(r&&r.isReactComponent)}function j(e,r,t){if(e==null)return\"\";if(typeof e==\"function\")return ue(e,Ue(e));if(typeof e==\"string\")return S(e);switch(e){case A:return S(\"Suspense\");case I:return S(\"SuspenseList\")}if(typeof e==\"object\")switch(e.$$typeof){case O:return Le(e.render);case P:return j(e.type,r,t);case W:{var n=e,i=n._payload,u=n._init;try{return j(u(i),r,t)}catch{}}}return\"\"}var D=Object.prototype.hasOwnProperty,le={},ce=R.ReactDebugCurrentFrame;function F(e){if(e){var r=e._owner,t=j(e.type,e._source,r?r.type:null);ce.setExtraStackFrame(t)}else ce.setExtraStackFrame(null)}function Be(e,r,t,n,i){{var u=Function.call.bind(D);for(var o in e)if(u(e,o)){var a=void 0;try{if(typeof e[o]!=\"function\"){var v=Error((n||\"React class\")+\": \"+t+\" type `\"+o+\"` is invalid; it must be a function, usually from the `prop-types` package, but received `\"+typeof e[o]+\"`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.\");throw v.name=\"Invariant Violation\",v}a=e[o](r,o,n,t,null,\"SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED\")}catch(c){a=c}a&&!(a instanceof Error)&&(F(i),d(\"%s: type specification of %s `%s` is invalid; the type checker function must return `null` or an `Error` but returned a %s. You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument).\",n||\"React class\",t,o,typeof a),F(null)),a instanceof Error&&!(a.message in le)&&(le[a.message]=!0,F(i),d(\"Failed %s type: %s\",t,a.message),F(null))}}}var qe=Array.isArray;function V(e){return qe(e)}function Ge(e){{var r=typeof Symbol==\"function\"&&Symbol.toStringTag,t=r&&e[Symbol.toStringTag]||e.constructor.name||\"Object\";return t}}function He(e){try{return fe(e),!1}catch{return!0}}function fe(e){return\"\"+e}function de(e){if(He(e))return d(\"The provided key is an unsupported type %s. This value must be coerced to a string before before using it here.\",Ge(e)),fe(e)}var x=R.ReactCurrentOwner,ze={key:!0,ref:!0,__self:!0,__source:!0},ve,be,L;L={};function Xe(e){if(D.call(e,\"ref\")){var r=Object.getOwnPropertyDescriptor(e,\"ref\").get;if(r&&r.isReactWarning)return!1}return e.ref!==void 0}function Ke(e){if(D.call(e,\"key\")){var r=Object.getOwnPropertyDescriptor(e,\"key\").get;if(r&&r.isReactWarning)return!1}return e.key!==void 0}function Je(e,r){if(typeof e.ref==\"string\"&&x.current&&r&&x.current.stateNode!==r){var t=p(x.current.type);L[t]||(d('Component \"%s\" contains the string ref \"%s\". Support for string refs will be removed in a future major release. This case cannot be automatically converted to an arrow function. We ask you to manually fix this case by using useRef() or createRef() instead. Learn more about using refs safely here: https://reactjs.org/link/strict-mode-string-ref',p(x.current.type),e.ref),L[t]=!0)}}function Ze(e,r){{var t=function(){ve||(ve=!0,d(\"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)\",r))};t.isReactWarning=!0,Object.defineProperty(e,\"key\",{get:t,configurable:!0})}}function Qe(e,r){{var t=function(){be||(be=!0,d(\"%s: `ref` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)\",r))};t.isReactWarning=!0,Object.defineProperty(e,\"ref\",{get:t,configurable:!0})}}var er=function(e,r,t,n,i,u,o){var a={$$typeof:s,type:e,key:r,ref:t,props:o,_owner:u};return a._store={},Object.defineProperty(a._store,\"validated\",{configurable:!1,enumerable:!1,writable:!0,value:!1}),Object.defineProperty(a,\"_self\",{configurable:!1,enumerable:!1,writable:!1,value:n}),Object.defineProperty(a,\"_source\",{configurable:!1,enumerable:!1,writable:!1,value:i}),Object.freeze&&(Object.freeze(a.props),Object.freeze(a)),a};function rr(e,r,t,n,i){{var u,o={},a=null,v=null;t!==void 0&&(de(t),a=\"\"+t),Ke(r)&&(de(r.key),a=\"\"+r.key),Xe(r)&&(v=r.ref,Je(r,i));for(u in r)D.call(r,u)&&!ze.hasOwnProperty(u)&&(o[u]=r[u]);if(e&&e.defaultProps){var c=e.defaultProps;for(u in c)o[u]===void 0&&(o[u]=c[u])}if(a||v){var f=typeof e==\"function\"?e.displayName||e.name||\"Unknown\":e;a&&Ze(o,f),v&&Qe(o,f)}return er(e,a,v,i,n,x.current,o)}}var U=R.ReactCurrentOwner,me=R.ReactDebugCurrentFrame;function w(e){if(e){var r=e._owner,t=j(e.type,e._source,r?r.type:null);me.setExtraStackFrame(t)}else me.setExtraStackFrame(null)}var B;B=!1;function q(e){return typeof e==\"object\"&&e!==null&&e.$$typeof===s}function ge(){{if(U.current){var e=p(U.current.type);if(e)return`\n\nCheck the render method of \\``+e+\"`.\"}return\"\"}}function tr(e){{if(e!==void 0){var r=e.fileName.replace(/^.*[\\\\\\/]/,\"\"),t=e.lineNumber;return`\n\nCheck your code at `+r+\":\"+t+\".\"}return\"\"}}var pe={};function nr(e){{var r=ge();if(!r){var t=typeof e==\"string\"?e:e.displayName||e.name;t&&(r=`\n\nCheck the top-level render call using <`+t+\">.\")}return r}}function he(e,r){{if(!e._store||e._store.validated||e.key!=null)return;e._store.validated=!0;var t=nr(r);if(pe[t])return;pe[t]=!0;var n=\"\";e&&e._owner&&e._owner!==U.current&&(n=\" It was passed a child from \"+p(e._owner.type)+\".\"),w(e),d('Each child in a list should have a unique \"key\" prop.%s%s See https://reactjs.org/link/warning-keys for more information.',t,n),w(null)}}function ye(e,r){{if(typeof e!=\"object\")return;if(V(e))for(var t=0;t<e.length;t++){var n=e[t];q(n)&&he(n,r)}else if(q(e))e._store&&(e._store.validated=!0);else if(e){var i=ke(e);if(typeof i==\"function\"&&i!==e.entries)for(var u=i.call(e),o;!(o=u.next()).done;)q(o.value)&&he(o.value,r)}}}function ar(e){{var r=e.type;if(r==null||typeof r==\"string\")return;var t;if(typeof r==\"function\")t=r.propTypes;else if(typeof r==\"object\"&&(r.$$typeof===O||r.$$typeof===P))t=r.propTypes;else return;if(t){var n=p(r);Be(t,e.props,\"prop\",n,e)}else if(r.PropTypes!==void 0&&!B){B=!0;var i=p(r);d(\"Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?\",i||\"Unknown\")}typeof r.getDefaultProps==\"function\"&&!r.getDefaultProps.isReactClassApproved&&d(\"getDefaultProps is only used on classic React.createClass definitions. Use a static property named `defaultProps` instead.\")}}function or(e){{for(var r=Object.keys(e.props),t=0;t<r.length;t++){var n=r[t];if(n!==\"children\"&&n!==\"key\"){w(e),d(\"Invalid prop `%s` supplied to `React.Fragment`. React.Fragment can only have `key` and `children` props.\",n),w(null);break}}e.ref!==null&&(w(e),d(\"Invalid attribute `ref` supplied to `React.Fragment`.\"),w(null))}}function ir(e,r,t,n,i,u){{var o=We(e);if(!o){var a=\"\";(e===void 0||typeof e==\"object\"&&e!==null&&Object.keys(e).length===0)&&(a+=\" You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.\");var v=tr(i);v?a+=v:a+=ge();var c;e===null?c=\"null\":V(e)?c=\"array\":e!==void 0&&e.$$typeof===s?(c=\"<\"+(p(e.type)||\"Unknown\")+\" />\",a=\" Did you accidentally export a JSX literal instead of a component?\"):c=typeof e,d(\"React.jsx: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: %s.%s\",c,a)}var f=rr(e,r,t,i,u);if(f==null)return f;if(o){var b=r.children;if(b!==void 0)if(n)if(V(b)){for(var T=0;T<b.length;T++)ye(b[T],e);Object.freeze&&Object.freeze(b)}else d(\"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\");else ye(b,e)}return e===_?or(f):ar(f),f}}var sr=ir;H.Fragment=_,H.jsxDEV=sr})()});var xe=G((wr,Ce)=>{\"use strict\";Ce.exports=Te()});var yr={};vr(yr,{default:()=>hr,frontmatter:()=>gr});var m=br(xe()),gr={title:\"11 Common Website Errors\",excerpt:\"Nowadays, a website is an essential tool for any business looking to stand out and achieve its goals. However, creating a high-performing website can be a real challenge. There are common errors that can harm your site.\",date:\"2025-05-17\",updated:\"2025-05-20\",category:\"Web Development\",cover:\"/images/blog/11-erreurs-site-web/main-erreurs-site-web.webp\",published:!1};function Oe(l){let s=Object.assign({p:\"p\",h2:\"h2\",strong:\"strong\"},l.components);return(0,m.jsxDEV)(m.Fragment,{children:[(0,m.jsxDEV)(s.p,{children:\"Nowadays, a website is an essential tool for any business looking to stand out and achieve its goals. However, creating a high-performing website can be a real challenge. There are common errors that can harm your site.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-99089168-578c-4c65-9fa5-352383269f61.mdx\",lineNumber:11,columnNumber:1},this),`\n`,(0,m.jsxDEV)(s.p,{children:\"In this article, we'll present 11 common website errors and give you tips to avoid them.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-99089168-578c-4c65-9fa5-352383269f61.mdx\",lineNumber:13,columnNumber:1},this),`\n`,(0,m.jsxDEV)(s.h2,{children:\"1. Using Hard-to-Read Fonts\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-99089168-578c-4c65-9fa5-352383269f61.mdx\",lineNumber:15,columnNumber:1},this),`\n`,(0,m.jsxDEV)(s.p,{children:[\"It's tempting to use original fonts to stand out, but this can \",(0,m.jsxDEV)(s.strong,{children:\"harm your site's readability\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-99089168-578c-4c65-9fa5-352383269f61.mdx\",lineNumber:17,columnNumber:64},this),\". Readability is crucial for allowing your visitors to quickly and easily understand the message you want to convey. By using extravagant fonts, you risk not only losing your visitors' attention but also giving an unprofessional image of your business.\"]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-99089168-578c-4c65-9fa5-352383269f61.mdx\",lineNumber:17,columnNumber:1},this)]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-99089168-578c-4c65-9fa5-352383269f61.mdx\",lineNumber:1,columnNumber:1},this)}function pr(l={}){let{wrapper:s}=l.components||{};return s?(0,m.jsxDEV)(s,Object.assign({},l,{children:(0,m.jsxDEV)(Oe,l,void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-99089168-578c-4c65-9fa5-352383269f61.mdx\"},this)}),void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-99089168-578c-4c65-9fa5-352383269f61.mdx\"},this):Oe(l)}var hr=pr;return mr(yr);})();\n/*! Bundled license information:\n\nreact/cjs/react-jsx-dev-runtime.development.js:\n  (**\n   * @license React\n   * react-jsx-dev-runtime.development.js\n   *\n   * Copyright (c) Facebook, Inc. and its affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *)\n*/\n;return Component;"}, "_id": "blog/en/11-website-errors.mdx", "_raw": {"sourceFilePath": "blog/en/11-website-errors.mdx", "sourceFileName": "11-website-errors.mdx", "sourceFileDir": "blog/en", "contentType": "mdx", "flattenedPath": "blog/en/11-website-errors"}, "type": "Post", "locale": "en", "slug": "11-website-errors"}