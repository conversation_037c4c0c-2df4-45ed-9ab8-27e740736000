[{"title": "11 Common Website Errors", "excerpt": "Nowadays, a website is an essential tool for any business looking to stand out and achieve its goals. However, creating a high-performing website can be a real challenge. There are common errors that can harm your site.", "date": "2025-05-17T00:00:00.000Z", "updated": "2025-05-20T00:00:00.000Z", "category": "Web Development", "cover": "/images/blog/11-erreurs-site-web/main-erreurs-site-web.webp", "published": false, "body": {"raw": "\nNowadays, a website is an essential tool for any business looking to stand out and achieve its goals. However, creating a high-performing website can be a real challenge. There are common errors that can harm your site.\n\nIn this article, we'll present 11 common website errors and give you tips to avoid them.\n\n## 1. Using Hard-to-Read Fonts\n\nIt's tempting to use original fonts to stand out, but this can **harm your site's readability**. Readability is crucial for allowing your visitors to quickly and easily understand the message you want to convey. By using extravagant fonts, you risk not only losing your visitors' attention but also giving an unprofessional image of your business.\n", "code": "var Component=(()=>{var ur=Object.create;var N=Object.defineProperty;var lr=Object.getOwnPropertyDescriptor;var cr=Object.getOwnPropertyNames;var fr=Object.getPrototypeOf,dr=Object.prototype.hasOwnProperty;var G=(l,s)=>()=>(s||l((s={exports:{}}).exports,s),s.exports),vr=(l,s)=>{for(var g in s)N(l,g,{get:s[g],enumerable:!0})},Ee=(l,s,g,_)=>{if(s&&typeof s==\"object\"||typeof s==\"function\")for(let y of cr(s))!dr.call(l,y)&&y!==g&&N(l,y,{get:()=>s[y],enumerable:!(_=lr(s,y))||_.enumerable});return l};var br=(l,s,g)=>(g=l!=null?ur(fr(l)):{},Ee(s||!l||!l.__esModule?N(g,\"default\",{value:l,enumerable:!0}):g,l)),mr=l=>Ee(N({},\"__esModule\",{value:!0}),l);var we=G((Er,Re)=>{Re.exports=React});var Te=G(H=>{\"use strict\";(function(){\"use strict\";var l=we(),s=Symbol.for(\"react.element\"),g=Symbol.for(\"react.portal\"),_=Symbol.for(\"react.fragment\"),y=Symbol.for(\"react.strict_mode\"),z=Symbol.for(\"react.profiler\"),X=Symbol.for(\"react.provider\"),K=Symbol.for(\"react.context\"),O=Symbol.for(\"react.forward_ref\"),A=Symbol.for(\"react.suspense\"),I=Symbol.for(\"react.suspense_list\"),P=Symbol.for(\"react.memo\"),W=Symbol.for(\"react.lazy\"),Pe=Symbol.for(\"react.offscreen\"),J=Symbol.iterator,Se=\"@@iterator\";function ke(e){if(e===null||typeof e!=\"object\")return null;var r=J&&e[J]||e[Se];return typeof r==\"function\"?r:null}var R=l.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;function d(e){{for(var r=arguments.length,t=new Array(r>1?r-1:0),n=1;n<r;n++)t[n-1]=arguments[n];je(\"error\",e,t)}}function je(e,r,t){{var n=R.ReactDebugCurrentFrame,i=n.getStackAddendum();i!==\"\"&&(r+=\"%s\",t=t.concat([i]));var u=t.map(function(o){return String(o)});u.unshift(\"Warning: \"+r),Function.prototype.apply.call(console[e],console,u)}}var De=!1,Fe=!1,Ne=!1,Ae=!1,Ie=!1,Z;Z=Symbol.for(\"react.module.reference\");function We(e){return!!(typeof e==\"string\"||typeof e==\"function\"||e===_||e===z||Ie||e===y||e===A||e===I||Ae||e===Pe||De||Fe||Ne||typeof e==\"object\"&&e!==null&&(e.$$typeof===W||e.$$typeof===P||e.$$typeof===X||e.$$typeof===K||e.$$typeof===O||e.$$typeof===Z||e.getModuleId!==void 0))}function Ye(e,r,t){var n=e.displayName;if(n)return n;var i=r.displayName||r.name||\"\";return i!==\"\"?t+\"(\"+i+\")\":t}function Q(e){return e.displayName||\"Context\"}function p(e){if(e==null)return null;if(typeof e.tag==\"number\"&&d(\"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"),typeof e==\"function\")return e.displayName||e.name||null;if(typeof e==\"string\")return e;switch(e){case _:return\"Fragment\";case g:return\"Portal\";case z:return\"Profiler\";case y:return\"StrictMode\";case A:return\"Suspense\";case I:return\"SuspenseList\"}if(typeof e==\"object\")switch(e.$$typeof){case K:var r=e;return Q(r)+\".Consumer\";case X:var t=e;return Q(t._context)+\".Provider\";case O:return Ye(e,e.render,\"ForwardRef\");case P:var n=e.displayName||null;return n!==null?n:p(e.type)||\"Memo\";case W:{var i=e,u=i._payload,o=i._init;try{return p(o(u))}catch{return null}}}return null}var E=Object.assign,C=0,ee,re,te,ne,ae,oe,ie;function se(){}se.__reactDisabledLog=!0;function $e(){{if(C===0){ee=console.log,re=console.info,te=console.warn,ne=console.error,ae=console.group,oe=console.groupCollapsed,ie=console.groupEnd;var e={configurable:!0,enumerable:!0,value:se,writable:!0};Object.defineProperties(console,{info:e,log:e,warn:e,error:e,group:e,groupCollapsed:e,groupEnd:e})}C++}}function Me(){{if(C--,C===0){var e={configurable:!0,enumerable:!0,writable:!0};Object.defineProperties(console,{log:E({},e,{value:ee}),info:E({},e,{value:re}),warn:E({},e,{value:te}),error:E({},e,{value:ne}),group:E({},e,{value:ae}),groupCollapsed:E({},e,{value:oe}),groupEnd:E({},e,{value:ie})})}C<0&&d(\"disabledDepth fell below zero. This is a bug in React. Please file an issue.\")}}var Y=R.ReactCurrentDispatcher,$;function S(e,r,t){{if($===void 0)try{throw Error()}catch(i){var n=i.stack.trim().match(/\\n( *(at )?)/);$=n&&n[1]||\"\"}return`\n`+$+e}}var M=!1,k;{var Ve=typeof WeakMap==\"function\"?WeakMap:Map;k=new Ve}function ue(e,r){if(!e||M)return\"\";{var t=k.get(e);if(t!==void 0)return t}var n;M=!0;var i=Error.prepareStackTrace;Error.prepareStackTrace=void 0;var u;u=Y.current,Y.current=null,$e();try{if(r){var o=function(){throw Error()};if(Object.defineProperty(o.prototype,\"props\",{set:function(){throw Error()}}),typeof Reflect==\"object\"&&Reflect.construct){try{Reflect.construct(o,[])}catch(h){n=h}Reflect.construct(e,[],o)}else{try{o.call()}catch(h){n=h}e.call(o.prototype)}}else{try{throw Error()}catch(h){n=h}e()}}catch(h){if(h&&n&&typeof h.stack==\"string\"){for(var a=h.stack.split(`\n`),v=n.stack.split(`\n`),c=a.length-1,f=v.length-1;c>=1&&f>=0&&a[c]!==v[f];)f--;for(;c>=1&&f>=0;c--,f--)if(a[c]!==v[f]){if(c!==1||f!==1)do if(c--,f--,f<0||a[c]!==v[f]){var b=`\n`+a[c].replace(\" at new \",\" at \");return e.displayName&&b.includes(\"<anonymous>\")&&(b=b.replace(\"<anonymous>\",e.displayName)),typeof e==\"function\"&&k.set(e,b),b}while(c>=1&&f>=0);break}}}finally{M=!1,Y.current=u,Me(),Error.prepareStackTrace=i}var T=e?e.displayName||e.name:\"\",_e=T?S(T):\"\";return typeof e==\"function\"&&k.set(e,_e),_e}function Le(e,r,t){return ue(e,!1)}function Ue(e){var r=e.prototype;return!!(r&&r.isReactComponent)}function j(e,r,t){if(e==null)return\"\";if(typeof e==\"function\")return ue(e,Ue(e));if(typeof e==\"string\")return S(e);switch(e){case A:return S(\"Suspense\");case I:return S(\"SuspenseList\")}if(typeof e==\"object\")switch(e.$$typeof){case O:return Le(e.render);case P:return j(e.type,r,t);case W:{var n=e,i=n._payload,u=n._init;try{return j(u(i),r,t)}catch{}}}return\"\"}var D=Object.prototype.hasOwnProperty,le={},ce=R.ReactDebugCurrentFrame;function F(e){if(e){var r=e._owner,t=j(e.type,e._source,r?r.type:null);ce.setExtraStackFrame(t)}else ce.setExtraStackFrame(null)}function Be(e,r,t,n,i){{var u=Function.call.bind(D);for(var o in e)if(u(e,o)){var a=void 0;try{if(typeof e[o]!=\"function\"){var v=Error((n||\"React class\")+\": \"+t+\" type `\"+o+\"` is invalid; it must be a function, usually from the `prop-types` package, but received `\"+typeof e[o]+\"`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.\");throw v.name=\"Invariant Violation\",v}a=e[o](r,o,n,t,null,\"SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED\")}catch(c){a=c}a&&!(a instanceof Error)&&(F(i),d(\"%s: type specification of %s `%s` is invalid; the type checker function must return `null` or an `Error` but returned a %s. You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument).\",n||\"React class\",t,o,typeof a),F(null)),a instanceof Error&&!(a.message in le)&&(le[a.message]=!0,F(i),d(\"Failed %s type: %s\",t,a.message),F(null))}}}var qe=Array.isArray;function V(e){return qe(e)}function Ge(e){{var r=typeof Symbol==\"function\"&&Symbol.toStringTag,t=r&&e[Symbol.toStringTag]||e.constructor.name||\"Object\";return t}}function He(e){try{return fe(e),!1}catch{return!0}}function fe(e){return\"\"+e}function de(e){if(He(e))return d(\"The provided key is an unsupported type %s. This value must be coerced to a string before before using it here.\",Ge(e)),fe(e)}var x=R.ReactCurrentOwner,ze={key:!0,ref:!0,__self:!0,__source:!0},ve,be,L;L={};function Xe(e){if(D.call(e,\"ref\")){var r=Object.getOwnPropertyDescriptor(e,\"ref\").get;if(r&&r.isReactWarning)return!1}return e.ref!==void 0}function Ke(e){if(D.call(e,\"key\")){var r=Object.getOwnPropertyDescriptor(e,\"key\").get;if(r&&r.isReactWarning)return!1}return e.key!==void 0}function Je(e,r){if(typeof e.ref==\"string\"&&x.current&&r&&x.current.stateNode!==r){var t=p(x.current.type);L[t]||(d('Component \"%s\" contains the string ref \"%s\". Support for string refs will be removed in a future major release. This case cannot be automatically converted to an arrow function. We ask you to manually fix this case by using useRef() or createRef() instead. Learn more about using refs safely here: https://reactjs.org/link/strict-mode-string-ref',p(x.current.type),e.ref),L[t]=!0)}}function Ze(e,r){{var t=function(){ve||(ve=!0,d(\"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)\",r))};t.isReactWarning=!0,Object.defineProperty(e,\"key\",{get:t,configurable:!0})}}function Qe(e,r){{var t=function(){be||(be=!0,d(\"%s: `ref` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)\",r))};t.isReactWarning=!0,Object.defineProperty(e,\"ref\",{get:t,configurable:!0})}}var er=function(e,r,t,n,i,u,o){var a={$$typeof:s,type:e,key:r,ref:t,props:o,_owner:u};return a._store={},Object.defineProperty(a._store,\"validated\",{configurable:!1,enumerable:!1,writable:!0,value:!1}),Object.defineProperty(a,\"_self\",{configurable:!1,enumerable:!1,writable:!1,value:n}),Object.defineProperty(a,\"_source\",{configurable:!1,enumerable:!1,writable:!1,value:i}),Object.freeze&&(Object.freeze(a.props),Object.freeze(a)),a};function rr(e,r,t,n,i){{var u,o={},a=null,v=null;t!==void 0&&(de(t),a=\"\"+t),Ke(r)&&(de(r.key),a=\"\"+r.key),Xe(r)&&(v=r.ref,Je(r,i));for(u in r)D.call(r,u)&&!ze.hasOwnProperty(u)&&(o[u]=r[u]);if(e&&e.defaultProps){var c=e.defaultProps;for(u in c)o[u]===void 0&&(o[u]=c[u])}if(a||v){var f=typeof e==\"function\"?e.displayName||e.name||\"Unknown\":e;a&&Ze(o,f),v&&Qe(o,f)}return er(e,a,v,i,n,x.current,o)}}var U=R.ReactCurrentOwner,me=R.ReactDebugCurrentFrame;function w(e){if(e){var r=e._owner,t=j(e.type,e._source,r?r.type:null);me.setExtraStackFrame(t)}else me.setExtraStackFrame(null)}var B;B=!1;function q(e){return typeof e==\"object\"&&e!==null&&e.$$typeof===s}function ge(){{if(U.current){var e=p(U.current.type);if(e)return`\n\nCheck the render method of \\``+e+\"`.\"}return\"\"}}function tr(e){{if(e!==void 0){var r=e.fileName.replace(/^.*[\\\\\\/]/,\"\"),t=e.lineNumber;return`\n\nCheck your code at `+r+\":\"+t+\".\"}return\"\"}}var pe={};function nr(e){{var r=ge();if(!r){var t=typeof e==\"string\"?e:e.displayName||e.name;t&&(r=`\n\nCheck the top-level render call using <`+t+\">.\")}return r}}function he(e,r){{if(!e._store||e._store.validated||e.key!=null)return;e._store.validated=!0;var t=nr(r);if(pe[t])return;pe[t]=!0;var n=\"\";e&&e._owner&&e._owner!==U.current&&(n=\" It was passed a child from \"+p(e._owner.type)+\".\"),w(e),d('Each child in a list should have a unique \"key\" prop.%s%s See https://reactjs.org/link/warning-keys for more information.',t,n),w(null)}}function ye(e,r){{if(typeof e!=\"object\")return;if(V(e))for(var t=0;t<e.length;t++){var n=e[t];q(n)&&he(n,r)}else if(q(e))e._store&&(e._store.validated=!0);else if(e){var i=ke(e);if(typeof i==\"function\"&&i!==e.entries)for(var u=i.call(e),o;!(o=u.next()).done;)q(o.value)&&he(o.value,r)}}}function ar(e){{var r=e.type;if(r==null||typeof r==\"string\")return;var t;if(typeof r==\"function\")t=r.propTypes;else if(typeof r==\"object\"&&(r.$$typeof===O||r.$$typeof===P))t=r.propTypes;else return;if(t){var n=p(r);Be(t,e.props,\"prop\",n,e)}else if(r.PropTypes!==void 0&&!B){B=!0;var i=p(r);d(\"Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?\",i||\"Unknown\")}typeof r.getDefaultProps==\"function\"&&!r.getDefaultProps.isReactClassApproved&&d(\"getDefaultProps is only used on classic React.createClass definitions. Use a static property named `defaultProps` instead.\")}}function or(e){{for(var r=Object.keys(e.props),t=0;t<r.length;t++){var n=r[t];if(n!==\"children\"&&n!==\"key\"){w(e),d(\"Invalid prop `%s` supplied to `React.Fragment`. React.Fragment can only have `key` and `children` props.\",n),w(null);break}}e.ref!==null&&(w(e),d(\"Invalid attribute `ref` supplied to `React.Fragment`.\"),w(null))}}function ir(e,r,t,n,i,u){{var o=We(e);if(!o){var a=\"\";(e===void 0||typeof e==\"object\"&&e!==null&&Object.keys(e).length===0)&&(a+=\" You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.\");var v=tr(i);v?a+=v:a+=ge();var c;e===null?c=\"null\":V(e)?c=\"array\":e!==void 0&&e.$$typeof===s?(c=\"<\"+(p(e.type)||\"Unknown\")+\" />\",a=\" Did you accidentally export a JSX literal instead of a component?\"):c=typeof e,d(\"React.jsx: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: %s.%s\",c,a)}var f=rr(e,r,t,i,u);if(f==null)return f;if(o){var b=r.children;if(b!==void 0)if(n)if(V(b)){for(var T=0;T<b.length;T++)ye(b[T],e);Object.freeze&&Object.freeze(b)}else d(\"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\");else ye(b,e)}return e===_?or(f):ar(f),f}}var sr=ir;H.Fragment=_,H.jsxDEV=sr})()});var xe=G((wr,Ce)=>{\"use strict\";Ce.exports=Te()});var yr={};vr(yr,{default:()=>hr,frontmatter:()=>gr});var m=br(xe()),gr={title:\"11 Common Website Errors\",excerpt:\"Nowadays, a website is an essential tool for any business looking to stand out and achieve its goals. However, creating a high-performing website can be a real challenge. There are common errors that can harm your site.\",date:\"2025-05-17\",updated:\"2025-05-20\",category:\"Web Development\",cover:\"/images/blog/11-erreurs-site-web/main-erreurs-site-web.webp\",published:!1};function Oe(l){let s=Object.assign({p:\"p\",h2:\"h2\",strong:\"strong\"},l.components);return(0,m.jsxDEV)(m.Fragment,{children:[(0,m.jsxDEV)(s.p,{children:\"Nowadays, a website is an essential tool for any business looking to stand out and achieve its goals. However, creating a high-performing website can be a real challenge. There are common errors that can harm your site.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-99089168-578c-4c65-9fa5-352383269f61.mdx\",lineNumber:11,columnNumber:1},this),`\n`,(0,m.jsxDEV)(s.p,{children:\"In this article, we'll present 11 common website errors and give you tips to avoid them.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-99089168-578c-4c65-9fa5-352383269f61.mdx\",lineNumber:13,columnNumber:1},this),`\n`,(0,m.jsxDEV)(s.h2,{children:\"1. Using Hard-to-Read Fonts\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-99089168-578c-4c65-9fa5-352383269f61.mdx\",lineNumber:15,columnNumber:1},this),`\n`,(0,m.jsxDEV)(s.p,{children:[\"It's tempting to use original fonts to stand out, but this can \",(0,m.jsxDEV)(s.strong,{children:\"harm your site's readability\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-99089168-578c-4c65-9fa5-352383269f61.mdx\",lineNumber:17,columnNumber:64},this),\". Readability is crucial for allowing your visitors to quickly and easily understand the message you want to convey. By using extravagant fonts, you risk not only losing your visitors' attention but also giving an unprofessional image of your business.\"]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-99089168-578c-4c65-9fa5-352383269f61.mdx\",lineNumber:17,columnNumber:1},this)]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-99089168-578c-4c65-9fa5-352383269f61.mdx\",lineNumber:1,columnNumber:1},this)}function pr(l={}){let{wrapper:s}=l.components||{};return s?(0,m.jsxDEV)(s,Object.assign({},l,{children:(0,m.jsxDEV)(Oe,l,void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-99089168-578c-4c65-9fa5-352383269f61.mdx\"},this)}),void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-99089168-578c-4c65-9fa5-352383269f61.mdx\"},this):Oe(l)}var hr=pr;return mr(yr);})();\n/*! Bundled license information:\n\nreact/cjs/react-jsx-dev-runtime.development.js:\n  (**\n   * @license React\n   * react-jsx-dev-runtime.development.js\n   *\n   * Copyright (c) Facebook, Inc. and its affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *)\n*/\n;return Component;"}, "_id": "blog/en/11-website-errors.mdx", "_raw": {"sourceFilePath": "blog/en/11-website-errors.mdx", "sourceFileName": "11-website-errors.mdx", "sourceFileDir": "blog/en", "contentType": "mdx", "flattenedPath": "blog/en/11-website-errors"}, "type": "Post", "locale": "en", "slug": "11-website-errors"}, {"title": "6 Reasons to Know if Your Website Needs a Redesign", "excerpt": "If you've been using the same website for a while or if it was created back when responsive design didn't exist yet, there's a good chance your site needs to be reworked.", "date": "2025-05-17T00:00:00.000Z", "updated": "2025-05-20T00:00:00.000Z", "category": "Web Development", "cover": "/images/blog/6-raisons-refonte-site-web/main-refonte-site-web.webp", "published": false, "body": {"raw": "\nIf you've been using the same website for a while or if it was created back when responsive design didn't exist yet, there's a good chance your site needs to be reworked.\n\nHere are 6 key reasons that indicate your website needs a redesign to stay competitive and effective in today's digital landscape.\n", "code": "var Component=(()=>{var sr=Object.create;var A=Object.defineProperty;var lr=Object.getOwnPropertyDescriptor;var cr=Object.getOwnPropertyNames;var fr=Object.getPrototypeOf,dr=Object.prototype.hasOwnProperty;var q=(l,s)=>()=>(s||l((s={exports:{}}).exports,s),s.exports),vr=(l,s)=>{for(var p in s)A(l,p,{get:s[p],enumerable:!0})},_e=(l,s,p,E)=>{if(s&&typeof s==\"object\"||typeof s==\"function\")for(let h of cr(s))!dr.call(l,h)&&h!==p&&A(l,h,{get:()=>s[h],enumerable:!(E=lr(s,h))||E.enumerable});return l};var br=(l,s,p)=>(p=l!=null?sr(fr(l)):{},_e(s||!l||!l.__esModule?A(p,\"default\",{value:l,enumerable:!0}):p,l)),pr=l=>_e(A({},\"__esModule\",{value:!0}),l);var we=q((_r,Re)=>{Re.exports=React});var Te=q(z=>{\"use strict\";(function(){\"use strict\";var l=we(),s=Symbol.for(\"react.element\"),p=Symbol.for(\"react.portal\"),E=Symbol.for(\"react.fragment\"),h=Symbol.for(\"react.strict_mode\"),K=Symbol.for(\"react.profiler\"),X=Symbol.for(\"react.provider\"),H=Symbol.for(\"react.context\"),P=Symbol.for(\"react.forward_ref\"),N=Symbol.for(\"react.suspense\"),I=Symbol.for(\"react.suspense_list\"),x=Symbol.for(\"react.memo\"),W=Symbol.for(\"react.lazy\"),xe=Symbol.for(\"react.offscreen\"),J=Symbol.iterator,Se=\"@@iterator\";function je(e){if(e===null||typeof e!=\"object\")return null;var r=J&&e[J]||e[Se];return typeof r==\"function\"?r:null}var R=l.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;function d(e){{for(var r=arguments.length,t=new Array(r>1?r-1:0),n=1;n<r;n++)t[n-1]=arguments[n];ke(\"error\",e,t)}}function ke(e,r,t){{var n=R.ReactDebugCurrentFrame,o=n.getStackAddendum();o!==\"\"&&(r+=\"%s\",t=t.concat([o]));var u=t.map(function(i){return String(i)});u.unshift(\"Warning: \"+r),Function.prototype.apply.call(console[e],console,u)}}var De=!1,Fe=!1,Ae=!1,Ne=!1,Ie=!1,Z;Z=Symbol.for(\"react.module.reference\");function We(e){return!!(typeof e==\"string\"||typeof e==\"function\"||e===E||e===K||Ie||e===h||e===N||e===I||Ne||e===xe||De||Fe||Ae||typeof e==\"object\"&&e!==null&&(e.$$typeof===W||e.$$typeof===x||e.$$typeof===X||e.$$typeof===H||e.$$typeof===P||e.$$typeof===Z||e.getModuleId!==void 0))}function Ye(e,r,t){var n=e.displayName;if(n)return n;var o=r.displayName||r.name||\"\";return o!==\"\"?t+\"(\"+o+\")\":t}function Q(e){return e.displayName||\"Context\"}function g(e){if(e==null)return null;if(typeof e.tag==\"number\"&&d(\"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"),typeof e==\"function\")return e.displayName||e.name||null;if(typeof e==\"string\")return e;switch(e){case E:return\"Fragment\";case p:return\"Portal\";case K:return\"Profiler\";case h:return\"StrictMode\";case N:return\"Suspense\";case I:return\"SuspenseList\"}if(typeof e==\"object\")switch(e.$$typeof){case H:var r=e;return Q(r)+\".Consumer\";case X:var t=e;return Q(t._context)+\".Provider\";case P:return Ye(e,e.render,\"ForwardRef\");case x:var n=e.displayName||null;return n!==null?n:g(e.type)||\"Memo\";case W:{var o=e,u=o._payload,i=o._init;try{return g(i(u))}catch{return null}}}return null}var _=Object.assign,C=0,ee,re,te,ne,ae,ie,oe;function ue(){}ue.__reactDisabledLog=!0;function $e(){{if(C===0){ee=console.log,re=console.info,te=console.warn,ne=console.error,ae=console.group,ie=console.groupCollapsed,oe=console.groupEnd;var e={configurable:!0,enumerable:!0,value:ue,writable:!0};Object.defineProperties(console,{info:e,log:e,warn:e,error:e,group:e,groupCollapsed:e,groupEnd:e})}C++}}function Me(){{if(C--,C===0){var e={configurable:!0,enumerable:!0,writable:!0};Object.defineProperties(console,{log:_({},e,{value:ee}),info:_({},e,{value:re}),warn:_({},e,{value:te}),error:_({},e,{value:ne}),group:_({},e,{value:ae}),groupCollapsed:_({},e,{value:ie}),groupEnd:_({},e,{value:oe})})}C<0&&d(\"disabledDepth fell below zero. This is a bug in React. Please file an issue.\")}}var Y=R.ReactCurrentDispatcher,$;function S(e,r,t){{if($===void 0)try{throw Error()}catch(o){var n=o.stack.trim().match(/\\n( *(at )?)/);$=n&&n[1]||\"\"}return`\n`+$+e}}var M=!1,j;{var Ve=typeof WeakMap==\"function\"?WeakMap:Map;j=new Ve}function se(e,r){if(!e||M)return\"\";{var t=j.get(e);if(t!==void 0)return t}var n;M=!0;var o=Error.prepareStackTrace;Error.prepareStackTrace=void 0;var u;u=Y.current,Y.current=null,$e();try{if(r){var i=function(){throw Error()};if(Object.defineProperty(i.prototype,\"props\",{set:function(){throw Error()}}),typeof Reflect==\"object\"&&Reflect.construct){try{Reflect.construct(i,[])}catch(m){n=m}Reflect.construct(e,[],i)}else{try{i.call()}catch(m){n=m}e.call(i.prototype)}}else{try{throw Error()}catch(m){n=m}e()}}catch(m){if(m&&n&&typeof m.stack==\"string\"){for(var a=m.stack.split(`\n`),v=n.stack.split(`\n`),c=a.length-1,f=v.length-1;c>=1&&f>=0&&a[c]!==v[f];)f--;for(;c>=1&&f>=0;c--,f--)if(a[c]!==v[f]){if(c!==1||f!==1)do if(c--,f--,f<0||a[c]!==v[f]){var b=`\n`+a[c].replace(\" at new \",\" at \");return e.displayName&&b.includes(\"<anonymous>\")&&(b=b.replace(\"<anonymous>\",e.displayName)),typeof e==\"function\"&&j.set(e,b),b}while(c>=1&&f>=0);break}}}finally{M=!1,Y.current=u,Me(),Error.prepareStackTrace=o}var T=e?e.displayName||e.name:\"\",Ee=T?S(T):\"\";return typeof e==\"function\"&&j.set(e,Ee),Ee}function Le(e,r,t){return se(e,!1)}function Ue(e){var r=e.prototype;return!!(r&&r.isReactComponent)}function k(e,r,t){if(e==null)return\"\";if(typeof e==\"function\")return se(e,Ue(e));if(typeof e==\"string\")return S(e);switch(e){case N:return S(\"Suspense\");case I:return S(\"SuspenseList\")}if(typeof e==\"object\")switch(e.$$typeof){case P:return Le(e.render);case x:return k(e.type,r,t);case W:{var n=e,o=n._payload,u=n._init;try{return k(u(o),r,t)}catch{}}}return\"\"}var D=Object.prototype.hasOwnProperty,le={},ce=R.ReactDebugCurrentFrame;function F(e){if(e){var r=e._owner,t=k(e.type,e._source,r?r.type:null);ce.setExtraStackFrame(t)}else ce.setExtraStackFrame(null)}function Be(e,r,t,n,o){{var u=Function.call.bind(D);for(var i in e)if(u(e,i)){var a=void 0;try{if(typeof e[i]!=\"function\"){var v=Error((n||\"React class\")+\": \"+t+\" type `\"+i+\"` is invalid; it must be a function, usually from the `prop-types` package, but received `\"+typeof e[i]+\"`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.\");throw v.name=\"Invariant Violation\",v}a=e[i](r,i,n,t,null,\"SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED\")}catch(c){a=c}a&&!(a instanceof Error)&&(F(o),d(\"%s: type specification of %s `%s` is invalid; the type checker function must return `null` or an `Error` but returned a %s. You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument).\",n||\"React class\",t,i,typeof a),F(null)),a instanceof Error&&!(a.message in le)&&(le[a.message]=!0,F(o),d(\"Failed %s type: %s\",t,a.message),F(null))}}}var Ge=Array.isArray;function V(e){return Ge(e)}function qe(e){{var r=typeof Symbol==\"function\"&&Symbol.toStringTag,t=r&&e[Symbol.toStringTag]||e.constructor.name||\"Object\";return t}}function ze(e){try{return fe(e),!1}catch{return!0}}function fe(e){return\"\"+e}function de(e){if(ze(e))return d(\"The provided key is an unsupported type %s. This value must be coerced to a string before before using it here.\",qe(e)),fe(e)}var O=R.ReactCurrentOwner,Ke={key:!0,ref:!0,__self:!0,__source:!0},ve,be,L;L={};function Xe(e){if(D.call(e,\"ref\")){var r=Object.getOwnPropertyDescriptor(e,\"ref\").get;if(r&&r.isReactWarning)return!1}return e.ref!==void 0}function He(e){if(D.call(e,\"key\")){var r=Object.getOwnPropertyDescriptor(e,\"key\").get;if(r&&r.isReactWarning)return!1}return e.key!==void 0}function Je(e,r){if(typeof e.ref==\"string\"&&O.current&&r&&O.current.stateNode!==r){var t=g(O.current.type);L[t]||(d('Component \"%s\" contains the string ref \"%s\". Support for string refs will be removed in a future major release. This case cannot be automatically converted to an arrow function. We ask you to manually fix this case by using useRef() or createRef() instead. Learn more about using refs safely here: https://reactjs.org/link/strict-mode-string-ref',g(O.current.type),e.ref),L[t]=!0)}}function Ze(e,r){{var t=function(){ve||(ve=!0,d(\"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)\",r))};t.isReactWarning=!0,Object.defineProperty(e,\"key\",{get:t,configurable:!0})}}function Qe(e,r){{var t=function(){be||(be=!0,d(\"%s: `ref` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)\",r))};t.isReactWarning=!0,Object.defineProperty(e,\"ref\",{get:t,configurable:!0})}}var er=function(e,r,t,n,o,u,i){var a={$$typeof:s,type:e,key:r,ref:t,props:i,_owner:u};return a._store={},Object.defineProperty(a._store,\"validated\",{configurable:!1,enumerable:!1,writable:!0,value:!1}),Object.defineProperty(a,\"_self\",{configurable:!1,enumerable:!1,writable:!1,value:n}),Object.defineProperty(a,\"_source\",{configurable:!1,enumerable:!1,writable:!1,value:o}),Object.freeze&&(Object.freeze(a.props),Object.freeze(a)),a};function rr(e,r,t,n,o){{var u,i={},a=null,v=null;t!==void 0&&(de(t),a=\"\"+t),He(r)&&(de(r.key),a=\"\"+r.key),Xe(r)&&(v=r.ref,Je(r,o));for(u in r)D.call(r,u)&&!Ke.hasOwnProperty(u)&&(i[u]=r[u]);if(e&&e.defaultProps){var c=e.defaultProps;for(u in c)i[u]===void 0&&(i[u]=c[u])}if(a||v){var f=typeof e==\"function\"?e.displayName||e.name||\"Unknown\":e;a&&Ze(i,f),v&&Qe(i,f)}return er(e,a,v,o,n,O.current,i)}}var U=R.ReactCurrentOwner,pe=R.ReactDebugCurrentFrame;function w(e){if(e){var r=e._owner,t=k(e.type,e._source,r?r.type:null);pe.setExtraStackFrame(t)}else pe.setExtraStackFrame(null)}var B;B=!1;function G(e){return typeof e==\"object\"&&e!==null&&e.$$typeof===s}function ge(){{if(U.current){var e=g(U.current.type);if(e)return`\n\nCheck the render method of \\``+e+\"`.\"}return\"\"}}function tr(e){{if(e!==void 0){var r=e.fileName.replace(/^.*[\\\\\\/]/,\"\"),t=e.lineNumber;return`\n\nCheck your code at `+r+\":\"+t+\".\"}return\"\"}}var me={};function nr(e){{var r=ge();if(!r){var t=typeof e==\"string\"?e:e.displayName||e.name;t&&(r=`\n\nCheck the top-level render call using <`+t+\">.\")}return r}}function he(e,r){{if(!e._store||e._store.validated||e.key!=null)return;e._store.validated=!0;var t=nr(r);if(me[t])return;me[t]=!0;var n=\"\";e&&e._owner&&e._owner!==U.current&&(n=\" It was passed a child from \"+g(e._owner.type)+\".\"),w(e),d('Each child in a list should have a unique \"key\" prop.%s%s See https://reactjs.org/link/warning-keys for more information.',t,n),w(null)}}function ye(e,r){{if(typeof e!=\"object\")return;if(V(e))for(var t=0;t<e.length;t++){var n=e[t];G(n)&&he(n,r)}else if(G(e))e._store&&(e._store.validated=!0);else if(e){var o=je(e);if(typeof o==\"function\"&&o!==e.entries)for(var u=o.call(e),i;!(i=u.next()).done;)G(i.value)&&he(i.value,r)}}}function ar(e){{var r=e.type;if(r==null||typeof r==\"string\")return;var t;if(typeof r==\"function\")t=r.propTypes;else if(typeof r==\"object\"&&(r.$$typeof===P||r.$$typeof===x))t=r.propTypes;else return;if(t){var n=g(r);Be(t,e.props,\"prop\",n,e)}else if(r.PropTypes!==void 0&&!B){B=!0;var o=g(r);d(\"Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?\",o||\"Unknown\")}typeof r.getDefaultProps==\"function\"&&!r.getDefaultProps.isReactClassApproved&&d(\"getDefaultProps is only used on classic React.createClass definitions. Use a static property named `defaultProps` instead.\")}}function ir(e){{for(var r=Object.keys(e.props),t=0;t<r.length;t++){var n=r[t];if(n!==\"children\"&&n!==\"key\"){w(e),d(\"Invalid prop `%s` supplied to `React.Fragment`. React.Fragment can only have `key` and `children` props.\",n),w(null);break}}e.ref!==null&&(w(e),d(\"Invalid attribute `ref` supplied to `React.Fragment`.\"),w(null))}}function or(e,r,t,n,o,u){{var i=We(e);if(!i){var a=\"\";(e===void 0||typeof e==\"object\"&&e!==null&&Object.keys(e).length===0)&&(a+=\" You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.\");var v=tr(o);v?a+=v:a+=ge();var c;e===null?c=\"null\":V(e)?c=\"array\":e!==void 0&&e.$$typeof===s?(c=\"<\"+(g(e.type)||\"Unknown\")+\" />\",a=\" Did you accidentally export a JSX literal instead of a component?\"):c=typeof e,d(\"React.jsx: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: %s.%s\",c,a)}var f=rr(e,r,t,o,u);if(f==null)return f;if(i){var b=r.children;if(b!==void 0)if(n)if(V(b)){for(var T=0;T<b.length;T++)ye(b[T],e);Object.freeze&&Object.freeze(b)}else d(\"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\");else ye(b,e)}return e===E?ir(f):ar(f),f}}var ur=or;z.Fragment=E,z.jsxDEV=ur})()});var Oe=q((wr,Ce)=>{\"use strict\";Ce.exports=Te()});var yr={};vr(yr,{default:()=>hr,frontmatter:()=>gr});var y=br(Oe()),gr={title:\"6 Reasons to Know if Your Website Needs a Redesign\",excerpt:\"If you've been using the same website for a while or if it was created back when responsive design didn't exist yet, there's a good chance your site needs to be reworked.\",date:\"2025-05-17\",updated:\"2025-05-20\",category:\"Web Development\",cover:\"/images/blog/6-raisons-refonte-site-web/main-refonte-site-web.webp\",published:!1};function Pe(l){let s=Object.assign({p:\"p\"},l.components);return(0,y.jsxDEV)(y.Fragment,{children:[(0,y.jsxDEV)(s.p,{children:\"If you've been using the same website for a while or if it was created back when responsive design didn't exist yet, there's a good chance your site needs to be reworked.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-beac774e-7501-4bbe-a539-c3df1fd9a2e2.mdx\",lineNumber:11,columnNumber:1},this),`\n`,(0,y.jsxDEV)(s.p,{children:\"Here are 6 key reasons that indicate your website needs a redesign to stay competitive and effective in today's digital landscape.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-beac774e-7501-4bbe-a539-c3df1fd9a2e2.mdx\",lineNumber:13,columnNumber:1},this)]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-beac774e-7501-4bbe-a539-c3df1fd9a2e2.mdx\",lineNumber:1,columnNumber:1},this)}function mr(l={}){let{wrapper:s}=l.components||{};return s?(0,y.jsxDEV)(s,Object.assign({},l,{children:(0,y.jsxDEV)(Pe,l,void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-beac774e-7501-4bbe-a539-c3df1fd9a2e2.mdx\"},this)}),void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-beac774e-7501-4bbe-a539-c3df1fd9a2e2.mdx\"},this):Pe(l)}var hr=mr;return pr(yr);})();\n/*! Bundled license information:\n\nreact/cjs/react-jsx-dev-runtime.development.js:\n  (**\n   * @license React\n   * react-jsx-dev-runtime.development.js\n   *\n   * Copyright (c) Facebook, Inc. and its affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *)\n*/\n;return Component;"}, "_id": "blog/en/6-reasons-website-redesign.mdx", "_raw": {"sourceFilePath": "blog/en/6-reasons-website-redesign.mdx", "sourceFileName": "6-reasons-website-redesign.mdx", "sourceFileDir": "blog/en", "contentType": "mdx", "flattenedPath": "blog/en/6-reasons-website-redesign"}, "type": "Post", "locale": "en", "slug": "6-reasons-website-redesign"}, {"title": "New CF Montréal logo: A symbol of renewal for 2023", "excerpt": "In 2023, CF Montréal announced the replacement of its snowflake-shaped logo with a new blue and black logo, featuring a fleur-de-lys at its center and a reference to the club's founding year, 1993.", "date": "2023-05-04T00:00:00.000Z", "updated": "2023-05-04T00:00:00.000Z", "category": "Branding", "cover": "/images/blog/logo-cf-montreal-2023/main-logo-cf-montreal-2023.png", "published": true, "body": {"raw": "\nCF Montréal supporters were waiting for it. The Montreal-based soccer club, which plays in Major League Soccer (MLS), unveiled a new logo in 2023, marking a major turning point in its visual identity. After the controversy sparked by the 2021 rebranding, this change is accompanied by the return of iconic symbols like the fleur-de-lys.\n\nWhy this change? What does this new logo symbolize? And how was it received by the fans? Let's break it down.\n\n## Back to the roots: A logo inspired by the Club's history\n\nThe **new CF Montréal** logo pays homage to the club's heritage by reintroducing elements familiar to long-time supporters. Designed as a traditional crest, it incorporates strong historical symbols of the Montreal Impact (the club's original name).\n\n<Image\n    src='/images/blog/logo-cf-montreal-2023/explication-logo-cf-montreal.webp'\n    alt=\"Explication du logo CF Montréal\"\n/>\n\n- The shield, reminiscent of the club's former logos\n- The fleur-de-lys, a symbol of Quebec that was once featured on the Impact's emblem\n- \"Impact\" blue returns as the logo's predominant color, accompanied by black\n- The black and blue stripes, recalling the club's first jerseys and its professional debut in the 1990s\n- \"1993,\" the year of the club's inaugural season\n\nIn addition to the official name **CF Montréal** being prominently displayed, all these elements give the crest a meaningful identity, rooted in the club's history and culture while still looking toward the future.\n\n\n## Why did CF Montréal changed its logo in 2023?\nThe change of the CF Montréal logo in 2023 is primarily explained by the desire to **course-correct** after the controversial rebranding of 2021.\n\nAs a reminder, on January 14, 2021, the management at the time announced that the Montreal Impact would be changing its name and visual identity to become \"Club de Foot Montréal.\" This radical rebranding—a new name and a snowflake-shaped logo—sparked strong opposition from a segment of supporters and the Quebec soccer community.\n\nMany long-standing fans did not identify with this new emblem, considering it too disconnected from the club's history and Montreal's identity.\n\nFaced with this negative reaction, the leadership decided to listen to the voice of the supporters. The arrival of a new president in 2022, Gabriel Gervais (himself a former player for the club), marked a turning point. Gervais publicly acknowledged that it was **difficult to identify with the former logo**: \"Where is the fleur-de-lys? Where is the blue color? How are we represented?\" he questioned, highlighting the absence of traditional symbols. This admission brought to light the **branding mistake** made in 2021. Thus, less than two years after the previous redesign, CF Montréal chose to change its logo again in order to **realign its visual identity with its original DNA**.\n<Tweet id=\"1521993350240473091\" />\n\nIn practical terms, this reversal was a direct response to supporter expectations. \"We heard them loud and clear,\" declared owner Joey Saputo, referring to the fans.\n\n\nThe club, therefore, accepted the need to **walk back** certain aesthetic choices to **mend the bond** with its fanbase. This 2023 logo change aims to unite the community after a period of division and controversy. It is a humble step from the organization, admitting that the previous identity was not unanimously accepted and attempting to \"mend fences\" with its most loyal fans.\n<Image\n    src='/images/blog/logo-cf-montreal-2023/comparaison-logo-cf-montreal.webp'\n    alt=\"Comparaisons de l'évolution du logo CF Montréal\"\n/>\n\n## Generally positive reactions\n\nThe unveiling of the new logo was well-received. On social media, in specialized media outlets, and within supporter groups, the return of the blue and the fleur-de-lys was praised.\n\n\nHowever, some fans were hoping for a complete return to the \"Impact\" name. This will not be the case. Gabriel Gervais confirmed: **the name CF Montréal will remain.**\n\nNevertheless, the club is open to honoring the Impact's legacy in other ways: a symbolic name in the stadium, visual references, and nods in its communications.\n\nOverall, this new identity is seen as a balanced compromise between the old and the new. It gives the club an image that is more aligned with its community.\n## Conclusion\n\nThis identity change is possibly part of a broader trend in MLS, where several clubs have recently undergone logo redesigns to better reflect their identity (one might think of the rebrands for the Columbus Crew, Chicago Fire, etc.). But in Montreal's case, the move was primarily guided by a local need: to reconnect the club with its fanbase and its heritage.", "code": "var Component=(()=>{var cn=Object.create;var D=Object.defineProperty;var fn=Object.getOwnPropertyDescriptor;var dn=Object.getOwnPropertyNames;var bn=Object.getPrototypeOf,mn=Object.prototype.hasOwnProperty;var z=(u,t)=>()=>(t||u((t={exports:{}}).exports,t),t.exports),hn=(u,t)=>{for(var b in t)D(u,b,{get:t[b],enumerable:!0})},we=(u,t,b,p)=>{if(t&&typeof t==\"object\"||typeof t==\"function\")for(let y of dn(t))!mn.call(u,y)&&y!==b&&D(u,y,{get:()=>t[y],enumerable:!(p=fn(t,y))||p.enumerable});return u};var gn=(u,t,b)=>(b=u!=null?cn(bn(u)):{},we(t||!u||!u.__esModule?D(b,\"default\",{value:u,enumerable:!0}):b,u)),pn=u=>we(D({},\"__esModule\",{value:!0}),u);var xe=z((xn,Ne)=>{Ne.exports=React});var Ee=z(q=>{\"use strict\";(function(){\"use strict\";var u=xe(),t=Symbol.for(\"react.element\"),b=Symbol.for(\"react.portal\"),p=Symbol.for(\"react.fragment\"),y=Symbol.for(\"react.strict_mode\"),H=Symbol.for(\"react.profiler\"),X=Symbol.for(\"react.provider\"),K=Symbol.for(\"react.context\"),T=Symbol.for(\"react.forward_ref\"),M=Symbol.for(\"react.suspense\"),A=Symbol.for(\"react.suspense_list\"),C=Symbol.for(\"react.memo\"),I=Symbol.for(\"react.lazy\"),Oe=Symbol.for(\"react.offscreen\"),J=Symbol.iterator,Se=\"@@iterator\";function Pe(e){if(e===null||typeof e!=\"object\")return null;var n=J&&e[J]||e[Se];return typeof n==\"function\"?n:null}var N=u.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;function m(e){{for(var n=arguments.length,r=new Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];je(\"error\",e,r)}}function je(e,n,r){{var i=N.ReactDebugCurrentFrame,s=i.getStackAddendum();s!==\"\"&&(n+=\"%s\",r=r.concat([s]));var c=r.map(function(l){return String(l)});c.unshift(\"Warning: \"+n),Function.prototype.apply.call(console[e],console,c)}}var Fe=!1,De=!1,Me=!1,Ae=!1,Ie=!1,Q;Q=Symbol.for(\"react.module.reference\");function We(e){return!!(typeof e==\"string\"||typeof e==\"function\"||e===p||e===H||Ie||e===y||e===M||e===A||Ae||e===Oe||Fe||De||Me||typeof e==\"object\"&&e!==null&&(e.$$typeof===I||e.$$typeof===C||e.$$typeof===X||e.$$typeof===K||e.$$typeof===T||e.$$typeof===Q||e.getModuleId!==void 0))}function Ye(e,n,r){var i=e.displayName;if(i)return i;var s=n.displayName||n.name||\"\";return s!==\"\"?r+\"(\"+s+\")\":r}function Z(e){return e.displayName||\"Context\"}function _(e){if(e==null)return null;if(typeof e.tag==\"number\"&&m(\"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"),typeof e==\"function\")return e.displayName||e.name||null;if(typeof e==\"string\")return e;switch(e){case p:return\"Fragment\";case b:return\"Portal\";case H:return\"Profiler\";case y:return\"StrictMode\";case M:return\"Suspense\";case A:return\"SuspenseList\"}if(typeof e==\"object\")switch(e.$$typeof){case K:var n=e;return Z(n)+\".Consumer\";case X:var r=e;return Z(r._context)+\".Provider\";case T:return Ye(e,e.render,\"ForwardRef\");case C:var i=e.displayName||null;return i!==null?i:_(e.type)||\"Memo\";case I:{var s=e,c=s._payload,l=s._init;try{return _(l(c))}catch{return null}}}return null}var w=Object.assign,k=0,ee,ne,re,te,ie,ae,oe;function le(){}le.__reactDisabledLog=!0;function $e(){{if(k===0){ee=console.log,ne=console.info,re=console.warn,te=console.error,ie=console.group,ae=console.groupCollapsed,oe=console.groupEnd;var e={configurable:!0,enumerable:!0,value:le,writable:!0};Object.defineProperties(console,{info:e,log:e,warn:e,error:e,group:e,groupCollapsed:e,groupEnd:e})}k++}}function Le(){{if(k--,k===0){var e={configurable:!0,enumerable:!0,writable:!0};Object.defineProperties(console,{log:w({},e,{value:ee}),info:w({},e,{value:ne}),warn:w({},e,{value:re}),error:w({},e,{value:te}),group:w({},e,{value:ie}),groupCollapsed:w({},e,{value:ae}),groupEnd:w({},e,{value:oe})})}k<0&&m(\"disabledDepth fell below zero. This is a bug in React. Please file an issue.\")}}var W=N.ReactCurrentDispatcher,Y;function O(e,n,r){{if(Y===void 0)try{throw Error()}catch(s){var i=s.stack.trim().match(/\\n( *(at )?)/);Y=i&&i[1]||\"\"}return`\n`+Y+e}}var $=!1,S;{var Ve=typeof WeakMap==\"function\"?WeakMap:Map;S=new Ve}function se(e,n){if(!e||$)return\"\";{var r=S.get(e);if(r!==void 0)return r}var i;$=!0;var s=Error.prepareStackTrace;Error.prepareStackTrace=void 0;var c;c=W.current,W.current=null,$e();try{if(n){var l=function(){throw Error()};if(Object.defineProperty(l.prototype,\"props\",{set:function(){throw Error()}}),typeof Reflect==\"object\"&&Reflect.construct){try{Reflect.construct(l,[])}catch(v){i=v}Reflect.construct(e,[],l)}else{try{l.call()}catch(v){i=v}e.call(l.prototype)}}else{try{throw Error()}catch(v){i=v}e()}}catch(v){if(v&&i&&typeof v.stack==\"string\"){for(var o=v.stack.split(`\n`),h=i.stack.split(`\n`),f=o.length-1,d=h.length-1;f>=1&&d>=0&&o[f]!==h[d];)d--;for(;f>=1&&d>=0;f--,d--)if(o[f]!==h[d]){if(f!==1||d!==1)do if(f--,d--,d<0||o[f]!==h[d]){var g=`\n`+o[f].replace(\" at new \",\" at \");return e.displayName&&g.includes(\"<anonymous>\")&&(g=g.replace(\"<anonymous>\",e.displayName)),typeof e==\"function\"&&S.set(e,g),g}while(f>=1&&d>=0);break}}}finally{$=!1,W.current=c,Le(),Error.prepareStackTrace=s}var E=e?e.displayName||e.name:\"\",ye=E?O(E):\"\";return typeof e==\"function\"&&S.set(e,ye),ye}function Ue(e,n,r){return se(e,!1)}function Ge(e){var n=e.prototype;return!!(n&&n.isReactComponent)}function P(e,n,r){if(e==null)return\"\";if(typeof e==\"function\")return se(e,Ge(e));if(typeof e==\"string\")return O(e);switch(e){case M:return O(\"Suspense\");case A:return O(\"SuspenseList\")}if(typeof e==\"object\")switch(e.$$typeof){case T:return Ue(e.render);case C:return P(e.type,n,r);case I:{var i=e,s=i._payload,c=i._init;try{return P(c(s),n,r)}catch{}}}return\"\"}var j=Object.prototype.hasOwnProperty,ue={},ce=N.ReactDebugCurrentFrame;function F(e){if(e){var n=e._owner,r=P(e.type,e._source,n?n.type:null);ce.setExtraStackFrame(r)}else ce.setExtraStackFrame(null)}function Be(e,n,r,i,s){{var c=Function.call.bind(j);for(var l in e)if(c(e,l)){var o=void 0;try{if(typeof e[l]!=\"function\"){var h=Error((i||\"React class\")+\": \"+r+\" type `\"+l+\"` is invalid; it must be a function, usually from the `prop-types` package, but received `\"+typeof e[l]+\"`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.\");throw h.name=\"Invariant Violation\",h}o=e[l](n,l,i,r,null,\"SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED\")}catch(f){o=f}o&&!(o instanceof Error)&&(F(s),m(\"%s: type specification of %s `%s` is invalid; the type checker function must return `null` or an `Error` but returned a %s. You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument).\",i||\"React class\",r,l,typeof o),F(null)),o instanceof Error&&!(o.message in ue)&&(ue[o.message]=!0,F(s),m(\"Failed %s type: %s\",r,o.message),F(null))}}}var ze=Array.isArray;function L(e){return ze(e)}function qe(e){{var n=typeof Symbol==\"function\"&&Symbol.toStringTag,r=n&&e[Symbol.toStringTag]||e.constructor.name||\"Object\";return r}}function He(e){try{return fe(e),!1}catch{return!0}}function fe(e){return\"\"+e}function de(e){if(He(e))return m(\"The provided key is an unsupported type %s. This value must be coerced to a string before before using it here.\",qe(e)),fe(e)}var R=N.ReactCurrentOwner,Xe={key:!0,ref:!0,__self:!0,__source:!0},be,me,V;V={};function Ke(e){if(j.call(e,\"ref\")){var n=Object.getOwnPropertyDescriptor(e,\"ref\").get;if(n&&n.isReactWarning)return!1}return e.ref!==void 0}function Je(e){if(j.call(e,\"key\")){var n=Object.getOwnPropertyDescriptor(e,\"key\").get;if(n&&n.isReactWarning)return!1}return e.key!==void 0}function Qe(e,n){if(typeof e.ref==\"string\"&&R.current&&n&&R.current.stateNode!==n){var r=_(R.current.type);V[r]||(m('Component \"%s\" contains the string ref \"%s\". Support for string refs will be removed in a future major release. This case cannot be automatically converted to an arrow function. We ask you to manually fix this case by using useRef() or createRef() instead. Learn more about using refs safely here: https://reactjs.org/link/strict-mode-string-ref',_(R.current.type),e.ref),V[r]=!0)}}function Ze(e,n){{var r=function(){be||(be=!0,m(\"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)\",n))};r.isReactWarning=!0,Object.defineProperty(e,\"key\",{get:r,configurable:!0})}}function en(e,n){{var r=function(){me||(me=!0,m(\"%s: `ref` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)\",n))};r.isReactWarning=!0,Object.defineProperty(e,\"ref\",{get:r,configurable:!0})}}var nn=function(e,n,r,i,s,c,l){var o={$$typeof:t,type:e,key:n,ref:r,props:l,_owner:c};return o._store={},Object.defineProperty(o._store,\"validated\",{configurable:!1,enumerable:!1,writable:!0,value:!1}),Object.defineProperty(o,\"_self\",{configurable:!1,enumerable:!1,writable:!1,value:i}),Object.defineProperty(o,\"_source\",{configurable:!1,enumerable:!1,writable:!1,value:s}),Object.freeze&&(Object.freeze(o.props),Object.freeze(o)),o};function rn(e,n,r,i,s){{var c,l={},o=null,h=null;r!==void 0&&(de(r),o=\"\"+r),Je(n)&&(de(n.key),o=\"\"+n.key),Ke(n)&&(h=n.ref,Qe(n,s));for(c in n)j.call(n,c)&&!Xe.hasOwnProperty(c)&&(l[c]=n[c]);if(e&&e.defaultProps){var f=e.defaultProps;for(c in f)l[c]===void 0&&(l[c]=f[c])}if(o||h){var d=typeof e==\"function\"?e.displayName||e.name||\"Unknown\":e;o&&Ze(l,d),h&&en(l,d)}return nn(e,o,h,s,i,R.current,l)}}var U=N.ReactCurrentOwner,he=N.ReactDebugCurrentFrame;function x(e){if(e){var n=e._owner,r=P(e.type,e._source,n?n.type:null);he.setExtraStackFrame(r)}else he.setExtraStackFrame(null)}var G;G=!1;function B(e){return typeof e==\"object\"&&e!==null&&e.$$typeof===t}function ge(){{if(U.current){var e=_(U.current.type);if(e)return`\n\nCheck the render method of \\``+e+\"`.\"}return\"\"}}function tn(e){{if(e!==void 0){var n=e.fileName.replace(/^.*[\\\\\\/]/,\"\"),r=e.lineNumber;return`\n\nCheck your code at `+n+\":\"+r+\".\"}return\"\"}}var pe={};function an(e){{var n=ge();if(!n){var r=typeof e==\"string\"?e:e.displayName||e.name;r&&(n=`\n\nCheck the top-level render call using <`+r+\">.\")}return n}}function _e(e,n){{if(!e._store||e._store.validated||e.key!=null)return;e._store.validated=!0;var r=an(n);if(pe[r])return;pe[r]=!0;var i=\"\";e&&e._owner&&e._owner!==U.current&&(i=\" It was passed a child from \"+_(e._owner.type)+\".\"),x(e),m('Each child in a list should have a unique \"key\" prop.%s%s See https://reactjs.org/link/warning-keys for more information.',r,i),x(null)}}function ve(e,n){{if(typeof e!=\"object\")return;if(L(e))for(var r=0;r<e.length;r++){var i=e[r];B(i)&&_e(i,n)}else if(B(e))e._store&&(e._store.validated=!0);else if(e){var s=Pe(e);if(typeof s==\"function\"&&s!==e.entries)for(var c=s.call(e),l;!(l=c.next()).done;)B(l.value)&&_e(l.value,n)}}}function on(e){{var n=e.type;if(n==null||typeof n==\"string\")return;var r;if(typeof n==\"function\")r=n.propTypes;else if(typeof n==\"object\"&&(n.$$typeof===T||n.$$typeof===C))r=n.propTypes;else return;if(r){var i=_(n);Be(r,e.props,\"prop\",i,e)}else if(n.PropTypes!==void 0&&!G){G=!0;var s=_(n);m(\"Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?\",s||\"Unknown\")}typeof n.getDefaultProps==\"function\"&&!n.getDefaultProps.isReactClassApproved&&m(\"getDefaultProps is only used on classic React.createClass definitions. Use a static property named `defaultProps` instead.\")}}function ln(e){{for(var n=Object.keys(e.props),r=0;r<n.length;r++){var i=n[r];if(i!==\"children\"&&i!==\"key\"){x(e),m(\"Invalid prop `%s` supplied to `React.Fragment`. React.Fragment can only have `key` and `children` props.\",i),x(null);break}}e.ref!==null&&(x(e),m(\"Invalid attribute `ref` supplied to `React.Fragment`.\"),x(null))}}function sn(e,n,r,i,s,c){{var l=We(e);if(!l){var o=\"\";(e===void 0||typeof e==\"object\"&&e!==null&&Object.keys(e).length===0)&&(o+=\" You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.\");var h=tn(s);h?o+=h:o+=ge();var f;e===null?f=\"null\":L(e)?f=\"array\":e!==void 0&&e.$$typeof===t?(f=\"<\"+(_(e.type)||\"Unknown\")+\" />\",o=\" Did you accidentally export a JSX literal instead of a component?\"):f=typeof e,m(\"React.jsx: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: %s.%s\",f,o)}var d=rn(e,n,r,s,c);if(d==null)return d;if(l){var g=n.children;if(g!==void 0)if(i)if(L(g)){for(var E=0;E<g.length;E++)ve(g[E],e);Object.freeze&&Object.freeze(g)}else m(\"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\");else ve(g,e)}return e===p?ln(d):on(d),d}}var un=sn;q.Fragment=p,q.jsxDEV=un})()});var Re=z((kn,ke)=>{\"use strict\";ke.exports=Ee()});var wn={};hn(wn,{default:()=>yn,frontmatter:()=>_n});var a=gn(Re()),_n={title:\"New CF Montr\\xE9al logo: A symbol of renewal for 2023\",excerpt:\"In 2023, CF Montr\\xE9al announced the replacement of its snowflake-shaped logo with a new blue and black logo, featuring a fleur-de-lys at its center and a reference to the club's founding year, 1993.\",date:\"2023-05-04\",updated:\"2023-05-04\",category:\"Branding\",cover:\"/images/blog/logo-cf-montreal-2023/main-logo-cf-montreal-2023.png\"};function Te(u){let t=Object.assign({p:\"p\",h2:\"h2\",strong:\"strong\",ul:\"ul\",li:\"li\"},u.components),{Image:b,Tweet:p}=t;return b||Ce(\"Image\",!0,\"18:1-21:3\"),p||Ce(\"Tweet\",!0,\"40:1-40:35\"),(0,a.jsxDEV)(a.Fragment,{children:[(0,a.jsxDEV)(t.p,{children:\"CF Montr\\xE9al supporters were waiting for it. The Montreal-based soccer club, which plays in Major League Soccer (MLS), unveiled a new logo in 2023, marking a major turning point in its visual identity. After the controversy sparked by the 2021 rebranding, this change is accompanied by the return of iconic symbols like the fleur-de-lys.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-f935c489-a3db-4e48-9835-e693b5bf9082.mdx\",lineNumber:10,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:\"Why this change? What does this new logo symbolize? And how was it received by the fans? Let's break it down.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-f935c489-a3db-4e48-9835-e693b5bf9082.mdx\",lineNumber:12,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.h2,{children:\"Back to the roots: A logo inspired by the Club's history\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-f935c489-a3db-4e48-9835-e693b5bf9082.mdx\",lineNumber:14,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:[\"The \",(0,a.jsxDEV)(t.strong,{children:\"new CF Montr\\xE9al\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-f935c489-a3db-4e48-9835-e693b5bf9082.mdx\",lineNumber:16,columnNumber:5},this),\" logo pays homage to the club's heritage by reintroducing elements familiar to long-time supporters. Designed as a traditional crest, it incorporates strong historical symbols of the Montreal Impact (the club's original name).\"]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-f935c489-a3db-4e48-9835-e693b5bf9082.mdx\",lineNumber:16,columnNumber:1},this),`\n`,(0,a.jsxDEV)(b,{src:\"/images/blog/logo-cf-montreal-2023/explication-logo-cf-montreal.webp\",alt:\"Explication du logo CF Montr\\xE9al\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-f935c489-a3db-4e48-9835-e693b5bf9082.mdx\",lineNumber:18,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.ul,{children:[`\n`,(0,a.jsxDEV)(t.li,{children:\"The shield, reminiscent of the club's former logos\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-f935c489-a3db-4e48-9835-e693b5bf9082.mdx\",lineNumber:23,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.li,{children:\"The fleur-de-lys, a symbol of Quebec that was once featured on the Impact's emblem\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-f935c489-a3db-4e48-9835-e693b5bf9082.mdx\",lineNumber:24,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.li,{children:`\"Impact\" blue returns as the logo's predominant color, accompanied by black`},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-f935c489-a3db-4e48-9835-e693b5bf9082.mdx\",lineNumber:25,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.li,{children:\"The black and blue stripes, recalling the club's first jerseys and its professional debut in the 1990s\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-f935c489-a3db-4e48-9835-e693b5bf9082.mdx\",lineNumber:26,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.li,{children:`\"1993,\" the year of the club's inaugural season`},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-f935c489-a3db-4e48-9835-e693b5bf9082.mdx\",lineNumber:27,columnNumber:1},this),`\n`]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-f935c489-a3db-4e48-9835-e693b5bf9082.mdx\",lineNumber:23,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:[\"In addition to the official name \",(0,a.jsxDEV)(t.strong,{children:\"CF Montr\\xE9al\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-f935c489-a3db-4e48-9835-e693b5bf9082.mdx\",lineNumber:29,columnNumber:34},this),\" being prominently displayed, all these elements give the crest a meaningful identity, rooted in the club's history and culture while still looking toward the future.\"]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-f935c489-a3db-4e48-9835-e693b5bf9082.mdx\",lineNumber:29,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.h2,{children:\"Why did CF Montr\\xE9al changed its logo in 2023?\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-f935c489-a3db-4e48-9835-e693b5bf9082.mdx\",lineNumber:32,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:[\"The change of the CF Montr\\xE9al logo in 2023 is primarily explained by the desire to \",(0,a.jsxDEV)(t.strong,{children:\"course-correct\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-f935c489-a3db-4e48-9835-e693b5bf9082.mdx\",lineNumber:33,columnNumber:84},this),\" after the controversial rebranding of 2021.\"]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-f935c489-a3db-4e48-9835-e693b5bf9082.mdx\",lineNumber:33,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:'As a reminder, on January 14, 2021, the management at the time announced that the Montreal Impact would be changing its name and visual identity to become \"Club de Foot Montr\\xE9al.\" This radical rebranding\\u2014a new name and a snowflake-shaped logo\\u2014sparked strong opposition from a segment of supporters and the Quebec soccer community.'},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-f935c489-a3db-4e48-9835-e693b5bf9082.mdx\",lineNumber:35,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:\"Many long-standing fans did not identify with this new emblem, considering it too disconnected from the club's history and Montreal's identity.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-f935c489-a3db-4e48-9835-e693b5bf9082.mdx\",lineNumber:37,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:[\"Faced with this negative reaction, the leadership decided to listen to the voice of the supporters. The arrival of a new president in 2022, Gabriel Gervais (himself a former player for the club), marked a turning point. Gervais publicly acknowledged that it was \",(0,a.jsxDEV)(t.strong,{children:\"difficult to identify with the former logo\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-f935c489-a3db-4e48-9835-e693b5bf9082.mdx\",lineNumber:39,columnNumber:263},this),': \"Where is the fleur-de-lys? Where is the blue color? How are we represented?\" he questioned, highlighting the absence of traditional symbols. This admission brought to light the ',(0,a.jsxDEV)(t.strong,{children:\"branding mistake\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-f935c489-a3db-4e48-9835-e693b5bf9082.mdx\",lineNumber:39,columnNumber:489},this),\" made in 2021. Thus, less than two years after the previous redesign, CF Montr\\xE9al chose to change its logo again in order to \",(0,a.jsxDEV)(t.strong,{children:\"realign its visual identity with its original DNA\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-f935c489-a3db-4e48-9835-e693b5bf9082.mdx\",lineNumber:39,columnNumber:634},this),\".\"]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-f935c489-a3db-4e48-9835-e693b5bf9082.mdx\",lineNumber:39,columnNumber:1},this),`\n`,(0,a.jsxDEV)(p,{id:\"1521993350240473091\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-f935c489-a3db-4e48-9835-e693b5bf9082.mdx\",lineNumber:40,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:'In practical terms, this reversal was a direct response to supporter expectations. \"We heard them loud and clear,\" declared owner Joey Saputo, referring to the fans.'},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-f935c489-a3db-4e48-9835-e693b5bf9082.mdx\",lineNumber:42,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:[\"The club, therefore, accepted the need to \",(0,a.jsxDEV)(t.strong,{children:\"walk back\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-f935c489-a3db-4e48-9835-e693b5bf9082.mdx\",lineNumber:45,columnNumber:43},this),\" certain aesthetic choices to \",(0,a.jsxDEV)(t.strong,{children:\"mend the bond\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-f935c489-a3db-4e48-9835-e693b5bf9082.mdx\",lineNumber:45,columnNumber:86},this),' with its fanbase. This 2023 logo change aims to unite the community after a period of division and controversy. It is a humble step from the organization, admitting that the previous identity was not unanimously accepted and attempting to \"mend fences\" with its most loyal fans.']},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-f935c489-a3db-4e48-9835-e693b5bf9082.mdx\",lineNumber:45,columnNumber:1},this),`\n`,(0,a.jsxDEV)(b,{src:\"/images/blog/logo-cf-montreal-2023/comparaison-logo-cf-montreal.webp\",alt:\"Comparaisons de l'\\xE9volution du logo CF Montr\\xE9al\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-f935c489-a3db-4e48-9835-e693b5bf9082.mdx\",lineNumber:46,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.h2,{children:\"Generally positive reactions\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-f935c489-a3db-4e48-9835-e693b5bf9082.mdx\",lineNumber:51,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:\"The unveiling of the new logo was well-received. On social media, in specialized media outlets, and within supporter groups, the return of the blue and the fleur-de-lys was praised.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-f935c489-a3db-4e48-9835-e693b5bf9082.mdx\",lineNumber:53,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:['However, some fans were hoping for a complete return to the \"Impact\" name. This will not be the case. Gabriel Gervais confirmed: ',(0,a.jsxDEV)(t.strong,{children:\"the name CF Montr\\xE9al will remain.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-f935c489-a3db-4e48-9835-e693b5bf9082.mdx\",lineNumber:56,columnNumber:130},this)]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-f935c489-a3db-4e48-9835-e693b5bf9082.mdx\",lineNumber:56,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:\"Nevertheless, the club is open to honoring the Impact's legacy in other ways: a symbolic name in the stadium, visual references, and nods in its communications.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-f935c489-a3db-4e48-9835-e693b5bf9082.mdx\",lineNumber:58,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:\"Overall, this new identity is seen as a balanced compromise between the old and the new. It gives the club an image that is more aligned with its community.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-f935c489-a3db-4e48-9835-e693b5bf9082.mdx\",lineNumber:60,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.h2,{children:\"Conclusion\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-f935c489-a3db-4e48-9835-e693b5bf9082.mdx\",lineNumber:61,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:\"This identity change is possibly part of a broader trend in MLS, where several clubs have recently undergone logo redesigns to better reflect their identity (one might think of the rebrands for the Columbus Crew, Chicago Fire, etc.). But in Montreal's case, the move was primarily guided by a local need: to reconnect the club with its fanbase and its heritage.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-f935c489-a3db-4e48-9835-e693b5bf9082.mdx\",lineNumber:63,columnNumber:1},this)]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-f935c489-a3db-4e48-9835-e693b5bf9082.mdx\",lineNumber:1,columnNumber:1},this)}function vn(u={}){let{wrapper:t}=u.components||{};return t?(0,a.jsxDEV)(t,Object.assign({},u,{children:(0,a.jsxDEV)(Te,u,void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-f935c489-a3db-4e48-9835-e693b5bf9082.mdx\"},this)}),void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-f935c489-a3db-4e48-9835-e693b5bf9082.mdx\"},this):Te(u)}var yn=vn;function Ce(u,t,b){throw new Error(\"Expected \"+(t?\"component\":\"object\")+\" `\"+u+\"` to be defined: you likely forgot to import, pass, or provide it.\"+(b?\"\\nIt\\u2019s referenced in your code at `\"+b+\"` in `/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-f935c489-a3db-4e48-9835-e693b5bf9082.mdx`\":\"\"))}return pn(wn);})();\n/*! Bundled license information:\n\nreact/cjs/react-jsx-dev-runtime.development.js:\n  (**\n   * @license React\n   * react-jsx-dev-runtime.development.js\n   *\n   * Copyright (c) Facebook, Inc. and its affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *)\n*/\n;return Component;"}, "_id": "blog/en/logo-cf-montreal-2023.mdx", "_raw": {"sourceFilePath": "blog/en/logo-cf-montreal-2023.mdx", "sourceFileName": "logo-cf-montreal-2023.mdx", "sourceFileDir": "blog/en", "contentType": "mdx", "flattenedPath": "blog/en/logo-cf-montreal-2023"}, "type": "Post", "locale": "en", "slug": "logo-cf-montreal-2023"}, {"title": "Deezer unveils its new logo in 2023", "excerpt": "The year 2023 marks a new era for Deezer, the music streaming giant. With its bold rebranding embodied by a logo featuring a vibrant heart.", "date": "2023-11-15T00:00:00.000Z", "updated": "2023-11-15T00:00:00.000Z", "category": "Brand identity", "cover": "/images/blog/logo-deezer-2023/main-logo-deezer-2023.png", "published": true, "body": {"raw": "\nThis new purple heart splits opinion, but the community-centered approach aligns perfectly with <PERSON><PERSON>’s promise. We explain why—and how the logo could earn its place.\n\n## Why change?\nIn 2023, <PERSON><PERSON> was looking for two things:\n1. Clarify its mission to become “the home of music” rather than just another streaming service.\n2. Differentiate itself in a saturated market (Spotify and Apple Music) with a strong, easily adaptable symbol.\n\nSo, the redesign is less about aesthetics and more about repositioning.\n\n<video\n  src=\"/images/blog/logo-deezer-2023/affiches-animees-deezer.webm\"\n  playsInline\n  autoPlay\n  muted\n  loop\n  style={{ width: '100%' }}\n/>\n\n## A pulsing heart\n\nDee<PERSON>’s new symbol, an animated purple heart, literally embodies their message: “Feel the music.”\n\nThe animation syncs to the tempo of the current track and immerses the user in the platform’s universe. Visually, this heart is instantly memorable and contrasts with the dominant hues used by other streaming services, like Spotify’s green.\n\n<video\n  src=\"/images/blog/logo-deezer-2023/animation-logo-deezer.webm\"\n  playsInline\n  autoPlay\n  muted\n  loop\n  style={{ width: '100%' }}\n/>\n\nTo accompany this icon, <PERSON><PERSON> introduces a **custom typeface**: <PERSON><PERSON>.\n\nIts curves mirror the logo’s lines—narrow enough for dense interfaces, wide enough to support a strong message on a poster.\n\n<Image\n    src='/images/blog/logo-deezer-2023/affiches-deezer.jpg'\n    alt=\"Posters of the new <PERSON>zer logo\"\n/>\n\n<video\n  src=\"/images/blog/logo-deezer-2023/animation-video-deezer.webm\"\n  playsInline\n  autoPlay\n  muted\n  loop\n  style={{ width: '100%' }}\n/>\n\n## An identity to attract a younger audience\n\nAmong creatives, the risk-taking is appealing: abandoning the old equalizer logo for an animated heart was not an obvious choice. In forums, some applaud the boldness; others, more skeptical, find the visual too close to **dating app conventions**.\n\nSubscribers are divided: many appreciate the warmer approach, while nostalgic fans miss the old logo.\n\nIn terms of memorability, the wager pays off: the icon works as well on a smartwatch as on a city billboard. The remaining challenge is maintaining recognition without animation—on print, favicons, or merchandise—by relying on a coherent visual universe: waves, purple halo, micro-motions.\n\n<Image\n    src='/images/blog/logo-deezer-2023/illustrations-deezer.webp'\n    alt=\"Illustrations of Deezer’s visual identity\"\n/>", "code": "var Component=(()=>{var sr=Object.create;var F=Object.defineProperty;var ur=Object.getOwnPropertyDescriptor;var cr=Object.getOwnPropertyNames;var fr=Object.getPrototypeOf,dr=Object.prototype.hasOwnProperty;var q=(u,a)=>()=>(a||u((a={exports:{}}).exports,a),a.exports),mr=(u,a)=>{for(var m in a)F(u,m,{get:a[m],enumerable:!0})},we=(u,a,m,y)=>{if(a&&typeof a==\"object\"||typeof a==\"function\")for(let _ of cr(a))!dr.call(u,_)&&_!==m&&F(u,_,{get:()=>a[_],enumerable:!(y=ur(a,_))||y.enumerable});return u};var br=(u,a,m)=>(m=u!=null?sr(fr(u)):{},we(a||!u||!u.__esModule?F(m,\"default\",{value:u,enumerable:!0}):m,u)),pr=u=>we(F({},\"__esModule\",{value:!0}),u);var xe=q((Er,Ee)=>{Ee.exports=React});var Ne=q(G=>{\"use strict\";(function(){\"use strict\";var u=xe(),a=Symbol.for(\"react.element\"),m=Symbol.for(\"react.portal\"),y=Symbol.for(\"react.fragment\"),_=Symbol.for(\"react.strict_mode\"),X=Symbol.for(\"react.profiler\"),K=Symbol.for(\"react.provider\"),H=Symbol.for(\"react.context\"),T=Symbol.for(\"react.forward_ref\"),A=Symbol.for(\"react.suspense\"),I=Symbol.for(\"react.suspense_list\"),C=Symbol.for(\"react.memo\"),z=Symbol.for(\"react.lazy\"),Ce=Symbol.for(\"react.offscreen\"),J=Symbol.iterator,Pe=\"@@iterator\";function Se(e){if(e===null||typeof e!=\"object\")return null;var r=J&&e[J]||e[Pe];return typeof r==\"function\"?r:null}var E=u.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;function b(e){{for(var r=arguments.length,n=new Array(r>1?r-1:0),t=1;t<r;t++)n[t-1]=arguments[t];Oe(\"error\",e,n)}}function Oe(e,r,n){{var t=E.ReactDebugCurrentFrame,l=t.getStackAddendum();l!==\"\"&&(r+=\"%s\",n=n.concat([l]));var c=n.map(function(o){return String(o)});c.unshift(\"Warning: \"+r),Function.prototype.apply.call(console[e],console,c)}}var je=!1,De=!1,Fe=!1,Ae=!1,Ie=!1,Z;Z=Symbol.for(\"react.module.reference\");function ze(e){return!!(typeof e==\"string\"||typeof e==\"function\"||e===y||e===X||Ie||e===_||e===A||e===I||Ae||e===Ce||je||De||Fe||typeof e==\"object\"&&e!==null&&(e.$$typeof===z||e.$$typeof===C||e.$$typeof===K||e.$$typeof===H||e.$$typeof===T||e.$$typeof===Z||e.getModuleId!==void 0))}function We(e,r,n){var t=e.displayName;if(t)return t;var l=r.displayName||r.name||\"\";return l!==\"\"?n+\"(\"+l+\")\":n}function Q(e){return e.displayName||\"Context\"}function g(e){if(e==null)return null;if(typeof e.tag==\"number\"&&b(\"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"),typeof e==\"function\")return e.displayName||e.name||null;if(typeof e==\"string\")return e;switch(e){case y:return\"Fragment\";case m:return\"Portal\";case X:return\"Profiler\";case _:return\"StrictMode\";case A:return\"Suspense\";case I:return\"SuspenseList\"}if(typeof e==\"object\")switch(e.$$typeof){case H:var r=e;return Q(r)+\".Consumer\";case K:var n=e;return Q(n._context)+\".Provider\";case T:return We(e,e.render,\"ForwardRef\");case C:var t=e.displayName||null;return t!==null?t:g(e.type)||\"Memo\";case z:{var l=e,c=l._payload,o=l._init;try{return g(o(c))}catch{return null}}}return null}var w=Object.assign,R=0,ee,re,ne,te,ae,ie,oe;function le(){}le.__reactDisabledLog=!0;function Ye(){{if(R===0){ee=console.log,re=console.info,ne=console.warn,te=console.error,ae=console.group,ie=console.groupCollapsed,oe=console.groupEnd;var e={configurable:!0,enumerable:!0,value:le,writable:!0};Object.defineProperties(console,{info:e,log:e,warn:e,error:e,group:e,groupCollapsed:e,groupEnd:e})}R++}}function $e(){{if(R--,R===0){var e={configurable:!0,enumerable:!0,writable:!0};Object.defineProperties(console,{log:w({},e,{value:ee}),info:w({},e,{value:re}),warn:w({},e,{value:ne}),error:w({},e,{value:te}),group:w({},e,{value:ae}),groupCollapsed:w({},e,{value:ie}),groupEnd:w({},e,{value:oe})})}R<0&&b(\"disabledDepth fell below zero. This is a bug in React. Please file an issue.\")}}var W=E.ReactCurrentDispatcher,Y;function P(e,r,n){{if(Y===void 0)try{throw Error()}catch(l){var t=l.stack.trim().match(/\\n( *(at )?)/);Y=t&&t[1]||\"\"}return`\n`+Y+e}}var $=!1,S;{var Me=typeof WeakMap==\"function\"?WeakMap:Map;S=new Me}function se(e,r){if(!e||$)return\"\";{var n=S.get(e);if(n!==void 0)return n}var t;$=!0;var l=Error.prepareStackTrace;Error.prepareStackTrace=void 0;var c;c=W.current,W.current=null,Ye();try{if(r){var o=function(){throw Error()};if(Object.defineProperty(o.prototype,\"props\",{set:function(){throw Error()}}),typeof Reflect==\"object\"&&Reflect.construct){try{Reflect.construct(o,[])}catch(v){t=v}Reflect.construct(e,[],o)}else{try{o.call()}catch(v){t=v}e.call(o.prototype)}}else{try{throw Error()}catch(v){t=v}e()}}catch(v){if(v&&t&&typeof v.stack==\"string\"){for(var i=v.stack.split(`\n`),p=t.stack.split(`\n`),f=i.length-1,d=p.length-1;f>=1&&d>=0&&i[f]!==p[d];)d--;for(;f>=1&&d>=0;f--,d--)if(i[f]!==p[d]){if(f!==1||d!==1)do if(f--,d--,d<0||i[f]!==p[d]){var h=`\n`+i[f].replace(\" at new \",\" at \");return e.displayName&&h.includes(\"<anonymous>\")&&(h=h.replace(\"<anonymous>\",e.displayName)),typeof e==\"function\"&&S.set(e,h),h}while(f>=1&&d>=0);break}}}finally{$=!1,W.current=c,$e(),Error.prepareStackTrace=l}var N=e?e.displayName||e.name:\"\",ye=N?P(N):\"\";return typeof e==\"function\"&&S.set(e,ye),ye}function Ve(e,r,n){return se(e,!1)}function Le(e){var r=e.prototype;return!!(r&&r.isReactComponent)}function O(e,r,n){if(e==null)return\"\";if(typeof e==\"function\")return se(e,Le(e));if(typeof e==\"string\")return P(e);switch(e){case A:return P(\"Suspense\");case I:return P(\"SuspenseList\")}if(typeof e==\"object\")switch(e.$$typeof){case T:return Ve(e.render);case C:return O(e.type,r,n);case z:{var t=e,l=t._payload,c=t._init;try{return O(c(l),r,n)}catch{}}}return\"\"}var j=Object.prototype.hasOwnProperty,ue={},ce=E.ReactDebugCurrentFrame;function D(e){if(e){var r=e._owner,n=O(e.type,e._source,r?r.type:null);ce.setExtraStackFrame(n)}else ce.setExtraStackFrame(null)}function Ue(e,r,n,t,l){{var c=Function.call.bind(j);for(var o in e)if(c(e,o)){var i=void 0;try{if(typeof e[o]!=\"function\"){var p=Error((t||\"React class\")+\": \"+n+\" type `\"+o+\"` is invalid; it must be a function, usually from the `prop-types` package, but received `\"+typeof e[o]+\"`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.\");throw p.name=\"Invariant Violation\",p}i=e[o](r,o,t,n,null,\"SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED\")}catch(f){i=f}i&&!(i instanceof Error)&&(D(l),b(\"%s: type specification of %s `%s` is invalid; the type checker function must return `null` or an `Error` but returned a %s. You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument).\",t||\"React class\",n,o,typeof i),D(null)),i instanceof Error&&!(i.message in ue)&&(ue[i.message]=!0,D(l),b(\"Failed %s type: %s\",n,i.message),D(null))}}}var Be=Array.isArray;function M(e){return Be(e)}function qe(e){{var r=typeof Symbol==\"function\"&&Symbol.toStringTag,n=r&&e[Symbol.toStringTag]||e.constructor.name||\"Object\";return n}}function Ge(e){try{return fe(e),!1}catch{return!0}}function fe(e){return\"\"+e}function de(e){if(Ge(e))return b(\"The provided key is an unsupported type %s. This value must be coerced to a string before before using it here.\",qe(e)),fe(e)}var k=E.ReactCurrentOwner,Xe={key:!0,ref:!0,__self:!0,__source:!0},me,be,V;V={};function Ke(e){if(j.call(e,\"ref\")){var r=Object.getOwnPropertyDescriptor(e,\"ref\").get;if(r&&r.isReactWarning)return!1}return e.ref!==void 0}function He(e){if(j.call(e,\"key\")){var r=Object.getOwnPropertyDescriptor(e,\"key\").get;if(r&&r.isReactWarning)return!1}return e.key!==void 0}function Je(e,r){if(typeof e.ref==\"string\"&&k.current&&r&&k.current.stateNode!==r){var n=g(k.current.type);V[n]||(b('Component \"%s\" contains the string ref \"%s\". Support for string refs will be removed in a future major release. This case cannot be automatically converted to an arrow function. We ask you to manually fix this case by using useRef() or createRef() instead. Learn more about using refs safely here: https://reactjs.org/link/strict-mode-string-ref',g(k.current.type),e.ref),V[n]=!0)}}function Ze(e,r){{var n=function(){me||(me=!0,b(\"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)\",r))};n.isReactWarning=!0,Object.defineProperty(e,\"key\",{get:n,configurable:!0})}}function Qe(e,r){{var n=function(){be||(be=!0,b(\"%s: `ref` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)\",r))};n.isReactWarning=!0,Object.defineProperty(e,\"ref\",{get:n,configurable:!0})}}var er=function(e,r,n,t,l,c,o){var i={$$typeof:a,type:e,key:r,ref:n,props:o,_owner:c};return i._store={},Object.defineProperty(i._store,\"validated\",{configurable:!1,enumerable:!1,writable:!0,value:!1}),Object.defineProperty(i,\"_self\",{configurable:!1,enumerable:!1,writable:!1,value:t}),Object.defineProperty(i,\"_source\",{configurable:!1,enumerable:!1,writable:!1,value:l}),Object.freeze&&(Object.freeze(i.props),Object.freeze(i)),i};function rr(e,r,n,t,l){{var c,o={},i=null,p=null;n!==void 0&&(de(n),i=\"\"+n),He(r)&&(de(r.key),i=\"\"+r.key),Ke(r)&&(p=r.ref,Je(r,l));for(c in r)j.call(r,c)&&!Xe.hasOwnProperty(c)&&(o[c]=r[c]);if(e&&e.defaultProps){var f=e.defaultProps;for(c in f)o[c]===void 0&&(o[c]=f[c])}if(i||p){var d=typeof e==\"function\"?e.displayName||e.name||\"Unknown\":e;i&&Ze(o,d),p&&Qe(o,d)}return er(e,i,p,l,t,k.current,o)}}var L=E.ReactCurrentOwner,pe=E.ReactDebugCurrentFrame;function x(e){if(e){var r=e._owner,n=O(e.type,e._source,r?r.type:null);pe.setExtraStackFrame(n)}else pe.setExtraStackFrame(null)}var U;U=!1;function B(e){return typeof e==\"object\"&&e!==null&&e.$$typeof===a}function he(){{if(L.current){var e=g(L.current.type);if(e)return`\n\nCheck the render method of \\``+e+\"`.\"}return\"\"}}function nr(e){{if(e!==void 0){var r=e.fileName.replace(/^.*[\\\\\\/]/,\"\"),n=e.lineNumber;return`\n\nCheck your code at `+r+\":\"+n+\".\"}return\"\"}}var ge={};function tr(e){{var r=he();if(!r){var n=typeof e==\"string\"?e:e.displayName||e.name;n&&(r=`\n\nCheck the top-level render call using <`+n+\">.\")}return r}}function ve(e,r){{if(!e._store||e._store.validated||e.key!=null)return;e._store.validated=!0;var n=tr(r);if(ge[n])return;ge[n]=!0;var t=\"\";e&&e._owner&&e._owner!==L.current&&(t=\" It was passed a child from \"+g(e._owner.type)+\".\"),x(e),b('Each child in a list should have a unique \"key\" prop.%s%s See https://reactjs.org/link/warning-keys for more information.',n,t),x(null)}}function _e(e,r){{if(typeof e!=\"object\")return;if(M(e))for(var n=0;n<e.length;n++){var t=e[n];B(t)&&ve(t,r)}else if(B(e))e._store&&(e._store.validated=!0);else if(e){var l=Se(e);if(typeof l==\"function\"&&l!==e.entries)for(var c=l.call(e),o;!(o=c.next()).done;)B(o.value)&&ve(o.value,r)}}}function ar(e){{var r=e.type;if(r==null||typeof r==\"string\")return;var n;if(typeof r==\"function\")n=r.propTypes;else if(typeof r==\"object\"&&(r.$$typeof===T||r.$$typeof===C))n=r.propTypes;else return;if(n){var t=g(r);Ue(n,e.props,\"prop\",t,e)}else if(r.PropTypes!==void 0&&!U){U=!0;var l=g(r);b(\"Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?\",l||\"Unknown\")}typeof r.getDefaultProps==\"function\"&&!r.getDefaultProps.isReactClassApproved&&b(\"getDefaultProps is only used on classic React.createClass definitions. Use a static property named `defaultProps` instead.\")}}function ir(e){{for(var r=Object.keys(e.props),n=0;n<r.length;n++){var t=r[n];if(t!==\"children\"&&t!==\"key\"){x(e),b(\"Invalid prop `%s` supplied to `React.Fragment`. React.Fragment can only have `key` and `children` props.\",t),x(null);break}}e.ref!==null&&(x(e),b(\"Invalid attribute `ref` supplied to `React.Fragment`.\"),x(null))}}function or(e,r,n,t,l,c){{var o=ze(e);if(!o){var i=\"\";(e===void 0||typeof e==\"object\"&&e!==null&&Object.keys(e).length===0)&&(i+=\" You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.\");var p=nr(l);p?i+=p:i+=he();var f;e===null?f=\"null\":M(e)?f=\"array\":e!==void 0&&e.$$typeof===a?(f=\"<\"+(g(e.type)||\"Unknown\")+\" />\",i=\" Did you accidentally export a JSX literal instead of a component?\"):f=typeof e,b(\"React.jsx: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: %s.%s\",f,i)}var d=rr(e,r,n,l,c);if(d==null)return d;if(o){var h=r.children;if(h!==void 0)if(t)if(M(h)){for(var N=0;N<h.length;N++)_e(h[N],e);Object.freeze&&Object.freeze(h)}else b(\"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\");else _e(h,e)}return e===y?ir(d):ar(d),d}}var lr=or;G.Fragment=y,G.jsxDEV=lr})()});var ke=q((Nr,Re)=>{\"use strict\";Re.exports=Ne()});var yr={};mr(yr,{default:()=>vr,frontmatter:()=>hr});var s=br(ke()),hr={title:\"Deezer unveils its new logo in 2023\",excerpt:\"The year 2023 marks a new era for Deezer, the music streaming giant. With its bold rebranding embodied by a logo featuring a vibrant heart.\",date:\"2023-11-15\",updated:\"2023-11-15\",category:\"Brand identity\",cover:\"/images/blog/logo-deezer-2023/main-logo-deezer-2023.png\"};function Te(u){let a=Object.assign({p:\"p\",h2:\"h2\",ol:\"ol\",li:\"li\",strong:\"strong\"},u.components),{Image:m}=a;return m||_r(\"Image\",!0,\"47:1-50:3\"),(0,s.jsxDEV)(s.Fragment,{children:[(0,s.jsxDEV)(a.p,{children:\"This new purple heart splits opinion, but the community-centered approach aligns perfectly with Deezer\\u2019s promise. We explain why\\u2014and how the logo could earn its place.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-6a89e259-7d15-456f-b839-389f800a6589.mdx\",lineNumber:10,columnNumber:1},this),`\n`,(0,s.jsxDEV)(a.h2,{children:\"Why change?\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-6a89e259-7d15-456f-b839-389f800a6589.mdx\",lineNumber:12,columnNumber:1},this),`\n`,(0,s.jsxDEV)(a.p,{children:\"In 2023, Deezer was looking for two things:\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-6a89e259-7d15-456f-b839-389f800a6589.mdx\",lineNumber:13,columnNumber:1},this),`\n`,(0,s.jsxDEV)(a.ol,{children:[`\n`,(0,s.jsxDEV)(a.li,{children:\"Clarify its mission to become \\u201Cthe home of music\\u201D rather than just another streaming service.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-6a89e259-7d15-456f-b839-389f800a6589.mdx\",lineNumber:14,columnNumber:1},this),`\n`,(0,s.jsxDEV)(a.li,{children:\"Differentiate itself in a saturated market (Spotify and Apple Music) with a strong, easily adaptable symbol.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-6a89e259-7d15-456f-b839-389f800a6589.mdx\",lineNumber:15,columnNumber:1},this),`\n`]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-6a89e259-7d15-456f-b839-389f800a6589.mdx\",lineNumber:14,columnNumber:1},this),`\n`,(0,s.jsxDEV)(a.p,{children:\"So, the redesign is less about aesthetics and more about repositioning.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-6a89e259-7d15-456f-b839-389f800a6589.mdx\",lineNumber:17,columnNumber:1},this),`\n`,(0,s.jsxDEV)(\"video\",{src:\"/images/blog/logo-deezer-2023/affiches-animees-deezer.webm\",playsInline:!0,autoPlay:!0,muted:!0,loop:!0,style:{width:\"100%\"}},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-6a89e259-7d15-456f-b839-389f800a6589.mdx\",lineNumber:19,columnNumber:1},this),`\n`,(0,s.jsxDEV)(a.h2,{children:\"A pulsing heart\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-6a89e259-7d15-456f-b839-389f800a6589.mdx\",lineNumber:28,columnNumber:1},this),`\n`,(0,s.jsxDEV)(a.p,{children:\"Deezer\\u2019s new symbol, an animated purple heart, literally embodies their message: \\u201CFeel the music.\\u201D\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-6a89e259-7d15-456f-b839-389f800a6589.mdx\",lineNumber:30,columnNumber:1},this),`\n`,(0,s.jsxDEV)(a.p,{children:\"The animation syncs to the tempo of the current track and immerses the user in the platform\\u2019s universe. Visually, this heart is instantly memorable and contrasts with the dominant hues used by other streaming services, like Spotify\\u2019s green.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-6a89e259-7d15-456f-b839-389f800a6589.mdx\",lineNumber:32,columnNumber:1},this),`\n`,(0,s.jsxDEV)(\"video\",{src:\"/images/blog/logo-deezer-2023/animation-logo-deezer.webm\",playsInline:!0,autoPlay:!0,muted:!0,loop:!0,style:{width:\"100%\"}},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-6a89e259-7d15-456f-b839-389f800a6589.mdx\",lineNumber:34,columnNumber:1},this),`\n`,(0,s.jsxDEV)(a.p,{children:[\"To accompany this icon, Deezer introduces a \",(0,s.jsxDEV)(a.strong,{children:\"custom typeface\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-6a89e259-7d15-456f-b839-389f800a6589.mdx\",lineNumber:43,columnNumber:45},this),\": Deezer Sans.\"]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-6a89e259-7d15-456f-b839-389f800a6589.mdx\",lineNumber:43,columnNumber:1},this),`\n`,(0,s.jsxDEV)(a.p,{children:\"Its curves mirror the logo\\u2019s lines\\u2014narrow enough for dense interfaces, wide enough to support a strong message on a poster.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-6a89e259-7d15-456f-b839-389f800a6589.mdx\",lineNumber:45,columnNumber:1},this),`\n`,(0,s.jsxDEV)(m,{src:\"/images/blog/logo-deezer-2023/affiches-deezer.jpg\",alt:\"Posters of the new Deezer logo\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-6a89e259-7d15-456f-b839-389f800a6589.mdx\",lineNumber:47,columnNumber:1},this),`\n`,(0,s.jsxDEV)(\"video\",{src:\"/images/blog/logo-deezer-2023/animation-video-deezer.webm\",playsInline:!0,autoPlay:!0,muted:!0,loop:!0,style:{width:\"100%\"}},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-6a89e259-7d15-456f-b839-389f800a6589.mdx\",lineNumber:52,columnNumber:1},this),`\n`,(0,s.jsxDEV)(a.h2,{children:\"An identity to attract a younger audience\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-6a89e259-7d15-456f-b839-389f800a6589.mdx\",lineNumber:61,columnNumber:1},this),`\n`,(0,s.jsxDEV)(a.p,{children:[\"Among creatives, the risk-taking is appealing: abandoning the old equalizer logo for an animated heart was not an obvious choice. In forums, some applaud the boldness; others, more skeptical, find the visual too close to \",(0,s.jsxDEV)(a.strong,{children:\"dating app conventions\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-6a89e259-7d15-456f-b839-389f800a6589.mdx\",lineNumber:63,columnNumber:222},this),\".\"]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-6a89e259-7d15-456f-b839-389f800a6589.mdx\",lineNumber:63,columnNumber:1},this),`\n`,(0,s.jsxDEV)(a.p,{children:\"Subscribers are divided: many appreciate the warmer approach, while nostalgic fans miss the old logo.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-6a89e259-7d15-456f-b839-389f800a6589.mdx\",lineNumber:65,columnNumber:1},this),`\n`,(0,s.jsxDEV)(a.p,{children:\"In terms of memorability, the wager pays off: the icon works as well on a smartwatch as on a city billboard. The remaining challenge is maintaining recognition without animation\\u2014on print, favicons, or merchandise\\u2014by relying on a coherent visual universe: waves, purple halo, micro-motions.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-6a89e259-7d15-456f-b839-389f800a6589.mdx\",lineNumber:67,columnNumber:1},this),`\n`,(0,s.jsxDEV)(m,{src:\"/images/blog/logo-deezer-2023/illustrations-deezer.webp\",alt:\"Illustrations of Deezer\\u2019s visual identity\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-6a89e259-7d15-456f-b839-389f800a6589.mdx\",lineNumber:69,columnNumber:1},this)]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-6a89e259-7d15-456f-b839-389f800a6589.mdx\",lineNumber:1,columnNumber:1},this)}function gr(u={}){let{wrapper:a}=u.components||{};return a?(0,s.jsxDEV)(a,Object.assign({},u,{children:(0,s.jsxDEV)(Te,u,void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-6a89e259-7d15-456f-b839-389f800a6589.mdx\"},this)}),void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-6a89e259-7d15-456f-b839-389f800a6589.mdx\"},this):Te(u)}var vr=gr;function _r(u,a,m){throw new Error(\"Expected \"+(a?\"component\":\"object\")+\" `\"+u+\"` to be defined: you likely forgot to import, pass, or provide it.\"+(m?\"\\nIt\\u2019s referenced in your code at `\"+m+\"` in `/home/<USER>/git/kapreon-website-1/src/content/blog/en/_mdx_bundler_entry_point-6a89e259-7d15-456f-b839-389f800a6589.mdx`\":\"\"))}return pr(yr);})();\n/*! Bundled license information:\n\nreact/cjs/react-jsx-dev-runtime.development.js:\n  (**\n   * @license React\n   * react-jsx-dev-runtime.development.js\n   *\n   * Copyright (c) Facebook, Inc. and its affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *)\n*/\n;return Component;"}, "_id": "blog/en/logo-deezer-2023.mdx", "_raw": {"sourceFilePath": "blog/en/logo-deezer-2023.mdx", "sourceFileName": "logo-deezer-2023.mdx", "sourceFileDir": "blog/en", "contentType": "mdx", "flattenedPath": "blog/en/logo-deezer-2023"}, "type": "Post", "locale": "en", "slug": "logo-deezer-2023"}, {"title": "11 erreurs fréquentes sur un site web", "excerpt": "De nos jours, un site web est un outil indispensable pour toute entreprise souhaitant se démarquer et atteindre ses objectifs. Cependant, créer un site web performant peut être un véritable challenge. Il existe des erreurs fréquentes qui peuvent nuire à votre site.", "date": "2025-05-17T00:00:00.000Z", "updated": "2025-05-20T00:00:00.000Z", "category": "Développement web", "cover": "/images/blog/11-erreurs-site-web/main-erreurs-site-web.webp", "published": false, "body": {"raw": "\n\nDe nos jours, un site web est un outil indispensable pour toute entreprise souhaitant se démarquer et atteindre ses objectifs. Cependant, créer un site web performant peut être un véritable challenge. Il existe des erreurs fréquentes qui peuvent nuire à votre site.\n\nDans cet article, nous vous présenterons 11 fréquentes sur un site web et nous vous donnerons des conseils pour les éviter.\n\n## 1. Utiliser des polices d’écriture peu lisibles\n\nIl est tentant d’utiliser des polices d’écriture originales pour se démarquer, mais cela peut **nuire à la lisibilité** de votre site. La lisibilité est cruciale pour permettre à vos visiteurs de comprendre rapidement et facilement le message que vous souhaitez transmettre. En utilisant des polices extravagantes, vous risquez non seulement de perdre l’attention de vos visiteurs, mais également de donner une image peu professionnelle de votre entreprise. \n\n", "code": "var Component=(()=>{var sr=Object.create;var N=Object.defineProperty;var lr=Object.getOwnPropertyDescriptor;var cr=Object.getOwnPropertyNames;var fr=Object.getPrototypeOf,dr=Object.prototype.hasOwnProperty;var z=(l,u)=>()=>(u||l((u={exports:{}}).exports,u),u.exports),vr=(l,u)=>{for(var m in u)N(l,m,{get:u[m],enumerable:!0})},ye=(l,u,m,E)=>{if(u&&typeof u==\"object\"||typeof u==\"function\")for(let _ of cr(u))!dr.call(l,_)&&_!==m&&N(l,_,{get:()=>u[_],enumerable:!(E=lr(u,_))||E.enumerable});return l};var br=(l,u,m)=>(m=l!=null?sr(fr(l)):{},ye(u||!l||!l.__esModule?N(m,\"default\",{value:l,enumerable:!0}):m,l)),pr=l=>ye(N({},\"__esModule\",{value:!0}),l);var we=z((yr,Re)=>{Re.exports=React});var Te=z(G=>{\"use strict\";(function(){\"use strict\";var l=we(),u=Symbol.for(\"react.element\"),m=Symbol.for(\"react.portal\"),E=Symbol.for(\"react.fragment\"),_=Symbol.for(\"react.strict_mode\"),X=Symbol.for(\"react.profiler\"),K=Symbol.for(\"react.provider\"),H=Symbol.for(\"react.context\"),O=Symbol.for(\"react.forward_ref\"),A=Symbol.for(\"react.suspense\"),I=Symbol.for(\"react.suspense_list\"),P=Symbol.for(\"react.memo\"),Y=Symbol.for(\"react.lazy\"),Pe=Symbol.for(\"react.offscreen\"),J=Symbol.iterator,Se=\"@@iterator\";function je(e){if(e===null||typeof e!=\"object\")return null;var r=J&&e[J]||e[Se];return typeof r==\"function\"?r:null}var R=l.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;function d(e){{for(var r=arguments.length,t=new Array(r>1?r-1:0),n=1;n<r;n++)t[n-1]=arguments[n];ke(\"error\",e,t)}}function ke(e,r,t){{var n=R.ReactDebugCurrentFrame,o=n.getStackAddendum();o!==\"\"&&(r+=\"%s\",t=t.concat([o]));var s=t.map(function(i){return String(i)});s.unshift(\"Warning: \"+r),Function.prototype.apply.call(console[e],console,s)}}var De=!1,Fe=!1,Ne=!1,Ae=!1,Ie=!1,Z;Z=Symbol.for(\"react.module.reference\");function Ye(e){return!!(typeof e==\"string\"||typeof e==\"function\"||e===E||e===X||Ie||e===_||e===A||e===I||Ae||e===Pe||De||Fe||Ne||typeof e==\"object\"&&e!==null&&(e.$$typeof===Y||e.$$typeof===P||e.$$typeof===K||e.$$typeof===H||e.$$typeof===O||e.$$typeof===Z||e.getModuleId!==void 0))}function $e(e,r,t){var n=e.displayName;if(n)return n;var o=r.displayName||r.name||\"\";return o!==\"\"?t+\"(\"+o+\")\":t}function Q(e){return e.displayName||\"Context\"}function g(e){if(e==null)return null;if(typeof e.tag==\"number\"&&d(\"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"),typeof e==\"function\")return e.displayName||e.name||null;if(typeof e==\"string\")return e;switch(e){case E:return\"Fragment\";case m:return\"Portal\";case X:return\"Profiler\";case _:return\"StrictMode\";case A:return\"Suspense\";case I:return\"SuspenseList\"}if(typeof e==\"object\")switch(e.$$typeof){case H:var r=e;return Q(r)+\".Consumer\";case K:var t=e;return Q(t._context)+\".Provider\";case O:return $e(e,e.render,\"ForwardRef\");case P:var n=e.displayName||null;return n!==null?n:g(e.type)||\"Memo\";case Y:{var o=e,s=o._payload,i=o._init;try{return g(i(s))}catch{return null}}}return null}var y=Object.assign,C=0,ee,re,te,ne,ae,ie,oe;function ue(){}ue.__reactDisabledLog=!0;function We(){{if(C===0){ee=console.log,re=console.info,te=console.warn,ne=console.error,ae=console.group,ie=console.groupCollapsed,oe=console.groupEnd;var e={configurable:!0,enumerable:!0,value:ue,writable:!0};Object.defineProperties(console,{info:e,log:e,warn:e,error:e,group:e,groupCollapsed:e,groupEnd:e})}C++}}function Me(){{if(C--,C===0){var e={configurable:!0,enumerable:!0,writable:!0};Object.defineProperties(console,{log:y({},e,{value:ee}),info:y({},e,{value:re}),warn:y({},e,{value:te}),error:y({},e,{value:ne}),group:y({},e,{value:ae}),groupCollapsed:y({},e,{value:ie}),groupEnd:y({},e,{value:oe})})}C<0&&d(\"disabledDepth fell below zero. This is a bug in React. Please file an issue.\")}}var $=R.ReactCurrentDispatcher,W;function S(e,r,t){{if(W===void 0)try{throw Error()}catch(o){var n=o.stack.trim().match(/\\n( *(at )?)/);W=n&&n[1]||\"\"}return`\n`+W+e}}var M=!1,j;{var Ve=typeof WeakMap==\"function\"?WeakMap:Map;j=new Ve}function se(e,r){if(!e||M)return\"\";{var t=j.get(e);if(t!==void 0)return t}var n;M=!0;var o=Error.prepareStackTrace;Error.prepareStackTrace=void 0;var s;s=$.current,$.current=null,We();try{if(r){var i=function(){throw Error()};if(Object.defineProperty(i.prototype,\"props\",{set:function(){throw Error()}}),typeof Reflect==\"object\"&&Reflect.construct){try{Reflect.construct(i,[])}catch(h){n=h}Reflect.construct(e,[],i)}else{try{i.call()}catch(h){n=h}e.call(i.prototype)}}else{try{throw Error()}catch(h){n=h}e()}}catch(h){if(h&&n&&typeof h.stack==\"string\"){for(var a=h.stack.split(`\n`),v=n.stack.split(`\n`),c=a.length-1,f=v.length-1;c>=1&&f>=0&&a[c]!==v[f];)f--;for(;c>=1&&f>=0;c--,f--)if(a[c]!==v[f]){if(c!==1||f!==1)do if(c--,f--,f<0||a[c]!==v[f]){var b=`\n`+a[c].replace(\" at new \",\" at \");return e.displayName&&b.includes(\"<anonymous>\")&&(b=b.replace(\"<anonymous>\",e.displayName)),typeof e==\"function\"&&j.set(e,b),b}while(c>=1&&f>=0);break}}}finally{M=!1,$.current=s,Me(),Error.prepareStackTrace=o}var T=e?e.displayName||e.name:\"\",Ee=T?S(T):\"\";return typeof e==\"function\"&&j.set(e,Ee),Ee}function Le(e,r,t){return se(e,!1)}function qe(e){var r=e.prototype;return!!(r&&r.isReactComponent)}function k(e,r,t){if(e==null)return\"\";if(typeof e==\"function\")return se(e,qe(e));if(typeof e==\"string\")return S(e);switch(e){case A:return S(\"Suspense\");case I:return S(\"SuspenseList\")}if(typeof e==\"object\")switch(e.$$typeof){case O:return Le(e.render);case P:return k(e.type,r,t);case Y:{var n=e,o=n._payload,s=n._init;try{return k(s(o),r,t)}catch{}}}return\"\"}var D=Object.prototype.hasOwnProperty,le={},ce=R.ReactDebugCurrentFrame;function F(e){if(e){var r=e._owner,t=k(e.type,e._source,r?r.type:null);ce.setExtraStackFrame(t)}else ce.setExtraStackFrame(null)}function Ue(e,r,t,n,o){{var s=Function.call.bind(D);for(var i in e)if(s(e,i)){var a=void 0;try{if(typeof e[i]!=\"function\"){var v=Error((n||\"React class\")+\": \"+t+\" type `\"+i+\"` is invalid; it must be a function, usually from the `prop-types` package, but received `\"+typeof e[i]+\"`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.\");throw v.name=\"Invariant Violation\",v}a=e[i](r,i,n,t,null,\"SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED\")}catch(c){a=c}a&&!(a instanceof Error)&&(F(o),d(\"%s: type specification of %s `%s` is invalid; the type checker function must return `null` or an `Error` but returned a %s. You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument).\",n||\"React class\",t,i,typeof a),F(null)),a instanceof Error&&!(a.message in le)&&(le[a.message]=!0,F(o),d(\"Failed %s type: %s\",t,a.message),F(null))}}}var Be=Array.isArray;function V(e){return Be(e)}function ze(e){{var r=typeof Symbol==\"function\"&&Symbol.toStringTag,t=r&&e[Symbol.toStringTag]||e.constructor.name||\"Object\";return t}}function Ge(e){try{return fe(e),!1}catch{return!0}}function fe(e){return\"\"+e}function de(e){if(Ge(e))return d(\"The provided key is an unsupported type %s. This value must be coerced to a string before before using it here.\",ze(e)),fe(e)}var x=R.ReactCurrentOwner,Xe={key:!0,ref:!0,__self:!0,__source:!0},ve,be,L;L={};function Ke(e){if(D.call(e,\"ref\")){var r=Object.getOwnPropertyDescriptor(e,\"ref\").get;if(r&&r.isReactWarning)return!1}return e.ref!==void 0}function He(e){if(D.call(e,\"key\")){var r=Object.getOwnPropertyDescriptor(e,\"key\").get;if(r&&r.isReactWarning)return!1}return e.key!==void 0}function Je(e,r){if(typeof e.ref==\"string\"&&x.current&&r&&x.current.stateNode!==r){var t=g(x.current.type);L[t]||(d('Component \"%s\" contains the string ref \"%s\". Support for string refs will be removed in a future major release. This case cannot be automatically converted to an arrow function. We ask you to manually fix this case by using useRef() or createRef() instead. Learn more about using refs safely here: https://reactjs.org/link/strict-mode-string-ref',g(x.current.type),e.ref),L[t]=!0)}}function Ze(e,r){{var t=function(){ve||(ve=!0,d(\"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)\",r))};t.isReactWarning=!0,Object.defineProperty(e,\"key\",{get:t,configurable:!0})}}function Qe(e,r){{var t=function(){be||(be=!0,d(\"%s: `ref` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)\",r))};t.isReactWarning=!0,Object.defineProperty(e,\"ref\",{get:t,configurable:!0})}}var er=function(e,r,t,n,o,s,i){var a={$$typeof:u,type:e,key:r,ref:t,props:i,_owner:s};return a._store={},Object.defineProperty(a._store,\"validated\",{configurable:!1,enumerable:!1,writable:!0,value:!1}),Object.defineProperty(a,\"_self\",{configurable:!1,enumerable:!1,writable:!1,value:n}),Object.defineProperty(a,\"_source\",{configurable:!1,enumerable:!1,writable:!1,value:o}),Object.freeze&&(Object.freeze(a.props),Object.freeze(a)),a};function rr(e,r,t,n,o){{var s,i={},a=null,v=null;t!==void 0&&(de(t),a=\"\"+t),He(r)&&(de(r.key),a=\"\"+r.key),Ke(r)&&(v=r.ref,Je(r,o));for(s in r)D.call(r,s)&&!Xe.hasOwnProperty(s)&&(i[s]=r[s]);if(e&&e.defaultProps){var c=e.defaultProps;for(s in c)i[s]===void 0&&(i[s]=c[s])}if(a||v){var f=typeof e==\"function\"?e.displayName||e.name||\"Unknown\":e;a&&Ze(i,f),v&&Qe(i,f)}return er(e,a,v,o,n,x.current,i)}}var q=R.ReactCurrentOwner,pe=R.ReactDebugCurrentFrame;function w(e){if(e){var r=e._owner,t=k(e.type,e._source,r?r.type:null);pe.setExtraStackFrame(t)}else pe.setExtraStackFrame(null)}var U;U=!1;function B(e){return typeof e==\"object\"&&e!==null&&e.$$typeof===u}function me(){{if(q.current){var e=g(q.current.type);if(e)return`\n\nCheck the render method of \\``+e+\"`.\"}return\"\"}}function tr(e){{if(e!==void 0){var r=e.fileName.replace(/^.*[\\\\\\/]/,\"\"),t=e.lineNumber;return`\n\nCheck your code at `+r+\":\"+t+\".\"}return\"\"}}var ge={};function nr(e){{var r=me();if(!r){var t=typeof e==\"string\"?e:e.displayName||e.name;t&&(r=`\n\nCheck the top-level render call using <`+t+\">.\")}return r}}function he(e,r){{if(!e._store||e._store.validated||e.key!=null)return;e._store.validated=!0;var t=nr(r);if(ge[t])return;ge[t]=!0;var n=\"\";e&&e._owner&&e._owner!==q.current&&(n=\" It was passed a child from \"+g(e._owner.type)+\".\"),w(e),d('Each child in a list should have a unique \"key\" prop.%s%s See https://reactjs.org/link/warning-keys for more information.',t,n),w(null)}}function _e(e,r){{if(typeof e!=\"object\")return;if(V(e))for(var t=0;t<e.length;t++){var n=e[t];B(n)&&he(n,r)}else if(B(e))e._store&&(e._store.validated=!0);else if(e){var o=je(e);if(typeof o==\"function\"&&o!==e.entries)for(var s=o.call(e),i;!(i=s.next()).done;)B(i.value)&&he(i.value,r)}}}function ar(e){{var r=e.type;if(r==null||typeof r==\"string\")return;var t;if(typeof r==\"function\")t=r.propTypes;else if(typeof r==\"object\"&&(r.$$typeof===O||r.$$typeof===P))t=r.propTypes;else return;if(t){var n=g(r);Ue(t,e.props,\"prop\",n,e)}else if(r.PropTypes!==void 0&&!U){U=!0;var o=g(r);d(\"Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?\",o||\"Unknown\")}typeof r.getDefaultProps==\"function\"&&!r.getDefaultProps.isReactClassApproved&&d(\"getDefaultProps is only used on classic React.createClass definitions. Use a static property named `defaultProps` instead.\")}}function ir(e){{for(var r=Object.keys(e.props),t=0;t<r.length;t++){var n=r[t];if(n!==\"children\"&&n!==\"key\"){w(e),d(\"Invalid prop `%s` supplied to `React.Fragment`. React.Fragment can only have `key` and `children` props.\",n),w(null);break}}e.ref!==null&&(w(e),d(\"Invalid attribute `ref` supplied to `React.Fragment`.\"),w(null))}}function or(e,r,t,n,o,s){{var i=Ye(e);if(!i){var a=\"\";(e===void 0||typeof e==\"object\"&&e!==null&&Object.keys(e).length===0)&&(a+=\" You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.\");var v=tr(o);v?a+=v:a+=me();var c;e===null?c=\"null\":V(e)?c=\"array\":e!==void 0&&e.$$typeof===u?(c=\"<\"+(g(e.type)||\"Unknown\")+\" />\",a=\" Did you accidentally export a JSX literal instead of a component?\"):c=typeof e,d(\"React.jsx: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: %s.%s\",c,a)}var f=rr(e,r,t,o,s);if(f==null)return f;if(i){var b=r.children;if(b!==void 0)if(n)if(V(b)){for(var T=0;T<b.length;T++)_e(b[T],e);Object.freeze&&Object.freeze(b)}else d(\"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\");else _e(b,e)}return e===E?ir(f):ar(f),f}}var ur=or;G.Fragment=E,G.jsxDEV=ur})()});var xe=z((wr,Ce)=>{\"use strict\";Ce.exports=Te()});var _r={};vr(_r,{default:()=>hr,frontmatter:()=>mr});var p=br(xe()),mr={title:\"11 erreurs fr\\xE9quentes sur un site web\",excerpt:\"De nos jours, un site web est un outil indispensable pour toute entreprise souhaitant se d\\xE9marquer et atteindre ses objectifs. Cependant, cr\\xE9er un site web performant peut \\xEAtre un v\\xE9ritable challenge. Il existe des erreurs fr\\xE9quentes qui peuvent nuire \\xE0 votre site.\",date:\"2025-05-17\",updated:\"2025-05-20\",category:\"D\\xE9veloppement web\",cover:\"/images/blog/11-erreurs-site-web/main-erreurs-site-web.webp\",published:!1};function Oe(l){let u=Object.assign({p:\"p\",h2:\"h2\",strong:\"strong\"},l.components);return(0,p.jsxDEV)(p.Fragment,{children:[(0,p.jsxDEV)(u.p,{children:\"De nos jours, un site web est un outil indispensable pour toute entreprise souhaitant se d\\xE9marquer et atteindre ses objectifs. Cependant, cr\\xE9er un site web performant peut \\xEAtre un v\\xE9ritable challenge. Il existe des erreurs fr\\xE9quentes qui peuvent nuire \\xE0 votre site.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-1031abaf-b6c1-4686-946d-22493bc29c07.mdx\",lineNumber:13,columnNumber:1},this),`\n`,(0,p.jsxDEV)(u.p,{children:\"Dans cet article, nous vous pr\\xE9senterons 11 fr\\xE9quentes sur un site web et nous vous donnerons des conseils pour les \\xE9viter.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-1031abaf-b6c1-4686-946d-22493bc29c07.mdx\",lineNumber:15,columnNumber:1},this),`\n`,(0,p.jsxDEV)(u.h2,{children:\"1. Utiliser des polices d\\u2019\\xE9criture peu lisibles\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-1031abaf-b6c1-4686-946d-22493bc29c07.mdx\",lineNumber:17,columnNumber:1},this),`\n`,(0,p.jsxDEV)(u.p,{children:[\"Il est tentant d\\u2019utiliser des polices d\\u2019\\xE9criture originales pour se d\\xE9marquer, mais cela peut \",(0,p.jsxDEV)(u.strong,{children:\"nuire \\xE0 la lisibilit\\xE9\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-1031abaf-b6c1-4686-946d-22493bc29c07.mdx\",lineNumber:19,columnNumber:95},this),\" de votre site. La lisibilit\\xE9 est cruciale pour permettre \\xE0 vos visiteurs de comprendre rapidement et facilement le message que vous souhaitez transmettre. En utilisant des polices extravagantes, vous risquez non seulement de perdre l\\u2019attention de vos visiteurs, mais \\xE9galement de donner une image peu professionnelle de votre entreprise.\"]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-1031abaf-b6c1-4686-946d-22493bc29c07.mdx\",lineNumber:19,columnNumber:1},this)]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-1031abaf-b6c1-4686-946d-22493bc29c07.mdx\",lineNumber:1,columnNumber:1},this)}function gr(l={}){let{wrapper:u}=l.components||{};return u?(0,p.jsxDEV)(u,Object.assign({},l,{children:(0,p.jsxDEV)(Oe,l,void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-1031abaf-b6c1-4686-946d-22493bc29c07.mdx\"},this)}),void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-1031abaf-b6c1-4686-946d-22493bc29c07.mdx\"},this):Oe(l)}var hr=gr;return pr(_r);})();\n/*! Bundled license information:\n\nreact/cjs/react-jsx-dev-runtime.development.js:\n  (**\n   * @license React\n   * react-jsx-dev-runtime.development.js\n   *\n   * Copyright (c) Facebook, Inc. and its affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *)\n*/\n;return Component;"}, "_id": "blog/fr/11-erreurs-site-web.mdx", "_raw": {"sourceFilePath": "blog/fr/11-erreurs-site-web.mdx", "sourceFileName": "11-erreurs-site-web.mdx", "sourceFileDir": "blog/fr", "contentType": "mdx", "flattenedPath": "blog/fr/11-erreurs-site-web"}, "type": "Post", "locale": "fr", "slug": "11-erreurs-site-web"}, {"title": "6 raisons pour savoir si votre site web à besoin d’une refonte", "excerpt": "Si vous utilisez le même site web depuis un certain temps ou s’il a été créé à l’époque où le responsive design n’existait pas encore, il y a de fortes chances que votre site ait besoin d’être retravaillé.", "date": "2025-05-17T00:00:00.000Z", "updated": "2025-05-20T00:00:00.000Z", "category": "Développement web", "cover": "/images/blog/6-raisons-refonte-site-web/main-refonte-site-web.webp", "published": false, "body": {"raw": "\n", "code": "var Component=(()=>{var sr=Object.create;var A=Object.defineProperty;var lr=Object.getOwnPropertyDescriptor;var fr=Object.getOwnPropertyNames;var cr=Object.getPrototypeOf,dr=Object.prototype.hasOwnProperty;var z=(s,l)=>()=>(l||s((l={exports:{}}).exports,l),l.exports),vr=(s,l)=>{for(var b in l)A(s,b,{get:l[b],enumerable:!0})},_e=(s,l,b,E)=>{if(l&&typeof l==\"object\"||typeof l==\"function\")for(let h of fr(l))!dr.call(s,h)&&h!==b&&A(s,h,{get:()=>l[h],enumerable:!(E=lr(l,h))||E.enumerable});return s};var pr=(s,l,b)=>(b=s!=null?sr(cr(s)):{},_e(l||!s||!s.__esModule?A(b,\"default\",{value:s,enumerable:!0}):b,s)),br=s=>_e(A({},\"__esModule\",{value:!0}),s);var Te=z((_r,Re)=>{Re.exports=React});var we=z(G=>{\"use strict\";(function(){\"use strict\";var s=Te(),l=Symbol.for(\"react.element\"),b=Symbol.for(\"react.portal\"),E=Symbol.for(\"react.fragment\"),h=Symbol.for(\"react.strict_mode\"),X=Symbol.for(\"react.profiler\"),K=Symbol.for(\"react.provider\"),H=Symbol.for(\"react.context\"),P=Symbol.for(\"react.forward_ref\"),I=Symbol.for(\"react.suspense\"),Y=Symbol.for(\"react.suspense_list\"),S=Symbol.for(\"react.memo\"),$=Symbol.for(\"react.lazy\"),Se=Symbol.for(\"react.offscreen\"),J=Symbol.iterator,xe=\"@@iterator\";function je(e){if(e===null||typeof e!=\"object\")return null;var r=J&&e[J]||e[xe];return typeof r==\"function\"?r:null}var _=s.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;function d(e){{for(var r=arguments.length,t=new Array(r>1?r-1:0),n=1;n<r;n++)t[n-1]=arguments[n];ke(\"error\",e,t)}}function ke(e,r,t){{var n=_.ReactDebugCurrentFrame,o=n.getStackAddendum();o!==\"\"&&(r+=\"%s\",t=t.concat([o]));var u=t.map(function(i){return String(i)});u.unshift(\"Warning: \"+r),Function.prototype.apply.call(console[e],console,u)}}var De=!1,Fe=!1,Ae=!1,Ie=!1,Ye=!1,Z;Z=Symbol.for(\"react.module.reference\");function $e(e){return!!(typeof e==\"string\"||typeof e==\"function\"||e===E||e===X||Ye||e===h||e===I||e===Y||Ie||e===Se||De||Fe||Ae||typeof e==\"object\"&&e!==null&&(e.$$typeof===$||e.$$typeof===S||e.$$typeof===K||e.$$typeof===H||e.$$typeof===P||e.$$typeof===Z||e.getModuleId!==void 0))}function We(e,r,t){var n=e.displayName;if(n)return n;var o=r.displayName||r.name||\"\";return o!==\"\"?t+\"(\"+o+\")\":t}function Q(e){return e.displayName||\"Context\"}function g(e){if(e==null)return null;if(typeof e.tag==\"number\"&&d(\"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"),typeof e==\"function\")return e.displayName||e.name||null;if(typeof e==\"string\")return e;switch(e){case E:return\"Fragment\";case b:return\"Portal\";case X:return\"Profiler\";case h:return\"StrictMode\";case I:return\"Suspense\";case Y:return\"SuspenseList\"}if(typeof e==\"object\")switch(e.$$typeof){case H:var r=e;return Q(r)+\".Consumer\";case K:var t=e;return Q(t._context)+\".Provider\";case P:return We(e,e.render,\"ForwardRef\");case S:var n=e.displayName||null;return n!==null?n:g(e.type)||\"Memo\";case $:{var o=e,u=o._payload,i=o._init;try{return g(i(u))}catch{return null}}}return null}var y=Object.assign,C=0,ee,re,te,ne,ae,ie,oe;function ue(){}ue.__reactDisabledLog=!0;function Ne(){{if(C===0){ee=console.log,re=console.info,te=console.warn,ne=console.error,ae=console.group,ie=console.groupCollapsed,oe=console.groupEnd;var e={configurable:!0,enumerable:!0,value:ue,writable:!0};Object.defineProperties(console,{info:e,log:e,warn:e,error:e,group:e,groupCollapsed:e,groupEnd:e})}C++}}function Me(){{if(C--,C===0){var e={configurable:!0,enumerable:!0,writable:!0};Object.defineProperties(console,{log:y({},e,{value:ee}),info:y({},e,{value:re}),warn:y({},e,{value:te}),error:y({},e,{value:ne}),group:y({},e,{value:ae}),groupCollapsed:y({},e,{value:ie}),groupEnd:y({},e,{value:oe})})}C<0&&d(\"disabledDepth fell below zero. This is a bug in React. Please file an issue.\")}}var W=_.ReactCurrentDispatcher,N;function x(e,r,t){{if(N===void 0)try{throw Error()}catch(o){var n=o.stack.trim().match(/\\n( *(at )?)/);N=n&&n[1]||\"\"}return`\n`+N+e}}var M=!1,j;{var Ve=typeof WeakMap==\"function\"?WeakMap:Map;j=new Ve}function se(e,r){if(!e||M)return\"\";{var t=j.get(e);if(t!==void 0)return t}var n;M=!0;var o=Error.prepareStackTrace;Error.prepareStackTrace=void 0;var u;u=W.current,W.current=null,Ne();try{if(r){var i=function(){throw Error()};if(Object.defineProperty(i.prototype,\"props\",{set:function(){throw Error()}}),typeof Reflect==\"object\"&&Reflect.construct){try{Reflect.construct(i,[])}catch(m){n=m}Reflect.construct(e,[],i)}else{try{i.call()}catch(m){n=m}e.call(i.prototype)}}else{try{throw Error()}catch(m){n=m}e()}}catch(m){if(m&&n&&typeof m.stack==\"string\"){for(var a=m.stack.split(`\n`),v=n.stack.split(`\n`),f=a.length-1,c=v.length-1;f>=1&&c>=0&&a[f]!==v[c];)c--;for(;f>=1&&c>=0;f--,c--)if(a[f]!==v[c]){if(f!==1||c!==1)do if(f--,c--,c<0||a[f]!==v[c]){var p=`\n`+a[f].replace(\" at new \",\" at \");return e.displayName&&p.includes(\"<anonymous>\")&&(p=p.replace(\"<anonymous>\",e.displayName)),typeof e==\"function\"&&j.set(e,p),p}while(f>=1&&c>=0);break}}}finally{M=!1,W.current=u,Me(),Error.prepareStackTrace=o}var T=e?e.displayName||e.name:\"\",ye=T?x(T):\"\";return typeof e==\"function\"&&j.set(e,ye),ye}function Le(e,r,t){return se(e,!1)}function Ue(e){var r=e.prototype;return!!(r&&r.isReactComponent)}function k(e,r,t){if(e==null)return\"\";if(typeof e==\"function\")return se(e,Ue(e));if(typeof e==\"string\")return x(e);switch(e){case I:return x(\"Suspense\");case Y:return x(\"SuspenseList\")}if(typeof e==\"object\")switch(e.$$typeof){case P:return Le(e.render);case S:return k(e.type,r,t);case $:{var n=e,o=n._payload,u=n._init;try{return k(u(o),r,t)}catch{}}}return\"\"}var D=Object.prototype.hasOwnProperty,le={},fe=_.ReactDebugCurrentFrame;function F(e){if(e){var r=e._owner,t=k(e.type,e._source,r?r.type:null);fe.setExtraStackFrame(t)}else fe.setExtraStackFrame(null)}function Be(e,r,t,n,o){{var u=Function.call.bind(D);for(var i in e)if(u(e,i)){var a=void 0;try{if(typeof e[i]!=\"function\"){var v=Error((n||\"React class\")+\": \"+t+\" type `\"+i+\"` is invalid; it must be a function, usually from the `prop-types` package, but received `\"+typeof e[i]+\"`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.\");throw v.name=\"Invariant Violation\",v}a=e[i](r,i,n,t,null,\"SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED\")}catch(f){a=f}a&&!(a instanceof Error)&&(F(o),d(\"%s: type specification of %s `%s` is invalid; the type checker function must return `null` or an `Error` but returned a %s. You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument).\",n||\"React class\",t,i,typeof a),F(null)),a instanceof Error&&!(a.message in le)&&(le[a.message]=!0,F(o),d(\"Failed %s type: %s\",t,a.message),F(null))}}}var qe=Array.isArray;function V(e){return qe(e)}function ze(e){{var r=typeof Symbol==\"function\"&&Symbol.toStringTag,t=r&&e[Symbol.toStringTag]||e.constructor.name||\"Object\";return t}}function Ge(e){try{return ce(e),!1}catch{return!0}}function ce(e){return\"\"+e}function de(e){if(Ge(e))return d(\"The provided key is an unsupported type %s. This value must be coerced to a string before before using it here.\",ze(e)),ce(e)}var O=_.ReactCurrentOwner,Xe={key:!0,ref:!0,__self:!0,__source:!0},ve,pe,L;L={};function Ke(e){if(D.call(e,\"ref\")){var r=Object.getOwnPropertyDescriptor(e,\"ref\").get;if(r&&r.isReactWarning)return!1}return e.ref!==void 0}function He(e){if(D.call(e,\"key\")){var r=Object.getOwnPropertyDescriptor(e,\"key\").get;if(r&&r.isReactWarning)return!1}return e.key!==void 0}function Je(e,r){if(typeof e.ref==\"string\"&&O.current&&r&&O.current.stateNode!==r){var t=g(O.current.type);L[t]||(d('Component \"%s\" contains the string ref \"%s\". Support for string refs will be removed in a future major release. This case cannot be automatically converted to an arrow function. We ask you to manually fix this case by using useRef() or createRef() instead. Learn more about using refs safely here: https://reactjs.org/link/strict-mode-string-ref',g(O.current.type),e.ref),L[t]=!0)}}function Ze(e,r){{var t=function(){ve||(ve=!0,d(\"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)\",r))};t.isReactWarning=!0,Object.defineProperty(e,\"key\",{get:t,configurable:!0})}}function Qe(e,r){{var t=function(){pe||(pe=!0,d(\"%s: `ref` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)\",r))};t.isReactWarning=!0,Object.defineProperty(e,\"ref\",{get:t,configurable:!0})}}var er=function(e,r,t,n,o,u,i){var a={$$typeof:l,type:e,key:r,ref:t,props:i,_owner:u};return a._store={},Object.defineProperty(a._store,\"validated\",{configurable:!1,enumerable:!1,writable:!0,value:!1}),Object.defineProperty(a,\"_self\",{configurable:!1,enumerable:!1,writable:!1,value:n}),Object.defineProperty(a,\"_source\",{configurable:!1,enumerable:!1,writable:!1,value:o}),Object.freeze&&(Object.freeze(a.props),Object.freeze(a)),a};function rr(e,r,t,n,o){{var u,i={},a=null,v=null;t!==void 0&&(de(t),a=\"\"+t),He(r)&&(de(r.key),a=\"\"+r.key),Ke(r)&&(v=r.ref,Je(r,o));for(u in r)D.call(r,u)&&!Xe.hasOwnProperty(u)&&(i[u]=r[u]);if(e&&e.defaultProps){var f=e.defaultProps;for(u in f)i[u]===void 0&&(i[u]=f[u])}if(a||v){var c=typeof e==\"function\"?e.displayName||e.name||\"Unknown\":e;a&&Ze(i,c),v&&Qe(i,c)}return er(e,a,v,o,n,O.current,i)}}var U=_.ReactCurrentOwner,be=_.ReactDebugCurrentFrame;function R(e){if(e){var r=e._owner,t=k(e.type,e._source,r?r.type:null);be.setExtraStackFrame(t)}else be.setExtraStackFrame(null)}var B;B=!1;function q(e){return typeof e==\"object\"&&e!==null&&e.$$typeof===l}function ge(){{if(U.current){var e=g(U.current.type);if(e)return`\n\nCheck the render method of \\``+e+\"`.\"}return\"\"}}function tr(e){{if(e!==void 0){var r=e.fileName.replace(/^.*[\\\\\\/]/,\"\"),t=e.lineNumber;return`\n\nCheck your code at `+r+\":\"+t+\".\"}return\"\"}}var me={};function nr(e){{var r=ge();if(!r){var t=typeof e==\"string\"?e:e.displayName||e.name;t&&(r=`\n\nCheck the top-level render call using <`+t+\">.\")}return r}}function he(e,r){{if(!e._store||e._store.validated||e.key!=null)return;e._store.validated=!0;var t=nr(r);if(me[t])return;me[t]=!0;var n=\"\";e&&e._owner&&e._owner!==U.current&&(n=\" It was passed a child from \"+g(e._owner.type)+\".\"),R(e),d('Each child in a list should have a unique \"key\" prop.%s%s See https://reactjs.org/link/warning-keys for more information.',t,n),R(null)}}function Ee(e,r){{if(typeof e!=\"object\")return;if(V(e))for(var t=0;t<e.length;t++){var n=e[t];q(n)&&he(n,r)}else if(q(e))e._store&&(e._store.validated=!0);else if(e){var o=je(e);if(typeof o==\"function\"&&o!==e.entries)for(var u=o.call(e),i;!(i=u.next()).done;)q(i.value)&&he(i.value,r)}}}function ar(e){{var r=e.type;if(r==null||typeof r==\"string\")return;var t;if(typeof r==\"function\")t=r.propTypes;else if(typeof r==\"object\"&&(r.$$typeof===P||r.$$typeof===S))t=r.propTypes;else return;if(t){var n=g(r);Be(t,e.props,\"prop\",n,e)}else if(r.PropTypes!==void 0&&!B){B=!0;var o=g(r);d(\"Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?\",o||\"Unknown\")}typeof r.getDefaultProps==\"function\"&&!r.getDefaultProps.isReactClassApproved&&d(\"getDefaultProps is only used on classic React.createClass definitions. Use a static property named `defaultProps` instead.\")}}function ir(e){{for(var r=Object.keys(e.props),t=0;t<r.length;t++){var n=r[t];if(n!==\"children\"&&n!==\"key\"){R(e),d(\"Invalid prop `%s` supplied to `React.Fragment`. React.Fragment can only have `key` and `children` props.\",n),R(null);break}}e.ref!==null&&(R(e),d(\"Invalid attribute `ref` supplied to `React.Fragment`.\"),R(null))}}function or(e,r,t,n,o,u){{var i=$e(e);if(!i){var a=\"\";(e===void 0||typeof e==\"object\"&&e!==null&&Object.keys(e).length===0)&&(a+=\" You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.\");var v=tr(o);v?a+=v:a+=ge();var f;e===null?f=\"null\":V(e)?f=\"array\":e!==void 0&&e.$$typeof===l?(f=\"<\"+(g(e.type)||\"Unknown\")+\" />\",a=\" Did you accidentally export a JSX literal instead of a component?\"):f=typeof e,d(\"React.jsx: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: %s.%s\",f,a)}var c=rr(e,r,t,o,u);if(c==null)return c;if(i){var p=r.children;if(p!==void 0)if(n)if(V(p)){for(var T=0;T<p.length;T++)Ee(p[T],e);Object.freeze&&Object.freeze(p)}else d(\"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\");else Ee(p,e)}return e===E?ir(c):ar(c),c}}var ur=or;G.Fragment=E,G.jsxDEV=ur})()});var Oe=z((Tr,Ce)=>{\"use strict\";Ce.exports=we()});var Er={};vr(Er,{default:()=>hr,frontmatter:()=>gr});var w=pr(Oe()),gr={title:\"6 raisons pour savoir si votre site web \\xE0 besoin d\\u2019une refonte\",excerpt:\"Si vous utilisez le m\\xEAme site web depuis un certain temps ou s\\u2019il a \\xE9t\\xE9 cr\\xE9\\xE9 \\xE0 l\\u2019\\xE9poque o\\xF9 le responsive design n\\u2019existait pas encore, il y a de fortes chances que votre site ait besoin d\\u2019\\xEAtre retravaill\\xE9.\",date:\"2025-05-17\",updated:\"2025-05-20\",category:\"D\\xE9veloppement web\",cover:\"/images/blog/6-raisons-refonte-site-web/main-refonte-site-web.webp\",published:!1};function Pe(s){return(0,w.jsxDEV)(w.Fragment,{},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-9dbd840e-5430-4f80-800d-9d7a7a612a94.mdx\",lineNumber:1,columnNumber:1},this)}function mr(s={}){let{wrapper:l}=s.components||{};return l?(0,w.jsxDEV)(l,Object.assign({},s,{children:(0,w.jsxDEV)(Pe,s,void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-9dbd840e-5430-4f80-800d-9d7a7a612a94.mdx\"},this)}),void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-9dbd840e-5430-4f80-800d-9d7a7a612a94.mdx\"},this):Pe(s)}var hr=mr;return br(Er);})();\n/*! Bundled license information:\n\nreact/cjs/react-jsx-dev-runtime.development.js:\n  (**\n   * @license React\n   * react-jsx-dev-runtime.development.js\n   *\n   * Copyright (c) Facebook, Inc. and its affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *)\n*/\n;return Component;"}, "_id": "blog/fr/6-raisons-refonte-site-web.mdx", "_raw": {"sourceFilePath": "blog/fr/6-raisons-refonte-site-web.mdx", "sourceFileName": "6-raisons-refonte-site-web.mdx", "sourceFileDir": "blog/fr", "contentType": "mdx", "flattenedPath": "blog/fr/6-raisons-refonte-site-web"}, "type": "Post", "locale": "fr", "slug": "6-raisons-refonte-site-web"}, {"title": "Nouveau logo CF Montréal : un symbole de renouveau pour 2023", "excerpt": "En 2023, le CF Montréal annonce le remplacement de son logo, en forme de flocon de neige, par un nouveau logo bleu et noir, au centre duquel on retrouve une fleur de lys et une référence à l’année de fondation du club, 1993.", "date": "2023-05-04T00:00:00.000Z", "updated": "2023-05-04T00:00:00.000Z", "category": "Image de marque", "cover": "/images/blog/logo-cf-montreal-2023/main-logo-cf-montreal-2023.png", "published": true, "body": {"raw": "\nLes partisans du CF Montréal l'attendaient. Le club de soccer montréalais, qui évolue en Major League Soccer (MLS), a dévoilé en 2023 un nouveau logo marquant un tournant majeur dans son identité visuelle. Après la controverse suscitée par le rebranding de 2021, ce changement s'accompagne du retour de symboles emblématiques comme la fleur de lys.\n\nPourquoi ce changement ? Que symbolise ce nouveau logo ? Et comment a-t-il été reçu par les fans ? On fait le point.\n\n## Retour aux sources : un logo inspiré de l'histoire du club\n\nLe **nouveau logo du CF Montréal** rend hommage à l'héritage du club en réintroduisant des éléments familiers aux supporteurs de la première heure. Conçu sous la forme d’un écusson traditionnel, il incorpore des symboles historiques forts de l’Impact de Montréal (le nom d’origine du club).\n\n<Image\n    src='/images/blog/logo-cf-montreal-2023/explication-logo-cf-montreal.webp'\n    alt=\"Explication du logo CF Montréal\"\n/>\n\n- Le bouclier qui rappelle les anciens logos du club\n- La fleur de lys, symbole du Québec et figurant autrefois sur l'emblème de l'Impact\n- Le bleu « Impact » redevient la couleur prédominante du logo, accompagné de noir\n- Les bandes noires et bleues qui rappellent les premiers maillots du club et les débuts professionnels dans les années 1990\n- « 1993 » l'année de la saison inaugurale du club\n\nEn plus du nom officiel **CF Montréal** bien en vue, l'ensemble de ces éléments confère au blason une identité riche de sens, ancrée dans l'histoire et la culture du club tout en restant tournée vers l'avenir.\n\n\n\n## Pourquoi le CF Montréal a changé de logo en 2023 ?\nLe changement de logo du CF Montréal en 2023 s'explique avant tout par la volonté de **corriger le tir** après le rebranding controversé de 2021.\n\nPour rappel, le 14 janvier 2021, la direction de l'époque avait annoncé que l'Impact de Montréal changeait de nom et d'identité visuelle pour devenir « Club de Foot Montréal ». Ce rebranding radical – nouveau nom et logo en forme de flocon de neige – avait suscité une vive opposition chez une partie des supporteurs et de la communauté du soccer québécois.\n\nBeaucoup de fans historiques ne se reconnaissaient pas dans ce nouvel emblème, jugé trop éloigné de l'histoire du club et de l'identité montréalaise.\n\nFace à cette réaction négative, les dirigeants ont décidé d’écouter la voix des partisans. L’arrivée d’un nouveau président en 2022, Gabriel Gervais (lui-même ancien joueur du club), a marqué un tournant. Gervais a reconnu publiquement qu’il était **difficile de s’identifier à l’ancien logo** : « La fleur de lys est où ? La couleur bleue est où ? Comment sommes-nous représentés ? » s'est-il interrogé, soulignant l'absence des symboles traditionnels. Cet aveu a mis en lumière **l'erreur de branding** commise en 2021. Ainsi, moins de deux ans après la précédente refonte, le CF Montréal a choisi de changer à nouveau de logo afin de **réaligner son identité visuelle sur son ADN d'origine**.\n\n<Tweet id=\"1521993350240473091\" />\n\nConcrètement, ce retour en arrière était une réponse directe aux attentes des supporteurs. « We heard them loud and clear » – « nous les avons entendus haut et fort », a déclaré le propriétaire Joey Saputo en évoquant les fans.\n\nLe club a donc assumé de **faire marche arrière** sur certains choix esthétiques pour **réparer le lien** avec sa base de partisans. Ce changement de logo en 2023 a pour but de rassembler la communauté après une période de division et de controverse. C’est une démarche humble de la part de l’organisation, qui admet que l’identité précédente n’a pas fait l’unanimité et qui tente de « recoller les pots cassés » avec ses fans les plus fidèles.\n\n<Image\n    src='/images/blog/logo-cf-montreal-2023/comparaison-logo-cf-montreal.webp'\n    alt=\"Comparaisons de l'évolution du logo CF Montréal\"\n/>\n\n## Des réactions globalement positives\n\nLe dévoilement du nouveau logo a reçu un bon accueil. Sur les réseaux sociaux, dans les médias spécialisés et au sein des groupes de supporters, le retour du bleu et de la fleur de lys a été salué.\n\nCertains fans espéraient néanmoins un retour complet à l’appellation « Impact ». Ce ne sera pas le cas. Gabriel Gervais l’a confirmé : **le nom CF Montréal reste**.\n\nToutefois, le club se montre ouvert à honorer l’héritage de l’Impact autrement : nom symbolique dans le stade, références visuelles, clins d’œil dans les communications.\n\nDans l’ensemble, cette nouvelle identité est perçue comme un compromis équilibré entre l’ancien et le nouveau. Elle redonne au club une image plus alignée avec sa communauté.\n\n## Conclusion\n\nCe changement d’identité s’inscrit possiblement dans une tendance plus large de la MLS où plusieurs clubs ont récemment procédé à des refontes de logo pour mieux refléter leur identité (on peut penser aux rebrandings du Columbus Crew, du Chicago Fire, etc.). Mais dans le cas de Montréal, la démarche a surtout été guidée par un besoin local : reconnecter le club à sa base et à son patrimoine.", "code": "var Component=(()=>{var sr=Object.create;var D=Object.defineProperty;var cr=Object.getOwnPropertyDescriptor;var dr=Object.getOwnPropertyNames;var fr=Object.getPrototypeOf,mr=Object.prototype.hasOwnProperty;var B=(s,t)=>()=>(t||s((t={exports:{}}).exports,t),t.exports),br=(s,t)=>{for(var m in t)D(s,m,{get:t[m],enumerable:!0})},xe=(s,t,m,h)=>{if(t&&typeof t==\"object\"||typeof t==\"function\")for(let y of dr(t))!mr.call(s,y)&&y!==m&&D(s,y,{get:()=>t[y],enumerable:!(h=cr(t,y))||h.enumerable});return s};var pr=(s,t,m)=>(m=s!=null?sr(fr(s)):{},xe(t||!s||!s.__esModule?D(m,\"default\",{value:s,enumerable:!0}):m,s)),gr=s=>xe(D({},\"__esModule\",{value:!0}),s);var Ee=B((Nr,Ne)=>{Ne.exports=React});var we=B(z=>{\"use strict\";(function(){\"use strict\";var s=Ee(),t=Symbol.for(\"react.element\"),m=Symbol.for(\"react.portal\"),h=Symbol.for(\"react.fragment\"),y=Symbol.for(\"react.strict_mode\"),X=Symbol.for(\"react.profiler\"),K=Symbol.for(\"react.provider\"),H=Symbol.for(\"react.context\"),k=Symbol.for(\"react.forward_ref\"),M=Symbol.for(\"react.suspense\"),A=Symbol.for(\"react.suspense_list\"),T=Symbol.for(\"react.memo\"),I=Symbol.for(\"react.lazy\"),Oe=Symbol.for(\"react.offscreen\"),J=Symbol.iterator,Pe=\"@@iterator\";function Se(e){if(e===null||typeof e!=\"object\")return null;var r=J&&e[J]||e[Pe];return typeof r==\"function\"?r:null}var N=s.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;function b(e){{for(var r=arguments.length,n=new Array(r>1?r-1:0),a=1;a<r;a++)n[a-1]=arguments[a];je(\"error\",e,n)}}function je(e,r,n){{var a=N.ReactDebugCurrentFrame,u=a.getStackAddendum();u!==\"\"&&(r+=\"%s\",n=n.concat([u]));var c=n.map(function(l){return String(l)});c.unshift(\"Warning: \"+r),Function.prototype.apply.call(console[e],console,c)}}var Fe=!1,De=!1,Me=!1,Ae=!1,Ie=!1,Q;Q=Symbol.for(\"react.module.reference\");function Le(e){return!!(typeof e==\"string\"||typeof e==\"function\"||e===h||e===X||Ie||e===y||e===M||e===A||Ae||e===Oe||Fe||De||Me||typeof e==\"object\"&&e!==null&&(e.$$typeof===I||e.$$typeof===T||e.$$typeof===K||e.$$typeof===H||e.$$typeof===k||e.$$typeof===Q||e.getModuleId!==void 0))}function qe(e,r,n){var a=e.displayName;if(a)return a;var u=r.displayName||r.name||\"\";return u!==\"\"?n+\"(\"+u+\")\":n}function Z(e){return e.displayName||\"Context\"}function _(e){if(e==null)return null;if(typeof e.tag==\"number\"&&b(\"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"),typeof e==\"function\")return e.displayName||e.name||null;if(typeof e==\"string\")return e;switch(e){case h:return\"Fragment\";case m:return\"Portal\";case X:return\"Profiler\";case y:return\"StrictMode\";case M:return\"Suspense\";case A:return\"SuspenseList\"}if(typeof e==\"object\")switch(e.$$typeof){case H:var r=e;return Z(r)+\".Consumer\";case K:var n=e;return Z(n._context)+\".Provider\";case k:return qe(e,e.render,\"ForwardRef\");case T:var a=e.displayName||null;return a!==null?a:_(e.type)||\"Memo\";case I:{var u=e,c=u._payload,l=u._init;try{return _(l(c))}catch{return null}}}return null}var x=Object.assign,C=0,ee,re,ne,te,ae,oe,ie;function le(){}le.__reactDisabledLog=!0;function We(){{if(C===0){ee=console.log,re=console.info,ne=console.warn,te=console.error,ae=console.group,oe=console.groupCollapsed,ie=console.groupEnd;var e={configurable:!0,enumerable:!0,value:le,writable:!0};Object.defineProperties(console,{info:e,log:e,warn:e,error:e,group:e,groupCollapsed:e,groupEnd:e})}C++}}function Ye(){{if(C--,C===0){var e={configurable:!0,enumerable:!0,writable:!0};Object.defineProperties(console,{log:x({},e,{value:ee}),info:x({},e,{value:re}),warn:x({},e,{value:ne}),error:x({},e,{value:te}),group:x({},e,{value:ae}),groupCollapsed:x({},e,{value:oe}),groupEnd:x({},e,{value:ie})})}C<0&&b(\"disabledDepth fell below zero. This is a bug in React. Please file an issue.\")}}var L=N.ReactCurrentDispatcher,q;function O(e,r,n){{if(q===void 0)try{throw Error()}catch(u){var a=u.stack.trim().match(/\\n( *(at )?)/);q=a&&a[1]||\"\"}return`\n`+q+e}}var W=!1,P;{var $e=typeof WeakMap==\"function\"?WeakMap:Map;P=new $e}function ue(e,r){if(!e||W)return\"\";{var n=P.get(e);if(n!==void 0)return n}var a;W=!0;var u=Error.prepareStackTrace;Error.prepareStackTrace=void 0;var c;c=L.current,L.current=null,We();try{if(r){var l=function(){throw Error()};if(Object.defineProperty(l.prototype,\"props\",{set:function(){throw Error()}}),typeof Reflect==\"object\"&&Reflect.construct){try{Reflect.construct(l,[])}catch(v){a=v}Reflect.construct(e,[],l)}else{try{l.call()}catch(v){a=v}e.call(l.prototype)}}else{try{throw Error()}catch(v){a=v}e()}}catch(v){if(v&&a&&typeof v.stack==\"string\"){for(var i=v.stack.split(`\n`),p=a.stack.split(`\n`),d=i.length-1,f=p.length-1;d>=1&&f>=0&&i[d]!==p[f];)f--;for(;d>=1&&f>=0;d--,f--)if(i[d]!==p[f]){if(d!==1||f!==1)do if(d--,f--,f<0||i[d]!==p[f]){var g=`\n`+i[d].replace(\" at new \",\" at \");return e.displayName&&g.includes(\"<anonymous>\")&&(g=g.replace(\"<anonymous>\",e.displayName)),typeof e==\"function\"&&P.set(e,g),g}while(d>=1&&f>=0);break}}}finally{W=!1,L.current=c,Ye(),Error.prepareStackTrace=u}var w=e?e.displayName||e.name:\"\",ye=w?O(w):\"\";return typeof e==\"function\"&&P.set(e,ye),ye}function Ve(e,r,n){return ue(e,!1)}function Ue(e){var r=e.prototype;return!!(r&&r.isReactComponent)}function S(e,r,n){if(e==null)return\"\";if(typeof e==\"function\")return ue(e,Ue(e));if(typeof e==\"string\")return O(e);switch(e){case M:return O(\"Suspense\");case A:return O(\"SuspenseList\")}if(typeof e==\"object\")switch(e.$$typeof){case k:return Ve(e.render);case T:return S(e.type,r,n);case I:{var a=e,u=a._payload,c=a._init;try{return S(c(u),r,n)}catch{}}}return\"\"}var j=Object.prototype.hasOwnProperty,se={},ce=N.ReactDebugCurrentFrame;function F(e){if(e){var r=e._owner,n=S(e.type,e._source,r?r.type:null);ce.setExtraStackFrame(n)}else ce.setExtraStackFrame(null)}function Ge(e,r,n,a,u){{var c=Function.call.bind(j);for(var l in e)if(c(e,l)){var i=void 0;try{if(typeof e[l]!=\"function\"){var p=Error((a||\"React class\")+\": \"+n+\" type `\"+l+\"` is invalid; it must be a function, usually from the `prop-types` package, but received `\"+typeof e[l]+\"`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.\");throw p.name=\"Invariant Violation\",p}i=e[l](r,l,a,n,null,\"SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED\")}catch(d){i=d}i&&!(i instanceof Error)&&(F(u),b(\"%s: type specification of %s `%s` is invalid; the type checker function must return `null` or an `Error` but returned a %s. You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument).\",a||\"React class\",n,l,typeof i),F(null)),i instanceof Error&&!(i.message in se)&&(se[i.message]=!0,F(u),b(\"Failed %s type: %s\",n,i.message),F(null))}}}var Be=Array.isArray;function Y(e){return Be(e)}function ze(e){{var r=typeof Symbol==\"function\"&&Symbol.toStringTag,n=r&&e[Symbol.toStringTag]||e.constructor.name||\"Object\";return n}}function Xe(e){try{return de(e),!1}catch{return!0}}function de(e){return\"\"+e}function fe(e){if(Xe(e))return b(\"The provided key is an unsupported type %s. This value must be coerced to a string before before using it here.\",ze(e)),de(e)}var R=N.ReactCurrentOwner,Ke={key:!0,ref:!0,__self:!0,__source:!0},me,be,$;$={};function He(e){if(j.call(e,\"ref\")){var r=Object.getOwnPropertyDescriptor(e,\"ref\").get;if(r&&r.isReactWarning)return!1}return e.ref!==void 0}function Je(e){if(j.call(e,\"key\")){var r=Object.getOwnPropertyDescriptor(e,\"key\").get;if(r&&r.isReactWarning)return!1}return e.key!==void 0}function Qe(e,r){if(typeof e.ref==\"string\"&&R.current&&r&&R.current.stateNode!==r){var n=_(R.current.type);$[n]||(b('Component \"%s\" contains the string ref \"%s\". Support for string refs will be removed in a future major release. This case cannot be automatically converted to an arrow function. We ask you to manually fix this case by using useRef() or createRef() instead. Learn more about using refs safely here: https://reactjs.org/link/strict-mode-string-ref',_(R.current.type),e.ref),$[n]=!0)}}function Ze(e,r){{var n=function(){me||(me=!0,b(\"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)\",r))};n.isReactWarning=!0,Object.defineProperty(e,\"key\",{get:n,configurable:!0})}}function er(e,r){{var n=function(){be||(be=!0,b(\"%s: `ref` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)\",r))};n.isReactWarning=!0,Object.defineProperty(e,\"ref\",{get:n,configurable:!0})}}var rr=function(e,r,n,a,u,c,l){var i={$$typeof:t,type:e,key:r,ref:n,props:l,_owner:c};return i._store={},Object.defineProperty(i._store,\"validated\",{configurable:!1,enumerable:!1,writable:!0,value:!1}),Object.defineProperty(i,\"_self\",{configurable:!1,enumerable:!1,writable:!1,value:a}),Object.defineProperty(i,\"_source\",{configurable:!1,enumerable:!1,writable:!1,value:u}),Object.freeze&&(Object.freeze(i.props),Object.freeze(i)),i};function nr(e,r,n,a,u){{var c,l={},i=null,p=null;n!==void 0&&(fe(n),i=\"\"+n),Je(r)&&(fe(r.key),i=\"\"+r.key),He(r)&&(p=r.ref,Qe(r,u));for(c in r)j.call(r,c)&&!Ke.hasOwnProperty(c)&&(l[c]=r[c]);if(e&&e.defaultProps){var d=e.defaultProps;for(c in d)l[c]===void 0&&(l[c]=d[c])}if(i||p){var f=typeof e==\"function\"?e.displayName||e.name||\"Unknown\":e;i&&Ze(l,f),p&&er(l,f)}return rr(e,i,p,u,a,R.current,l)}}var V=N.ReactCurrentOwner,pe=N.ReactDebugCurrentFrame;function E(e){if(e){var r=e._owner,n=S(e.type,e._source,r?r.type:null);pe.setExtraStackFrame(n)}else pe.setExtraStackFrame(null)}var U;U=!1;function G(e){return typeof e==\"object\"&&e!==null&&e.$$typeof===t}function ge(){{if(V.current){var e=_(V.current.type);if(e)return`\n\nCheck the render method of \\``+e+\"`.\"}return\"\"}}function tr(e){{if(e!==void 0){var r=e.fileName.replace(/^.*[\\\\\\/]/,\"\"),n=e.lineNumber;return`\n\nCheck your code at `+r+\":\"+n+\".\"}return\"\"}}var he={};function ar(e){{var r=ge();if(!r){var n=typeof e==\"string\"?e:e.displayName||e.name;n&&(r=`\n\nCheck the top-level render call using <`+n+\">.\")}return r}}function _e(e,r){{if(!e._store||e._store.validated||e.key!=null)return;e._store.validated=!0;var n=ar(r);if(he[n])return;he[n]=!0;var a=\"\";e&&e._owner&&e._owner!==V.current&&(a=\" It was passed a child from \"+_(e._owner.type)+\".\"),E(e),b('Each child in a list should have a unique \"key\" prop.%s%s See https://reactjs.org/link/warning-keys for more information.',n,a),E(null)}}function ve(e,r){{if(typeof e!=\"object\")return;if(Y(e))for(var n=0;n<e.length;n++){var a=e[n];G(a)&&_e(a,r)}else if(G(e))e._store&&(e._store.validated=!0);else if(e){var u=Se(e);if(typeof u==\"function\"&&u!==e.entries)for(var c=u.call(e),l;!(l=c.next()).done;)G(l.value)&&_e(l.value,r)}}}function or(e){{var r=e.type;if(r==null||typeof r==\"string\")return;var n;if(typeof r==\"function\")n=r.propTypes;else if(typeof r==\"object\"&&(r.$$typeof===k||r.$$typeof===T))n=r.propTypes;else return;if(n){var a=_(r);Ge(n,e.props,\"prop\",a,e)}else if(r.PropTypes!==void 0&&!U){U=!0;var u=_(r);b(\"Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?\",u||\"Unknown\")}typeof r.getDefaultProps==\"function\"&&!r.getDefaultProps.isReactClassApproved&&b(\"getDefaultProps is only used on classic React.createClass definitions. Use a static property named `defaultProps` instead.\")}}function ir(e){{for(var r=Object.keys(e.props),n=0;n<r.length;n++){var a=r[n];if(a!==\"children\"&&a!==\"key\"){E(e),b(\"Invalid prop `%s` supplied to `React.Fragment`. React.Fragment can only have `key` and `children` props.\",a),E(null);break}}e.ref!==null&&(E(e),b(\"Invalid attribute `ref` supplied to `React.Fragment`.\"),E(null))}}function lr(e,r,n,a,u,c){{var l=Le(e);if(!l){var i=\"\";(e===void 0||typeof e==\"object\"&&e!==null&&Object.keys(e).length===0)&&(i+=\" You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.\");var p=tr(u);p?i+=p:i+=ge();var d;e===null?d=\"null\":Y(e)?d=\"array\":e!==void 0&&e.$$typeof===t?(d=\"<\"+(_(e.type)||\"Unknown\")+\" />\",i=\" Did you accidentally export a JSX literal instead of a component?\"):d=typeof e,b(\"React.jsx: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: %s.%s\",d,i)}var f=nr(e,r,n,u,c);if(f==null)return f;if(l){var g=r.children;if(g!==void 0)if(a)if(Y(g)){for(var w=0;w<g.length;w++)ve(g[w],e);Object.freeze&&Object.freeze(g)}else b(\"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\");else ve(g,e)}return e===h?ir(f):or(f),f}}var ur=lr;z.Fragment=h,z.jsxDEV=ur})()});var Re=B((wr,Ce)=>{\"use strict\";Ce.exports=we()});var yr={};br(yr,{default:()=>vr,frontmatter:()=>hr});var o=pr(Re()),hr={title:\"Nouveau logo CF Montr\\xE9al\\xA0: un symbole de renouveau pour 2023\",excerpt:\"En 2023, le CF Montr\\xE9al annonce le remplacement de son logo, en forme de flocon de neige, par un nouveau logo bleu et noir, au centre duquel on retrouve une fleur de lys et une r\\xE9f\\xE9rence \\xE0 l\\u2019ann\\xE9e de fondation du club, 1993.\",date:\"2023-05-04\",updated:\"2023-05-04\",category:\"Image de marque\",cover:\"/images/blog/logo-cf-montreal-2023/main-logo-cf-montreal-2023.png\"};function ke(s){let t=Object.assign({p:\"p\",h2:\"h2\",strong:\"strong\",ul:\"ul\",li:\"li\"},s.components),{Image:m,Tweet:h}=t;return m||Te(\"Image\",!0,\"18:1-21:3\"),h||Te(\"Tweet\",!0,\"42:1-42:35\"),(0,o.jsxDEV)(o.Fragment,{children:[(0,o.jsxDEV)(t.p,{children:\"Les partisans du CF Montr\\xE9al l'attendaient. Le club de soccer montr\\xE9alais, qui \\xE9volue en Major League Soccer (MLS), a d\\xE9voil\\xE9 en 2023 un nouveau logo marquant un tournant majeur dans son identit\\xE9 visuelle. Apr\\xE8s la controverse suscit\\xE9e par le rebranding de 2021, ce changement s'accompagne du retour de symboles embl\\xE9matiques comme la fleur de lys.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-703d5ba7-6f0c-46a1-a369-4ca0452571b7.mdx\",lineNumber:10,columnNumber:1},this),`\n`,(0,o.jsxDEV)(t.p,{children:\"Pourquoi ce changement\\xA0? Que symbolise ce nouveau logo\\xA0? Et comment a-t-il \\xE9t\\xE9 re\\xE7u par les fans\\xA0? On fait le point.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-703d5ba7-6f0c-46a1-a369-4ca0452571b7.mdx\",lineNumber:12,columnNumber:1},this),`\n`,(0,o.jsxDEV)(t.h2,{children:\"Retour aux sources\\xA0: un logo inspir\\xE9 de l'histoire du club\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-703d5ba7-6f0c-46a1-a369-4ca0452571b7.mdx\",lineNumber:14,columnNumber:1},this),`\n`,(0,o.jsxDEV)(t.p,{children:[\"Le \",(0,o.jsxDEV)(t.strong,{children:\"nouveau logo du CF Montr\\xE9al\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-703d5ba7-6f0c-46a1-a369-4ca0452571b7.mdx\",lineNumber:16,columnNumber:4},this),\" rend hommage \\xE0 l'h\\xE9ritage du club en r\\xE9introduisant des \\xE9l\\xE9ments familiers aux supporteurs de la premi\\xE8re heure. Con\\xE7u sous la forme d\\u2019un \\xE9cusson traditionnel, il incorpore des symboles historiques forts de l\\u2019Impact de Montr\\xE9al (le nom d\\u2019origine du club).\"]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-703d5ba7-6f0c-46a1-a369-4ca0452571b7.mdx\",lineNumber:16,columnNumber:1},this),`\n`,(0,o.jsxDEV)(m,{src:\"/images/blog/logo-cf-montreal-2023/explication-logo-cf-montreal.webp\",alt:\"Explication du logo CF Montr\\xE9al\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-703d5ba7-6f0c-46a1-a369-4ca0452571b7.mdx\",lineNumber:18,columnNumber:1},this),`\n`,(0,o.jsxDEV)(t.ul,{children:[`\n`,(0,o.jsxDEV)(t.li,{children:\"Le bouclier qui rappelle les anciens logos du club\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-703d5ba7-6f0c-46a1-a369-4ca0452571b7.mdx\",lineNumber:23,columnNumber:1},this),`\n`,(0,o.jsxDEV)(t.li,{children:\"La fleur de lys, symbole du Qu\\xE9bec et figurant autrefois sur l'embl\\xE8me de l'Impact\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-703d5ba7-6f0c-46a1-a369-4ca0452571b7.mdx\",lineNumber:24,columnNumber:1},this),`\n`,(0,o.jsxDEV)(t.li,{children:\"Le bleu \\xAB\\xA0Impact\\xA0\\xBB redevient la couleur pr\\xE9dominante du logo, accompagn\\xE9 de noir\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-703d5ba7-6f0c-46a1-a369-4ca0452571b7.mdx\",lineNumber:25,columnNumber:1},this),`\n`,(0,o.jsxDEV)(t.li,{children:\"Les bandes noires et bleues qui rappellent les premiers maillots du club et les d\\xE9buts professionnels dans les ann\\xE9es 1990\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-703d5ba7-6f0c-46a1-a369-4ca0452571b7.mdx\",lineNumber:26,columnNumber:1},this),`\n`,(0,o.jsxDEV)(t.li,{children:\"\\xAB\\xA01993\\xA0\\xBB l'ann\\xE9e de la saison inaugurale du club\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-703d5ba7-6f0c-46a1-a369-4ca0452571b7.mdx\",lineNumber:27,columnNumber:1},this),`\n`]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-703d5ba7-6f0c-46a1-a369-4ca0452571b7.mdx\",lineNumber:23,columnNumber:1},this),`\n`,(0,o.jsxDEV)(t.p,{children:[\"En plus du nom officiel \",(0,o.jsxDEV)(t.strong,{children:\"CF Montr\\xE9al\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-703d5ba7-6f0c-46a1-a369-4ca0452571b7.mdx\",lineNumber:29,columnNumber:25},this),\" bien en vue, l'ensemble de ces \\xE9l\\xE9ments conf\\xE8re au blason une identit\\xE9 riche de sens, ancr\\xE9e dans l'histoire et la culture du club tout en restant tourn\\xE9e vers l'avenir.\"]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-703d5ba7-6f0c-46a1-a369-4ca0452571b7.mdx\",lineNumber:29,columnNumber:1},this),`\n`,(0,o.jsxDEV)(t.h2,{children:\"Pourquoi le CF Montr\\xE9al a chang\\xE9 de logo en 2023\\xA0?\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-703d5ba7-6f0c-46a1-a369-4ca0452571b7.mdx\",lineNumber:33,columnNumber:1},this),`\n`,(0,o.jsxDEV)(t.p,{children:[\"Le changement de logo du CF Montr\\xE9al en 2023 s'explique avant tout par la volont\\xE9 de \",(0,o.jsxDEV)(t.strong,{children:\"corriger le tir\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-703d5ba7-6f0c-46a1-a369-4ca0452571b7.mdx\",lineNumber:34,columnNumber:86},this),\" apr\\xE8s le rebranding controvers\\xE9 de 2021.\"]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-703d5ba7-6f0c-46a1-a369-4ca0452571b7.mdx\",lineNumber:34,columnNumber:1},this),`\n`,(0,o.jsxDEV)(t.p,{children:\"Pour rappel, le 14 janvier 2021, la direction de l'\\xE9poque avait annonc\\xE9 que l'Impact de Montr\\xE9al changeait de nom et d'identit\\xE9 visuelle pour devenir \\xAB\\xA0Club de Foot Montr\\xE9al\\xA0\\xBB. Ce rebranding radical \\u2013 nouveau nom et logo en forme de flocon de neige \\u2013 avait suscit\\xE9 une vive opposition chez une partie des supporteurs et de la communaut\\xE9 du soccer qu\\xE9b\\xE9cois.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-703d5ba7-6f0c-46a1-a369-4ca0452571b7.mdx\",lineNumber:36,columnNumber:1},this),`\n`,(0,o.jsxDEV)(t.p,{children:\"Beaucoup de fans historiques ne se reconnaissaient pas dans ce nouvel embl\\xE8me, jug\\xE9 trop \\xE9loign\\xE9 de l'histoire du club et de l'identit\\xE9 montr\\xE9alaise.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-703d5ba7-6f0c-46a1-a369-4ca0452571b7.mdx\",lineNumber:38,columnNumber:1},this),`\n`,(0,o.jsxDEV)(t.p,{children:[\"Face \\xE0 cette r\\xE9action n\\xE9gative, les dirigeants ont d\\xE9cid\\xE9 d\\u2019\\xE9couter la voix des partisans. L\\u2019arriv\\xE9e d\\u2019un nouveau pr\\xE9sident en 2022, Gabriel Gervais (lui-m\\xEAme ancien joueur du club), a marqu\\xE9 un tournant. Gervais a reconnu publiquement qu\\u2019il \\xE9tait \",(0,o.jsxDEV)(t.strong,{children:\"difficile de s\\u2019identifier \\xE0 l\\u2019ancien logo\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-703d5ba7-6f0c-46a1-a369-4ca0452571b7.mdx\",lineNumber:40,columnNumber:249},this),\"\\xA0: \\xAB\\xA0La fleur de lys est o\\xF9\\u202F? La couleur bleue est o\\xF9\\u202F? Comment sommes-nous repr\\xE9sent\\xE9s\\u202F?\\xA0\\xBB s'est-il interrog\\xE9, soulignant l'absence des symboles traditionnels. Cet aveu a mis en lumi\\xE8re \",(0,o.jsxDEV)(t.strong,{children:\"l'erreur de branding\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-703d5ba7-6f0c-46a1-a369-4ca0452571b7.mdx\",lineNumber:40,columnNumber:481},this),\" commise en 2021. Ainsi, moins de deux ans apr\\xE8s la pr\\xE9c\\xE9dente refonte, le CF Montr\\xE9al a choisi de changer \\xE0 nouveau de logo afin de \",(0,o.jsxDEV)(t.strong,{children:\"r\\xE9aligner son identit\\xE9 visuelle sur son ADN d'origine\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-703d5ba7-6f0c-46a1-a369-4ca0452571b7.mdx\",lineNumber:40,columnNumber:638},this),\".\"]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-703d5ba7-6f0c-46a1-a369-4ca0452571b7.mdx\",lineNumber:40,columnNumber:1},this),`\n`,(0,o.jsxDEV)(h,{id:\"1521993350240473091\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-703d5ba7-6f0c-46a1-a369-4ca0452571b7.mdx\",lineNumber:42,columnNumber:1},this),`\n`,(0,o.jsxDEV)(t.p,{children:\"Concr\\xE8tement, ce retour en arri\\xE8re \\xE9tait une r\\xE9ponse directe aux attentes des supporteurs. \\xAB\\u202FWe heard them loud and clear\\u202F\\xBB \\u2013 \\xAB\\u202Fnous les avons entendus haut et fort\\u202F\\xBB, a d\\xE9clar\\xE9 le propri\\xE9taire Joey Saputo en \\xE9voquant les fans.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-703d5ba7-6f0c-46a1-a369-4ca0452571b7.mdx\",lineNumber:44,columnNumber:1},this),`\n`,(0,o.jsxDEV)(t.p,{children:[\"Le club a donc assum\\xE9 de \",(0,o.jsxDEV)(t.strong,{children:\"faire marche arri\\xE8re\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-703d5ba7-6f0c-46a1-a369-4ca0452571b7.mdx\",lineNumber:46,columnNumber:26},this),\" sur certains choix esth\\xE9tiques pour \",(0,o.jsxDEV)(t.strong,{children:\"r\\xE9parer le lien\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-703d5ba7-6f0c-46a1-a369-4ca0452571b7.mdx\",lineNumber:46,columnNumber:87},this),\" avec sa base de partisans. Ce changement de logo en 2023 a pour but de rassembler la communaut\\xE9 apr\\xE8s une p\\xE9riode de division et de controverse. C\\u2019est une d\\xE9marche humble de la part de l\\u2019organisation, qui admet que l\\u2019identit\\xE9 pr\\xE9c\\xE9dente n\\u2019a pas fait l\\u2019unanimit\\xE9 et qui tente de \\xAB\\xA0recoller les pots cass\\xE9s\\xA0\\xBB avec ses fans les plus fid\\xE8les.\"]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-703d5ba7-6f0c-46a1-a369-4ca0452571b7.mdx\",lineNumber:46,columnNumber:1},this),`\n`,(0,o.jsxDEV)(m,{src:\"/images/blog/logo-cf-montreal-2023/comparaison-logo-cf-montreal.webp\",alt:\"Comparaisons de l'\\xE9volution du logo CF Montr\\xE9al\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-703d5ba7-6f0c-46a1-a369-4ca0452571b7.mdx\",lineNumber:48,columnNumber:1},this),`\n`,(0,o.jsxDEV)(t.h2,{children:\"Des r\\xE9actions globalement positives\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-703d5ba7-6f0c-46a1-a369-4ca0452571b7.mdx\",lineNumber:53,columnNumber:1},this),`\n`,(0,o.jsxDEV)(t.p,{children:\"Le d\\xE9voilement du nouveau logo a re\\xE7u un bon accueil. Sur les r\\xE9seaux sociaux, dans les m\\xE9dias sp\\xE9cialis\\xE9s et au sein des groupes de supporters, le retour du bleu et de la fleur de lys a \\xE9t\\xE9 salu\\xE9.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-703d5ba7-6f0c-46a1-a369-4ca0452571b7.mdx\",lineNumber:55,columnNumber:1},this),`\n`,(0,o.jsxDEV)(t.p,{children:[\"Certains fans esp\\xE9raient n\\xE9anmoins un retour complet \\xE0 l\\u2019appellation \\xAB Impact \\xBB. Ce ne sera pas le cas. Gabriel Gervais l\\u2019a confirm\\xE9 : \",(0,o.jsxDEV)(t.strong,{children:\"le nom CF Montr\\xE9al reste\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-703d5ba7-6f0c-46a1-a369-4ca0452571b7.mdx\",lineNumber:57,columnNumber:136},this),\".\"]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-703d5ba7-6f0c-46a1-a369-4ca0452571b7.mdx\",lineNumber:57,columnNumber:1},this),`\n`,(0,o.jsxDEV)(t.p,{children:\"Toutefois, le club se montre ouvert \\xE0 honorer l\\u2019h\\xE9ritage de l\\u2019Impact autrement : nom symbolique dans le stade, r\\xE9f\\xE9rences visuelles, clins d\\u2019\\u0153il dans les communications.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-703d5ba7-6f0c-46a1-a369-4ca0452571b7.mdx\",lineNumber:59,columnNumber:1},this),`\n`,(0,o.jsxDEV)(t.p,{children:\"Dans l\\u2019ensemble, cette nouvelle identit\\xE9 est per\\xE7ue comme un compromis \\xE9quilibr\\xE9 entre l\\u2019ancien et le nouveau. Elle redonne au club une image plus align\\xE9e avec sa communaut\\xE9.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-703d5ba7-6f0c-46a1-a369-4ca0452571b7.mdx\",lineNumber:61,columnNumber:1},this),`\n`,(0,o.jsxDEV)(t.h2,{children:\"Conclusion\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-703d5ba7-6f0c-46a1-a369-4ca0452571b7.mdx\",lineNumber:63,columnNumber:1},this),`\n`,(0,o.jsxDEV)(t.p,{children:\"Ce changement d\\u2019identit\\xE9 s\\u2019inscrit possiblement dans une tendance plus large de la MLS o\\xF9 plusieurs clubs ont r\\xE9cemment proc\\xE9d\\xE9 \\xE0 des refontes de logo pour mieux refl\\xE9ter leur identit\\xE9 (on peut penser aux rebrandings du Columbus Crew, du Chicago Fire, etc.). Mais dans le cas de Montr\\xE9al, la d\\xE9marche a surtout \\xE9t\\xE9 guid\\xE9e par un besoin local : reconnecter le club \\xE0 sa base et \\xE0 son patrimoine.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-703d5ba7-6f0c-46a1-a369-4ca0452571b7.mdx\",lineNumber:65,columnNumber:1},this)]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-703d5ba7-6f0c-46a1-a369-4ca0452571b7.mdx\",lineNumber:1,columnNumber:1},this)}function _r(s={}){let{wrapper:t}=s.components||{};return t?(0,o.jsxDEV)(t,Object.assign({},s,{children:(0,o.jsxDEV)(ke,s,void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-703d5ba7-6f0c-46a1-a369-4ca0452571b7.mdx\"},this)}),void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-703d5ba7-6f0c-46a1-a369-4ca0452571b7.mdx\"},this):ke(s)}var vr=_r;function Te(s,t,m){throw new Error(\"Expected \"+(t?\"component\":\"object\")+\" `\"+s+\"` to be defined: you likely forgot to import, pass, or provide it.\"+(m?\"\\nIt\\u2019s referenced in your code at `\"+m+\"` in `/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-703d5ba7-6f0c-46a1-a369-4ca0452571b7.mdx`\":\"\"))}return gr(yr);})();\n/*! Bundled license information:\n\nreact/cjs/react-jsx-dev-runtime.development.js:\n  (**\n   * @license React\n   * react-jsx-dev-runtime.development.js\n   *\n   * Copyright (c) Facebook, Inc. and its affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *)\n*/\n;return Component;"}, "_id": "blog/fr/logo-cf-montreal-2023.mdx", "_raw": {"sourceFilePath": "blog/fr/logo-cf-montreal-2023.mdx", "sourceFileName": "logo-cf-montreal-2023.mdx", "sourceFileDir": "blog/fr", "contentType": "mdx", "flattenedPath": "blog/fr/logo-cf-montreal-2023"}, "type": "Post", "locale": "fr", "slug": "logo-cf-montreal-2023"}, {"title": "Deezer dévoile son nouveau logo en 2023", "excerpt": "L’année 2023 marque une ère nouvelle pour Deezer, le géant du streaming musical. Avec son audacieux rebranding incarné par un logo au cœur vibrant.", "date": "2023-11-15T00:00:00.000Z", "updated": "2023-11-15T00:00:00.000Z", "category": "Image de marque", "cover": "/images/blog/logo-deezer-2023/main-logo-deezer-2023.png", "published": true, "body": {"raw": "\nCe nouveau cœur violet divise, mais l’approche centrée-communauté colle bien à la promesse de <PERSON><PERSON>. On vous explique pourquoi—et comment le logo pourrait gagner ses galons.\n\n## Pourquoi changer ?\nEn 2023, Deezer cherchait deux choses :\n1. Clarifier sa mission pour devenir « la maison de la musique » plutôt qu’un simple service de streaming.\n2. Se différencier d’un marché saturé (Spotify et Apple Music) grâce à un symbole fort et facile à décliner.\n\nLa refonte s’inscrit donc moins dans un mouvement esthétique que dans une redéfinition de positionnement.\n\n<video\n  src=\"/images/blog/logo-deezer-2023/affiches-animees-deezer.webm\"\n  playsInline\n  autoPlay\n  muted\n  loop\n  style={{ width: '100%' }}\n/>\n\n## Un cœur qui pulse\n\nLe nouveau symbole de Deezer, un cœur violet animé, incarne littéralement leur message : « Feel the music ».\n\nL’animation se cale sur le tempo du morceau en cours et plonge l’utilisateur dans l’univers de la plateforme. Visuellement, ce cœur se mémorise d’un coup d’œil et tranche avec les teintes dominantes chez les autres services de streaming, comme pour le vert chez Spotify.\n\n<video\n  src=\"/images/blog/logo-deezer-2023/animation-logo-deezer.webm\"\n  playsInline\n  autoPlay\n  muted\n  loop\n  style={{ width: '100%' }}\n/>\n\nPour accompagner ce pictogramme, Deezer incarne une **nouvelle police** sur mesure : Deezer Sans. \n\nLes courbes reprennent les lignes du logo : suffisamment étroites pour les interfaces denses, assez larges pour soutenir un message fort sur une affiche.\n\n<Image\n    src='/images/blog/logo-deezer-2023/affiches-deezer.jpg'\n    alt=\"Affiches du nouveau logo Deezer\"\n/>\n\n<video\n  src=\"/images/blog/logo-deezer-2023/animation-video-deezer.webm\"\n  playsInline\n  autoPlay\n  muted\n  loop\n  style={{ width: '100%' }}\n/>\n\n## Une identité pour séduire un public plus jeune\n\nDu côté des créatifs, la prise de risque séduit : délaisser l'ancien logo avec l’égaliseur pour un cœur animé n’allait pas de soi. Dans les forums, certains applaudissent l’audace ; d’autres, plus sceptiques, trouvent le visuel trop proche des **codes d’une appli de rencontre**.\n\nLes abonnés sont partagés : beaucoup apprécient l’approche plus chaleureuse, tandis que les nostalgiques regrettent l’ancien logo.\n\nSur le plan de la mémorisation, le pari est gagnant : l’icône fonctionne aussi bien sur une montre connectée que sur un panneau d’affichage urbain. Le défi reste de préserver la reconnaissance hors animation — sur imprimé, favicon ou merchandise — en s’appuyant sur un univers visuel cohérent : ondes, halo violet, micro‑motions.\n\n<Image\n    src='/images/blog/logo-deezer-2023/illustrations-deezer.webp'\n    alt=\"Illustrations de l'identité visuelle de Deezer\"\n/>", "code": "var Component=(()=>{var lr=Object.create;var F=Object.defineProperty;var sr=Object.getOwnPropertyDescriptor;var cr=Object.getOwnPropertyNames;var fr=Object.getPrototypeOf,dr=Object.prototype.hasOwnProperty;var B=(s,a)=>()=>(a||s((a={exports:{}}).exports,a),a.exports),mr=(s,a)=>{for(var m in a)F(s,m,{get:a[m],enumerable:!0})},Ee=(s,a,m,y)=>{if(a&&typeof a==\"object\"||typeof a==\"function\")for(let _ of cr(a))!dr.call(s,_)&&_!==m&&F(s,_,{get:()=>a[_],enumerable:!(y=sr(a,_))||y.enumerable});return s};var br=(s,a,m)=>(m=s!=null?lr(fr(s)):{},Ee(a||!s||!s.__esModule?F(m,\"default\",{value:s,enumerable:!0}):m,s)),pr=s=>Ee(F({},\"__esModule\",{value:!0}),s);var we=B((xr,xe)=>{xe.exports=React});var Ne=B(G=>{\"use strict\";(function(){\"use strict\";var s=we(),a=Symbol.for(\"react.element\"),m=Symbol.for(\"react.portal\"),y=Symbol.for(\"react.fragment\"),_=Symbol.for(\"react.strict_mode\"),X=Symbol.for(\"react.profiler\"),K=Symbol.for(\"react.provider\"),H=Symbol.for(\"react.context\"),k=Symbol.for(\"react.forward_ref\"),A=Symbol.for(\"react.suspense\"),I=Symbol.for(\"react.suspense_list\"),C=Symbol.for(\"react.memo\"),z=Symbol.for(\"react.lazy\"),Ce=Symbol.for(\"react.offscreen\"),J=Symbol.iterator,Pe=\"@@iterator\";function Oe(e){if(e===null||typeof e!=\"object\")return null;var r=J&&e[J]||e[Pe];return typeof r==\"function\"?r:null}var x=s.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;function b(e){{for(var r=arguments.length,n=new Array(r>1?r-1:0),t=1;t<r;t++)n[t-1]=arguments[t];Se(\"error\",e,n)}}function Se(e,r,n){{var t=x.ReactDebugCurrentFrame,u=t.getStackAddendum();u!==\"\"&&(r+=\"%s\",n=n.concat([u]));var c=n.map(function(o){return String(o)});c.unshift(\"Warning: \"+r),Function.prototype.apply.call(console[e],console,c)}}var je=!1,De=!1,Fe=!1,Ae=!1,Ie=!1,Z;Z=Symbol.for(\"react.module.reference\");function ze(e){return!!(typeof e==\"string\"||typeof e==\"function\"||e===y||e===X||Ie||e===_||e===A||e===I||Ae||e===Ce||je||De||Fe||typeof e==\"object\"&&e!==null&&(e.$$typeof===z||e.$$typeof===C||e.$$typeof===K||e.$$typeof===H||e.$$typeof===k||e.$$typeof===Z||e.getModuleId!==void 0))}function Le(e,r,n){var t=e.displayName;if(t)return t;var u=r.displayName||r.name||\"\";return u!==\"\"?n+\"(\"+u+\")\":n}function Q(e){return e.displayName||\"Context\"}function v(e){if(e==null)return null;if(typeof e.tag==\"number\"&&b(\"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"),typeof e==\"function\")return e.displayName||e.name||null;if(typeof e==\"string\")return e;switch(e){case y:return\"Fragment\";case m:return\"Portal\";case X:return\"Profiler\";case _:return\"StrictMode\";case A:return\"Suspense\";case I:return\"SuspenseList\"}if(typeof e==\"object\")switch(e.$$typeof){case H:var r=e;return Q(r)+\".Consumer\";case K:var n=e;return Q(n._context)+\".Provider\";case k:return Le(e,e.render,\"ForwardRef\");case C:var t=e.displayName||null;return t!==null?t:v(e.type)||\"Memo\";case z:{var u=e,c=u._payload,o=u._init;try{return v(o(c))}catch{return null}}}return null}var E=Object.assign,R=0,ee,re,ne,te,ae,ie,oe;function ue(){}ue.__reactDisabledLog=!0;function Ye(){{if(R===0){ee=console.log,re=console.info,ne=console.warn,te=console.error,ae=console.group,ie=console.groupCollapsed,oe=console.groupEnd;var e={configurable:!0,enumerable:!0,value:ue,writable:!0};Object.defineProperties(console,{info:e,log:e,warn:e,error:e,group:e,groupCollapsed:e,groupEnd:e})}R++}}function $e(){{if(R--,R===0){var e={configurable:!0,enumerable:!0,writable:!0};Object.defineProperties(console,{log:E({},e,{value:ee}),info:E({},e,{value:re}),warn:E({},e,{value:ne}),error:E({},e,{value:te}),group:E({},e,{value:ae}),groupCollapsed:E({},e,{value:ie}),groupEnd:E({},e,{value:oe})})}R<0&&b(\"disabledDepth fell below zero. This is a bug in React. Please file an issue.\")}}var L=x.ReactCurrentDispatcher,Y;function P(e,r,n){{if(Y===void 0)try{throw Error()}catch(u){var t=u.stack.trim().match(/\\n( *(at )?)/);Y=t&&t[1]||\"\"}return`\n`+Y+e}}var $=!1,O;{var Me=typeof WeakMap==\"function\"?WeakMap:Map;O=new Me}function le(e,r){if(!e||$)return\"\";{var n=O.get(e);if(n!==void 0)return n}var t;$=!0;var u=Error.prepareStackTrace;Error.prepareStackTrace=void 0;var c;c=L.current,L.current=null,Ye();try{if(r){var o=function(){throw Error()};if(Object.defineProperty(o.prototype,\"props\",{set:function(){throw Error()}}),typeof Reflect==\"object\"&&Reflect.construct){try{Reflect.construct(o,[])}catch(h){t=h}Reflect.construct(e,[],o)}else{try{o.call()}catch(h){t=h}e.call(o.prototype)}}else{try{throw Error()}catch(h){t=h}e()}}catch(h){if(h&&t&&typeof h.stack==\"string\"){for(var i=h.stack.split(`\n`),p=t.stack.split(`\n`),f=i.length-1,d=p.length-1;f>=1&&d>=0&&i[f]!==p[d];)d--;for(;f>=1&&d>=0;f--,d--)if(i[f]!==p[d]){if(f!==1||d!==1)do if(f--,d--,d<0||i[f]!==p[d]){var g=`\n`+i[f].replace(\" at new \",\" at \");return e.displayName&&g.includes(\"<anonymous>\")&&(g=g.replace(\"<anonymous>\",e.displayName)),typeof e==\"function\"&&O.set(e,g),g}while(f>=1&&d>=0);break}}}finally{$=!1,L.current=c,$e(),Error.prepareStackTrace=u}var N=e?e.displayName||e.name:\"\",ye=N?P(N):\"\";return typeof e==\"function\"&&O.set(e,ye),ye}function We(e,r,n){return le(e,!1)}function qe(e){var r=e.prototype;return!!(r&&r.isReactComponent)}function S(e,r,n){if(e==null)return\"\";if(typeof e==\"function\")return le(e,qe(e));if(typeof e==\"string\")return P(e);switch(e){case A:return P(\"Suspense\");case I:return P(\"SuspenseList\")}if(typeof e==\"object\")switch(e.$$typeof){case k:return We(e.render);case C:return S(e.type,r,n);case z:{var t=e,u=t._payload,c=t._init;try{return S(c(u),r,n)}catch{}}}return\"\"}var j=Object.prototype.hasOwnProperty,se={},ce=x.ReactDebugCurrentFrame;function D(e){if(e){var r=e._owner,n=S(e.type,e._source,r?r.type:null);ce.setExtraStackFrame(n)}else ce.setExtraStackFrame(null)}function Ve(e,r,n,t,u){{var c=Function.call.bind(j);for(var o in e)if(c(e,o)){var i=void 0;try{if(typeof e[o]!=\"function\"){var p=Error((t||\"React class\")+\": \"+n+\" type `\"+o+\"` is invalid; it must be a function, usually from the `prop-types` package, but received `\"+typeof e[o]+\"`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.\");throw p.name=\"Invariant Violation\",p}i=e[o](r,o,t,n,null,\"SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED\")}catch(f){i=f}i&&!(i instanceof Error)&&(D(u),b(\"%s: type specification of %s `%s` is invalid; the type checker function must return `null` or an `Error` but returned a %s. You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument).\",t||\"React class\",n,o,typeof i),D(null)),i instanceof Error&&!(i.message in se)&&(se[i.message]=!0,D(u),b(\"Failed %s type: %s\",n,i.message),D(null))}}}var Ue=Array.isArray;function M(e){return Ue(e)}function Be(e){{var r=typeof Symbol==\"function\"&&Symbol.toStringTag,n=r&&e[Symbol.toStringTag]||e.constructor.name||\"Object\";return n}}function Ge(e){try{return fe(e),!1}catch{return!0}}function fe(e){return\"\"+e}function de(e){if(Ge(e))return b(\"The provided key is an unsupported type %s. This value must be coerced to a string before before using it here.\",Be(e)),fe(e)}var T=x.ReactCurrentOwner,Xe={key:!0,ref:!0,__self:!0,__source:!0},me,be,W;W={};function Ke(e){if(j.call(e,\"ref\")){var r=Object.getOwnPropertyDescriptor(e,\"ref\").get;if(r&&r.isReactWarning)return!1}return e.ref!==void 0}function He(e){if(j.call(e,\"key\")){var r=Object.getOwnPropertyDescriptor(e,\"key\").get;if(r&&r.isReactWarning)return!1}return e.key!==void 0}function Je(e,r){if(typeof e.ref==\"string\"&&T.current&&r&&T.current.stateNode!==r){var n=v(T.current.type);W[n]||(b('Component \"%s\" contains the string ref \"%s\". Support for string refs will be removed in a future major release. This case cannot be automatically converted to an arrow function. We ask you to manually fix this case by using useRef() or createRef() instead. Learn more about using refs safely here: https://reactjs.org/link/strict-mode-string-ref',v(T.current.type),e.ref),W[n]=!0)}}function Ze(e,r){{var n=function(){me||(me=!0,b(\"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)\",r))};n.isReactWarning=!0,Object.defineProperty(e,\"key\",{get:n,configurable:!0})}}function Qe(e,r){{var n=function(){be||(be=!0,b(\"%s: `ref` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)\",r))};n.isReactWarning=!0,Object.defineProperty(e,\"ref\",{get:n,configurable:!0})}}var er=function(e,r,n,t,u,c,o){var i={$$typeof:a,type:e,key:r,ref:n,props:o,_owner:c};return i._store={},Object.defineProperty(i._store,\"validated\",{configurable:!1,enumerable:!1,writable:!0,value:!1}),Object.defineProperty(i,\"_self\",{configurable:!1,enumerable:!1,writable:!1,value:t}),Object.defineProperty(i,\"_source\",{configurable:!1,enumerable:!1,writable:!1,value:u}),Object.freeze&&(Object.freeze(i.props),Object.freeze(i)),i};function rr(e,r,n,t,u){{var c,o={},i=null,p=null;n!==void 0&&(de(n),i=\"\"+n),He(r)&&(de(r.key),i=\"\"+r.key),Ke(r)&&(p=r.ref,Je(r,u));for(c in r)j.call(r,c)&&!Xe.hasOwnProperty(c)&&(o[c]=r[c]);if(e&&e.defaultProps){var f=e.defaultProps;for(c in f)o[c]===void 0&&(o[c]=f[c])}if(i||p){var d=typeof e==\"function\"?e.displayName||e.name||\"Unknown\":e;i&&Ze(o,d),p&&Qe(o,d)}return er(e,i,p,u,t,T.current,o)}}var q=x.ReactCurrentOwner,pe=x.ReactDebugCurrentFrame;function w(e){if(e){var r=e._owner,n=S(e.type,e._source,r?r.type:null);pe.setExtraStackFrame(n)}else pe.setExtraStackFrame(null)}var V;V=!1;function U(e){return typeof e==\"object\"&&e!==null&&e.$$typeof===a}function ge(){{if(q.current){var e=v(q.current.type);if(e)return`\n\nCheck the render method of \\``+e+\"`.\"}return\"\"}}function nr(e){{if(e!==void 0){var r=e.fileName.replace(/^.*[\\\\\\/]/,\"\"),n=e.lineNumber;return`\n\nCheck your code at `+r+\":\"+n+\".\"}return\"\"}}var ve={};function tr(e){{var r=ge();if(!r){var n=typeof e==\"string\"?e:e.displayName||e.name;n&&(r=`\n\nCheck the top-level render call using <`+n+\">.\")}return r}}function he(e,r){{if(!e._store||e._store.validated||e.key!=null)return;e._store.validated=!0;var n=tr(r);if(ve[n])return;ve[n]=!0;var t=\"\";e&&e._owner&&e._owner!==q.current&&(t=\" It was passed a child from \"+v(e._owner.type)+\".\"),w(e),b('Each child in a list should have a unique \"key\" prop.%s%s See https://reactjs.org/link/warning-keys for more information.',n,t),w(null)}}function _e(e,r){{if(typeof e!=\"object\")return;if(M(e))for(var n=0;n<e.length;n++){var t=e[n];U(t)&&he(t,r)}else if(U(e))e._store&&(e._store.validated=!0);else if(e){var u=Oe(e);if(typeof u==\"function\"&&u!==e.entries)for(var c=u.call(e),o;!(o=c.next()).done;)U(o.value)&&he(o.value,r)}}}function ar(e){{var r=e.type;if(r==null||typeof r==\"string\")return;var n;if(typeof r==\"function\")n=r.propTypes;else if(typeof r==\"object\"&&(r.$$typeof===k||r.$$typeof===C))n=r.propTypes;else return;if(n){var t=v(r);Ve(n,e.props,\"prop\",t,e)}else if(r.PropTypes!==void 0&&!V){V=!0;var u=v(r);b(\"Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?\",u||\"Unknown\")}typeof r.getDefaultProps==\"function\"&&!r.getDefaultProps.isReactClassApproved&&b(\"getDefaultProps is only used on classic React.createClass definitions. Use a static property named `defaultProps` instead.\")}}function ir(e){{for(var r=Object.keys(e.props),n=0;n<r.length;n++){var t=r[n];if(t!==\"children\"&&t!==\"key\"){w(e),b(\"Invalid prop `%s` supplied to `React.Fragment`. React.Fragment can only have `key` and `children` props.\",t),w(null);break}}e.ref!==null&&(w(e),b(\"Invalid attribute `ref` supplied to `React.Fragment`.\"),w(null))}}function or(e,r,n,t,u,c){{var o=ze(e);if(!o){var i=\"\";(e===void 0||typeof e==\"object\"&&e!==null&&Object.keys(e).length===0)&&(i+=\" You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.\");var p=nr(u);p?i+=p:i+=ge();var f;e===null?f=\"null\":M(e)?f=\"array\":e!==void 0&&e.$$typeof===a?(f=\"<\"+(v(e.type)||\"Unknown\")+\" />\",i=\" Did you accidentally export a JSX literal instead of a component?\"):f=typeof e,b(\"React.jsx: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: %s.%s\",f,i)}var d=rr(e,r,n,u,c);if(d==null)return d;if(o){var g=r.children;if(g!==void 0)if(t)if(M(g)){for(var N=0;N<g.length;N++)_e(g[N],e);Object.freeze&&Object.freeze(g)}else b(\"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\");else _e(g,e)}return e===y?ir(d):ar(d),d}}var ur=or;G.Fragment=y,G.jsxDEV=ur})()});var Te=B((Nr,Re)=>{\"use strict\";Re.exports=Ne()});var yr={};mr(yr,{default:()=>hr,frontmatter:()=>gr});var l=br(Te()),gr={title:\"Deezer d\\xE9voile son nouveau logo en 2023\",excerpt:\"L\\u2019ann\\xE9e 2023 marque une \\xE8re nouvelle pour Deezer, le g\\xE9ant du streaming musical. Avec son audacieux rebranding incarn\\xE9 par un logo au c\\u0153ur vibrant.\",date:\"2023-11-15\",updated:\"2023-11-15\",category:\"Image de marque\",cover:\"/images/blog/logo-deezer-2023/main-logo-deezer-2023.png\"};function ke(s){let a=Object.assign({p:\"p\",h2:\"h2\",ol:\"ol\",li:\"li\",strong:\"strong\"},s.components),{Image:m}=a;return m||_r(\"Image\",!0,\"47:1-50:3\"),(0,l.jsxDEV)(l.Fragment,{children:[(0,l.jsxDEV)(a.p,{children:\"Ce nouveau c\\u0153ur violet divise, mais l\\u2019approche centr\\xE9e-communaut\\xE9 colle bien \\xE0 la promesse de Deezer. On vous explique pourquoi\\u2014et comment le logo pourrait gagner ses galons.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-b0bf40d6-38c9-44a8-822d-ca492c96deb3.mdx\",lineNumber:10,columnNumber:1},this),`\n`,(0,l.jsxDEV)(a.h2,{children:\"Pourquoi changer\\xA0?\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-b0bf40d6-38c9-44a8-822d-ca492c96deb3.mdx\",lineNumber:12,columnNumber:1},this),`\n`,(0,l.jsxDEV)(a.p,{children:\"En 2023, Deezer cherchait deux choses\\xA0:\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-b0bf40d6-38c9-44a8-822d-ca492c96deb3.mdx\",lineNumber:13,columnNumber:1},this),`\n`,(0,l.jsxDEV)(a.ol,{children:[`\n`,(0,l.jsxDEV)(a.li,{children:\"Clarifier sa mission pour devenir \\xAB\\u202Fla maison de la musique\\u202F\\xBB plut\\xF4t qu\\u2019un simple service de streaming.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-b0bf40d6-38c9-44a8-822d-ca492c96deb3.mdx\",lineNumber:14,columnNumber:1},this),`\n`,(0,l.jsxDEV)(a.li,{children:\"Se diff\\xE9rencier d\\u2019un march\\xE9 satur\\xE9 (Spotify et Apple Music) gr\\xE2ce \\xE0 un symbole fort et facile \\xE0 d\\xE9cliner.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-b0bf40d6-38c9-44a8-822d-ca492c96deb3.mdx\",lineNumber:15,columnNumber:1},this),`\n`]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-b0bf40d6-38c9-44a8-822d-ca492c96deb3.mdx\",lineNumber:14,columnNumber:1},this),`\n`,(0,l.jsxDEV)(a.p,{children:\"La refonte s\\u2019inscrit donc moins dans un mouvement esth\\xE9tique que dans une red\\xE9finition de positionnement.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-b0bf40d6-38c9-44a8-822d-ca492c96deb3.mdx\",lineNumber:17,columnNumber:1},this),`\n`,(0,l.jsxDEV)(\"video\",{src:\"/images/blog/logo-deezer-2023/affiches-animees-deezer.webm\",playsInline:!0,autoPlay:!0,muted:!0,loop:!0,style:{width:\"100%\"}},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-b0bf40d6-38c9-44a8-822d-ca492c96deb3.mdx\",lineNumber:19,columnNumber:1},this),`\n`,(0,l.jsxDEV)(a.h2,{children:\"Un c\\u0153ur qui pulse\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-b0bf40d6-38c9-44a8-822d-ca492c96deb3.mdx\",lineNumber:28,columnNumber:1},this),`\n`,(0,l.jsxDEV)(a.p,{children:\"Le nouveau symbole de Deezer, un c\\u0153ur violet anim\\xE9, incarne litt\\xE9ralement leur message\\xA0: \\xAB\\xA0Feel the music\\xA0\\xBB.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-b0bf40d6-38c9-44a8-822d-ca492c96deb3.mdx\",lineNumber:30,columnNumber:1},this),`\n`,(0,l.jsxDEV)(a.p,{children:\"L\\u2019animation se cale sur le tempo du morceau en cours et plonge l\\u2019utilisateur dans l\\u2019univers de la plateforme. Visuellement, ce c\\u0153ur se m\\xE9morise d\\u2019un coup d\\u2019\\u0153il et tranche avec les teintes dominantes chez les autres services de streaming, comme pour le vert chez Spotify.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-b0bf40d6-38c9-44a8-822d-ca492c96deb3.mdx\",lineNumber:32,columnNumber:1},this),`\n`,(0,l.jsxDEV)(\"video\",{src:\"/images/blog/logo-deezer-2023/animation-logo-deezer.webm\",playsInline:!0,autoPlay:!0,muted:!0,loop:!0,style:{width:\"100%\"}},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-b0bf40d6-38c9-44a8-822d-ca492c96deb3.mdx\",lineNumber:34,columnNumber:1},this),`\n`,(0,l.jsxDEV)(a.p,{children:[\"Pour accompagner ce pictogramme, Deezer incarne une \",(0,l.jsxDEV)(a.strong,{children:\"nouvelle police\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-b0bf40d6-38c9-44a8-822d-ca492c96deb3.mdx\",lineNumber:43,columnNumber:53},this),\" sur\\u202Fmesure\\xA0: Deezer Sans.\"]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-b0bf40d6-38c9-44a8-822d-ca492c96deb3.mdx\",lineNumber:43,columnNumber:1},this),`\n`,(0,l.jsxDEV)(a.p,{children:\"Les courbes reprennent les lignes du logo\\xA0: suffisamment \\xE9troites pour les interfaces denses, assez larges pour soutenir un message fort sur une affiche.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-b0bf40d6-38c9-44a8-822d-ca492c96deb3.mdx\",lineNumber:45,columnNumber:1},this),`\n`,(0,l.jsxDEV)(m,{src:\"/images/blog/logo-deezer-2023/affiches-deezer.jpg\",alt:\"Affiches du nouveau logo Deezer\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-b0bf40d6-38c9-44a8-822d-ca492c96deb3.mdx\",lineNumber:47,columnNumber:1},this),`\n`,(0,l.jsxDEV)(\"video\",{src:\"/images/blog/logo-deezer-2023/animation-video-deezer.webm\",playsInline:!0,autoPlay:!0,muted:!0,loop:!0,style:{width:\"100%\"}},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-b0bf40d6-38c9-44a8-822d-ca492c96deb3.mdx\",lineNumber:52,columnNumber:1},this),`\n`,(0,l.jsxDEV)(a.h2,{children:\"Une identit\\xE9 pour s\\xE9duire un public plus jeune\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-b0bf40d6-38c9-44a8-822d-ca492c96deb3.mdx\",lineNumber:61,columnNumber:1},this),`\n`,(0,l.jsxDEV)(a.p,{children:[\"Du c\\xF4t\\xE9 des cr\\xE9atifs, la prise de risque s\\xE9duit\\u202F: d\\xE9laisser l'ancien logo avec l\\u2019\\xE9galiseur pour un c\\u0153ur anim\\xE9 n\\u2019allait pas de soi. Dans les forums, certains applaudissent l\\u2019audace\\u202F; d\\u2019autres, plus sceptiques, trouvent le visuel trop proche des \",(0,l.jsxDEV)(a.strong,{children:\"codes d\\u2019une appli de rencontre\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-b0bf40d6-38c9-44a8-822d-ca492c96deb3.mdx\",lineNumber:63,columnNumber:245},this),\".\"]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-b0bf40d6-38c9-44a8-822d-ca492c96deb3.mdx\",lineNumber:63,columnNumber:1},this),`\n`,(0,l.jsxDEV)(a.p,{children:\"Les abonn\\xE9s sont partag\\xE9s\\xA0: beaucoup appr\\xE9cient l\\u2019approche plus chaleureuse, tandis que les nostalgiques regrettent l\\u2019ancien logo.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-b0bf40d6-38c9-44a8-822d-ca492c96deb3.mdx\",lineNumber:65,columnNumber:1},this),`\n`,(0,l.jsxDEV)(a.p,{children:\"Sur le plan de la m\\xE9morisation, le pari est gagnant\\u202F: l\\u2019ic\\xF4ne fonctionne aussi bien sur une montre connect\\xE9e que sur un panneau d\\u2019affichage urbain. Le d\\xE9fi reste de pr\\xE9server la reconnaissance hors animation\\xA0\\u2014 sur imprim\\xE9, favicon ou merchandise \\u2014 en s\\u2019appuyant sur un univers visuel coh\\xE9rent\\u202F: ondes, halo violet, micro\\u2011motions.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-b0bf40d6-38c9-44a8-822d-ca492c96deb3.mdx\",lineNumber:67,columnNumber:1},this),`\n`,(0,l.jsxDEV)(m,{src:\"/images/blog/logo-deezer-2023/illustrations-deezer.webp\",alt:\"Illustrations de l'identit\\xE9 visuelle de Deezer\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-b0bf40d6-38c9-44a8-822d-ca492c96deb3.mdx\",lineNumber:69,columnNumber:1},this)]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-b0bf40d6-38c9-44a8-822d-ca492c96deb3.mdx\",lineNumber:1,columnNumber:1},this)}function vr(s={}){let{wrapper:a}=s.components||{};return a?(0,l.jsxDEV)(a,Object.assign({},s,{children:(0,l.jsxDEV)(ke,s,void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-b0bf40d6-38c9-44a8-822d-ca492c96deb3.mdx\"},this)}),void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-b0bf40d6-38c9-44a8-822d-ca492c96deb3.mdx\"},this):ke(s)}var hr=vr;function _r(s,a,m){throw new Error(\"Expected \"+(a?\"component\":\"object\")+\" `\"+s+\"` to be defined: you likely forgot to import, pass, or provide it.\"+(m?\"\\nIt\\u2019s referenced in your code at `\"+m+\"` in `/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-b0bf40d6-38c9-44a8-822d-ca492c96deb3.mdx`\":\"\"))}return pr(yr);})();\n/*! Bundled license information:\n\nreact/cjs/react-jsx-dev-runtime.development.js:\n  (**\n   * @license React\n   * react-jsx-dev-runtime.development.js\n   *\n   * Copyright (c) Facebook, Inc. and its affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *)\n*/\n;return Component;"}, "_id": "blog/fr/logo-deezer-2023.mdx", "_raw": {"sourceFilePath": "blog/fr/logo-deezer-2023.mdx", "sourceFileName": "logo-deezer-2023.mdx", "sourceFileDir": "blog/fr", "contentType": "mdx", "flattenedPath": "blog/fr/logo-deezer-2023"}, "type": "Post", "locale": "fr", "slug": "logo-deezer-2023"}]