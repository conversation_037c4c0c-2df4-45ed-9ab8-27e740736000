{"title": "Nouveau logo CF Montréal : un symbole de renouveau pour 2023", "excerpt": "En 2023, le CF Montréal annonce le remplacement de son logo, en forme de flocon de neige, par un nouveau logo bleu et noir, au centre duquel on retrouve une fleur de lys et une référence à l’année de fondation du club, 1993.", "date": "2023-05-04T00:00:00.000Z", "updated": "2023-05-04T00:00:00.000Z", "category": "Image de marque", "cover": "/images/blog/logo-cf-montreal-2023/main-logo-cf-montreal-2023.png", "published": true, "body": {"raw": "\nLes partisans du CF Montréal l'attendaient. Le club de soccer montréalais, qui évolue en Major League Soccer (MLS), a dévoilé en 2023 un nouveau logo marquant un tournant majeur dans son identité visuelle. Après la controverse suscitée par le rebranding de 2021, ce changement s'accompagne du retour de symboles emblématiques comme la fleur de lys.\n\nPourquoi ce changement ? Que symbolise ce nouveau logo ? Et comment a-t-il été reçu par les fans ? On fait le point.\n\n## Retour aux sources : un logo inspiré de l'histoire du club\n\nLe **nouveau logo du CF Montréal** rend hommage à l'héritage du club en réintroduisant des éléments familiers aux supporteurs de la première heure. Conçu sous la forme d’un écusson traditionnel, il incorpore des symboles historiques forts de l’Impact de Montréal (le nom d’origine du club).\n\n<Image\n    src='/images/blog/logo-cf-montreal-2023/explication-logo-cf-montreal.webp'\n    alt=\"Explication du logo CF Montréal\"\n/>\n\n- Le bouclier qui rappelle les anciens logos du club\n- La fleur de lys, symbole du Québec et figurant autrefois sur l'emblème de l'Impact\n- Le bleu « Impact » redevient la couleur prédominante du logo, accompagné de noir\n- Les bandes noires et bleues qui rappellent les premiers maillots du club et les débuts professionnels dans les années 1990\n- « 1993 » l'année de la saison inaugurale du club\n\nEn plus du nom officiel **CF Montréal** bien en vue, l'ensemble de ces éléments confère au blason une identité riche de sens, ancrée dans l'histoire et la culture du club tout en restant tournée vers l'avenir.\n\n\n\n## Pourquoi le CF Montréal a changé de logo en 2023 ?\nLe changement de logo du CF Montréal en 2023 s'explique avant tout par la volonté de **corriger le tir** après le rebranding controversé de 2021.\n\nPour rappel, le 14 janvier 2021, la direction de l'époque avait annoncé que l'Impact de Montréal changeait de nom et d'identité visuelle pour devenir « Club de Foot Montréal ». Ce rebranding radical – nouveau nom et logo en forme de flocon de neige – avait suscité une vive opposition chez une partie des supporteurs et de la communauté du soccer québécois.\n\nBeaucoup de fans historiques ne se reconnaissaient pas dans ce nouvel emblème, jugé trop éloigné de l'histoire du club et de l'identité montréalaise.\n\nFace à cette réaction négative, les dirigeants ont décidé d’écouter la voix des partisans. L’arrivée d’un nouveau président en 2022, Gabriel Gervais (lui-même ancien joueur du club), a marqué un tournant. Gervais a reconnu publiquement qu’il était **difficile de s’identifier à l’ancien logo** : « La fleur de lys est où ? La couleur bleue est où ? Comment sommes-nous représentés ? » s'est-il interrogé, soulignant l'absence des symboles traditionnels. Cet aveu a mis en lumière **l'erreur de branding** commise en 2021. Ainsi, moins de deux ans après la précédente refonte, le CF Montréal a choisi de changer à nouveau de logo afin de **réaligner son identité visuelle sur son ADN d'origine**.\n\n<Tweet id=\"1521993350240473091\" />\n\nConcrètement, ce retour en arrière était une réponse directe aux attentes des supporteurs. « We heard them loud and clear » – « nous les avons entendus haut et fort », a déclaré le propriétaire Joey Saputo en évoquant les fans.\n\nLe club a donc assumé de **faire marche arrière** sur certains choix esthétiques pour **réparer le lien** avec sa base de partisans. Ce changement de logo en 2023 a pour but de rassembler la communauté après une période de division et de controverse. C’est une démarche humble de la part de l’organisation, qui admet que l’identité précédente n’a pas fait l’unanimité et qui tente de « recoller les pots cassés » avec ses fans les plus fidèles.\n\n<Image\n    src='/images/blog/logo-cf-montreal-2023/comparaison-logo-cf-montreal.webp'\n    alt=\"Comparaisons de l'évolution du logo CF Montréal\"\n/>\n\n## Des réactions globalement positives\n\nLe dévoilement du nouveau logo a reçu un bon accueil. Sur les réseaux sociaux, dans les médias spécialisés et au sein des groupes de supporters, le retour du bleu et de la fleur de lys a été salué.\n\nCertains fans espéraient néanmoins un retour complet à l’appellation « Impact ». Ce ne sera pas le cas. Gabriel Gervais l’a confirmé : **le nom CF Montréal reste**.\n\nToutefois, le club se montre ouvert à honorer l’héritage de l’Impact autrement : nom symbolique dans le stade, références visuelles, clins d’œil dans les communications.\n\nDans l’ensemble, cette nouvelle identité est perçue comme un compromis équilibré entre l’ancien et le nouveau. Elle redonne au club une image plus alignée avec sa communauté.\n\n## Conclusion\n\nCe changement d’identité s’inscrit possiblement dans une tendance plus large de la MLS où plusieurs clubs ont récemment procédé à des refontes de logo pour mieux refléter leur identité (on peut penser aux rebrandings du Columbus Crew, du Chicago Fire, etc.). Mais dans le cas de Montréal, la démarche a surtout été guidée par un besoin local : reconnecter le club à sa base et à son patrimoine.", "code": "var Component=(()=>{var sr=Object.create;var D=Object.defineProperty;var cr=Object.getOwnPropertyDescriptor;var dr=Object.getOwnPropertyNames;var fr=Object.getPrototypeOf,mr=Object.prototype.hasOwnProperty;var B=(s,t)=>()=>(t||s((t={exports:{}}).exports,t),t.exports),br=(s,t)=>{for(var m in t)D(s,m,{get:t[m],enumerable:!0})},xe=(s,t,m,h)=>{if(t&&typeof t==\"object\"||typeof t==\"function\")for(let y of dr(t))!mr.call(s,y)&&y!==m&&D(s,y,{get:()=>t[y],enumerable:!(h=cr(t,y))||h.enumerable});return s};var pr=(s,t,m)=>(m=s!=null?sr(fr(s)):{},xe(t||!s||!s.__esModule?D(m,\"default\",{value:s,enumerable:!0}):m,s)),gr=s=>xe(D({},\"__esModule\",{value:!0}),s);var Ee=B((Nr,Ne)=>{Ne.exports=React});var we=B(z=>{\"use strict\";(function(){\"use strict\";var s=Ee(),t=Symbol.for(\"react.element\"),m=Symbol.for(\"react.portal\"),h=Symbol.for(\"react.fragment\"),y=Symbol.for(\"react.strict_mode\"),X=Symbol.for(\"react.profiler\"),K=Symbol.for(\"react.provider\"),H=Symbol.for(\"react.context\"),k=Symbol.for(\"react.forward_ref\"),M=Symbol.for(\"react.suspense\"),A=Symbol.for(\"react.suspense_list\"),T=Symbol.for(\"react.memo\"),I=Symbol.for(\"react.lazy\"),Oe=Symbol.for(\"react.offscreen\"),J=Symbol.iterator,Pe=\"@@iterator\";function Se(e){if(e===null||typeof e!=\"object\")return null;var r=J&&e[J]||e[Pe];return typeof r==\"function\"?r:null}var N=s.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;function b(e){{for(var r=arguments.length,n=new Array(r>1?r-1:0),a=1;a<r;a++)n[a-1]=arguments[a];je(\"error\",e,n)}}function je(e,r,n){{var a=N.ReactDebugCurrentFrame,u=a.getStackAddendum();u!==\"\"&&(r+=\"%s\",n=n.concat([u]));var c=n.map(function(l){return String(l)});c.unshift(\"Warning: \"+r),Function.prototype.apply.call(console[e],console,c)}}var Fe=!1,De=!1,Me=!1,Ae=!1,Ie=!1,Q;Q=Symbol.for(\"react.module.reference\");function Le(e){return!!(typeof e==\"string\"||typeof e==\"function\"||e===h||e===X||Ie||e===y||e===M||e===A||Ae||e===Oe||Fe||De||Me||typeof e==\"object\"&&e!==null&&(e.$$typeof===I||e.$$typeof===T||e.$$typeof===K||e.$$typeof===H||e.$$typeof===k||e.$$typeof===Q||e.getModuleId!==void 0))}function qe(e,r,n){var a=e.displayName;if(a)return a;var u=r.displayName||r.name||\"\";return u!==\"\"?n+\"(\"+u+\")\":n}function Z(e){return e.displayName||\"Context\"}function _(e){if(e==null)return null;if(typeof e.tag==\"number\"&&b(\"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"),typeof e==\"function\")return e.displayName||e.name||null;if(typeof e==\"string\")return e;switch(e){case h:return\"Fragment\";case m:return\"Portal\";case X:return\"Profiler\";case y:return\"StrictMode\";case M:return\"Suspense\";case A:return\"SuspenseList\"}if(typeof e==\"object\")switch(e.$$typeof){case H:var r=e;return Z(r)+\".Consumer\";case K:var n=e;return Z(n._context)+\".Provider\";case k:return qe(e,e.render,\"ForwardRef\");case T:var a=e.displayName||null;return a!==null?a:_(e.type)||\"Memo\";case I:{var u=e,c=u._payload,l=u._init;try{return _(l(c))}catch{return null}}}return null}var x=Object.assign,C=0,ee,re,ne,te,ae,oe,ie;function le(){}le.__reactDisabledLog=!0;function We(){{if(C===0){ee=console.log,re=console.info,ne=console.warn,te=console.error,ae=console.group,oe=console.groupCollapsed,ie=console.groupEnd;var e={configurable:!0,enumerable:!0,value:le,writable:!0};Object.defineProperties(console,{info:e,log:e,warn:e,error:e,group:e,groupCollapsed:e,groupEnd:e})}C++}}function Ye(){{if(C--,C===0){var e={configurable:!0,enumerable:!0,writable:!0};Object.defineProperties(console,{log:x({},e,{value:ee}),info:x({},e,{value:re}),warn:x({},e,{value:ne}),error:x({},e,{value:te}),group:x({},e,{value:ae}),groupCollapsed:x({},e,{value:oe}),groupEnd:x({},e,{value:ie})})}C<0&&b(\"disabledDepth fell below zero. This is a bug in React. Please file an issue.\")}}var L=N.ReactCurrentDispatcher,q;function O(e,r,n){{if(q===void 0)try{throw Error()}catch(u){var a=u.stack.trim().match(/\\n( *(at )?)/);q=a&&a[1]||\"\"}return`\n`+q+e}}var W=!1,P;{var $e=typeof WeakMap==\"function\"?WeakMap:Map;P=new $e}function ue(e,r){if(!e||W)return\"\";{var n=P.get(e);if(n!==void 0)return n}var a;W=!0;var u=Error.prepareStackTrace;Error.prepareStackTrace=void 0;var c;c=L.current,L.current=null,We();try{if(r){var l=function(){throw Error()};if(Object.defineProperty(l.prototype,\"props\",{set:function(){throw Error()}}),typeof Reflect==\"object\"&&Reflect.construct){try{Reflect.construct(l,[])}catch(v){a=v}Reflect.construct(e,[],l)}else{try{l.call()}catch(v){a=v}e.call(l.prototype)}}else{try{throw Error()}catch(v){a=v}e()}}catch(v){if(v&&a&&typeof v.stack==\"string\"){for(var i=v.stack.split(`\n`),p=a.stack.split(`\n`),d=i.length-1,f=p.length-1;d>=1&&f>=0&&i[d]!==p[f];)f--;for(;d>=1&&f>=0;d--,f--)if(i[d]!==p[f]){if(d!==1||f!==1)do if(d--,f--,f<0||i[d]!==p[f]){var g=`\n`+i[d].replace(\" at new \",\" at \");return e.displayName&&g.includes(\"<anonymous>\")&&(g=g.replace(\"<anonymous>\",e.displayName)),typeof e==\"function\"&&P.set(e,g),g}while(d>=1&&f>=0);break}}}finally{W=!1,L.current=c,Ye(),Error.prepareStackTrace=u}var w=e?e.displayName||e.name:\"\",ye=w?O(w):\"\";return typeof e==\"function\"&&P.set(e,ye),ye}function Ve(e,r,n){return ue(e,!1)}function Ue(e){var r=e.prototype;return!!(r&&r.isReactComponent)}function S(e,r,n){if(e==null)return\"\";if(typeof e==\"function\")return ue(e,Ue(e));if(typeof e==\"string\")return O(e);switch(e){case M:return O(\"Suspense\");case A:return O(\"SuspenseList\")}if(typeof e==\"object\")switch(e.$$typeof){case k:return Ve(e.render);case T:return S(e.type,r,n);case I:{var a=e,u=a._payload,c=a._init;try{return S(c(u),r,n)}catch{}}}return\"\"}var j=Object.prototype.hasOwnProperty,se={},ce=N.ReactDebugCurrentFrame;function F(e){if(e){var r=e._owner,n=S(e.type,e._source,r?r.type:null);ce.setExtraStackFrame(n)}else ce.setExtraStackFrame(null)}function Ge(e,r,n,a,u){{var c=Function.call.bind(j);for(var l in e)if(c(e,l)){var i=void 0;try{if(typeof e[l]!=\"function\"){var p=Error((a||\"React class\")+\": \"+n+\" type `\"+l+\"` is invalid; it must be a function, usually from the `prop-types` package, but received `\"+typeof e[l]+\"`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.\");throw p.name=\"Invariant Violation\",p}i=e[l](r,l,a,n,null,\"SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED\")}catch(d){i=d}i&&!(i instanceof Error)&&(F(u),b(\"%s: type specification of %s `%s` is invalid; the type checker function must return `null` or an `Error` but returned a %s. You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument).\",a||\"React class\",n,l,typeof i),F(null)),i instanceof Error&&!(i.message in se)&&(se[i.message]=!0,F(u),b(\"Failed %s type: %s\",n,i.message),F(null))}}}var Be=Array.isArray;function Y(e){return Be(e)}function ze(e){{var r=typeof Symbol==\"function\"&&Symbol.toStringTag,n=r&&e[Symbol.toStringTag]||e.constructor.name||\"Object\";return n}}function Xe(e){try{return de(e),!1}catch{return!0}}function de(e){return\"\"+e}function fe(e){if(Xe(e))return b(\"The provided key is an unsupported type %s. This value must be coerced to a string before before using it here.\",ze(e)),de(e)}var R=N.ReactCurrentOwner,Ke={key:!0,ref:!0,__self:!0,__source:!0},me,be,$;$={};function He(e){if(j.call(e,\"ref\")){var r=Object.getOwnPropertyDescriptor(e,\"ref\").get;if(r&&r.isReactWarning)return!1}return e.ref!==void 0}function Je(e){if(j.call(e,\"key\")){var r=Object.getOwnPropertyDescriptor(e,\"key\").get;if(r&&r.isReactWarning)return!1}return e.key!==void 0}function Qe(e,r){if(typeof e.ref==\"string\"&&R.current&&r&&R.current.stateNode!==r){var n=_(R.current.type);$[n]||(b('Component \"%s\" contains the string ref \"%s\". Support for string refs will be removed in a future major release. This case cannot be automatically converted to an arrow function. We ask you to manually fix this case by using useRef() or createRef() instead. Learn more about using refs safely here: https://reactjs.org/link/strict-mode-string-ref',_(R.current.type),e.ref),$[n]=!0)}}function Ze(e,r){{var n=function(){me||(me=!0,b(\"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)\",r))};n.isReactWarning=!0,Object.defineProperty(e,\"key\",{get:n,configurable:!0})}}function er(e,r){{var n=function(){be||(be=!0,b(\"%s: `ref` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)\",r))};n.isReactWarning=!0,Object.defineProperty(e,\"ref\",{get:n,configurable:!0})}}var rr=function(e,r,n,a,u,c,l){var i={$$typeof:t,type:e,key:r,ref:n,props:l,_owner:c};return i._store={},Object.defineProperty(i._store,\"validated\",{configurable:!1,enumerable:!1,writable:!0,value:!1}),Object.defineProperty(i,\"_self\",{configurable:!1,enumerable:!1,writable:!1,value:a}),Object.defineProperty(i,\"_source\",{configurable:!1,enumerable:!1,writable:!1,value:u}),Object.freeze&&(Object.freeze(i.props),Object.freeze(i)),i};function nr(e,r,n,a,u){{var c,l={},i=null,p=null;n!==void 0&&(fe(n),i=\"\"+n),Je(r)&&(fe(r.key),i=\"\"+r.key),He(r)&&(p=r.ref,Qe(r,u));for(c in r)j.call(r,c)&&!Ke.hasOwnProperty(c)&&(l[c]=r[c]);if(e&&e.defaultProps){var d=e.defaultProps;for(c in d)l[c]===void 0&&(l[c]=d[c])}if(i||p){var f=typeof e==\"function\"?e.displayName||e.name||\"Unknown\":e;i&&Ze(l,f),p&&er(l,f)}return rr(e,i,p,u,a,R.current,l)}}var V=N.ReactCurrentOwner,pe=N.ReactDebugCurrentFrame;function E(e){if(e){var r=e._owner,n=S(e.type,e._source,r?r.type:null);pe.setExtraStackFrame(n)}else pe.setExtraStackFrame(null)}var U;U=!1;function G(e){return typeof e==\"object\"&&e!==null&&e.$$typeof===t}function ge(){{if(V.current){var e=_(V.current.type);if(e)return`\n\nCheck the render method of \\``+e+\"`.\"}return\"\"}}function tr(e){{if(e!==void 0){var r=e.fileName.replace(/^.*[\\\\\\/]/,\"\"),n=e.lineNumber;return`\n\nCheck your code at `+r+\":\"+n+\".\"}return\"\"}}var he={};function ar(e){{var r=ge();if(!r){var n=typeof e==\"string\"?e:e.displayName||e.name;n&&(r=`\n\nCheck the top-level render call using <`+n+\">.\")}return r}}function _e(e,r){{if(!e._store||e._store.validated||e.key!=null)return;e._store.validated=!0;var n=ar(r);if(he[n])return;he[n]=!0;var a=\"\";e&&e._owner&&e._owner!==V.current&&(a=\" It was passed a child from \"+_(e._owner.type)+\".\"),E(e),b('Each child in a list should have a unique \"key\" prop.%s%s See https://reactjs.org/link/warning-keys for more information.',n,a),E(null)}}function ve(e,r){{if(typeof e!=\"object\")return;if(Y(e))for(var n=0;n<e.length;n++){var a=e[n];G(a)&&_e(a,r)}else if(G(e))e._store&&(e._store.validated=!0);else if(e){var u=Se(e);if(typeof u==\"function\"&&u!==e.entries)for(var c=u.call(e),l;!(l=c.next()).done;)G(l.value)&&_e(l.value,r)}}}function or(e){{var r=e.type;if(r==null||typeof r==\"string\")return;var n;if(typeof r==\"function\")n=r.propTypes;else if(typeof r==\"object\"&&(r.$$typeof===k||r.$$typeof===T))n=r.propTypes;else return;if(n){var a=_(r);Ge(n,e.props,\"prop\",a,e)}else if(r.PropTypes!==void 0&&!U){U=!0;var u=_(r);b(\"Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?\",u||\"Unknown\")}typeof r.getDefaultProps==\"function\"&&!r.getDefaultProps.isReactClassApproved&&b(\"getDefaultProps is only used on classic React.createClass definitions. Use a static property named `defaultProps` instead.\")}}function ir(e){{for(var r=Object.keys(e.props),n=0;n<r.length;n++){var a=r[n];if(a!==\"children\"&&a!==\"key\"){E(e),b(\"Invalid prop `%s` supplied to `React.Fragment`. React.Fragment can only have `key` and `children` props.\",a),E(null);break}}e.ref!==null&&(E(e),b(\"Invalid attribute `ref` supplied to `React.Fragment`.\"),E(null))}}function lr(e,r,n,a,u,c){{var l=Le(e);if(!l){var i=\"\";(e===void 0||typeof e==\"object\"&&e!==null&&Object.keys(e).length===0)&&(i+=\" You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.\");var p=tr(u);p?i+=p:i+=ge();var d;e===null?d=\"null\":Y(e)?d=\"array\":e!==void 0&&e.$$typeof===t?(d=\"<\"+(_(e.type)||\"Unknown\")+\" />\",i=\" Did you accidentally export a JSX literal instead of a component?\"):d=typeof e,b(\"React.jsx: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: %s.%s\",d,i)}var f=nr(e,r,n,u,c);if(f==null)return f;if(l){var g=r.children;if(g!==void 0)if(a)if(Y(g)){for(var w=0;w<g.length;w++)ve(g[w],e);Object.freeze&&Object.freeze(g)}else b(\"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\");else ve(g,e)}return e===h?ir(f):or(f),f}}var ur=lr;z.Fragment=h,z.jsxDEV=ur})()});var Re=B((wr,Ce)=>{\"use strict\";Ce.exports=we()});var yr={};br(yr,{default:()=>vr,frontmatter:()=>hr});var o=pr(Re()),hr={title:\"Nouveau logo CF Montr\\xE9al\\xA0: un symbole de renouveau pour 2023\",excerpt:\"En 2023, le CF Montr\\xE9al annonce le remplacement de son logo, en forme de flocon de neige, par un nouveau logo bleu et noir, au centre duquel on retrouve une fleur de lys et une r\\xE9f\\xE9rence \\xE0 l\\u2019ann\\xE9e de fondation du club, 1993.\",date:\"2023-05-04\",updated:\"2023-05-04\",category:\"Image de marque\",cover:\"/images/blog/logo-cf-montreal-2023/main-logo-cf-montreal-2023.png\"};function ke(s){let t=Object.assign({p:\"p\",h2:\"h2\",strong:\"strong\",ul:\"ul\",li:\"li\"},s.components),{Image:m,Tweet:h}=t;return m||Te(\"Image\",!0,\"18:1-21:3\"),h||Te(\"Tweet\",!0,\"42:1-42:35\"),(0,o.jsxDEV)(o.Fragment,{children:[(0,o.jsxDEV)(t.p,{children:\"Les partisans du CF Montr\\xE9al l'attendaient. Le club de soccer montr\\xE9alais, qui \\xE9volue en Major League Soccer (MLS), a d\\xE9voil\\xE9 en 2023 un nouveau logo marquant un tournant majeur dans son identit\\xE9 visuelle. Apr\\xE8s la controverse suscit\\xE9e par le rebranding de 2021, ce changement s'accompagne du retour de symboles embl\\xE9matiques comme la fleur de lys.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-703d5ba7-6f0c-46a1-a369-4ca0452571b7.mdx\",lineNumber:10,columnNumber:1},this),`\n`,(0,o.jsxDEV)(t.p,{children:\"Pourquoi ce changement\\xA0? Que symbolise ce nouveau logo\\xA0? Et comment a-t-il \\xE9t\\xE9 re\\xE7u par les fans\\xA0? On fait le point.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-703d5ba7-6f0c-46a1-a369-4ca0452571b7.mdx\",lineNumber:12,columnNumber:1},this),`\n`,(0,o.jsxDEV)(t.h2,{children:\"Retour aux sources\\xA0: un logo inspir\\xE9 de l'histoire du club\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-703d5ba7-6f0c-46a1-a369-4ca0452571b7.mdx\",lineNumber:14,columnNumber:1},this),`\n`,(0,o.jsxDEV)(t.p,{children:[\"Le \",(0,o.jsxDEV)(t.strong,{children:\"nouveau logo du CF Montr\\xE9al\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-703d5ba7-6f0c-46a1-a369-4ca0452571b7.mdx\",lineNumber:16,columnNumber:4},this),\" rend hommage \\xE0 l'h\\xE9ritage du club en r\\xE9introduisant des \\xE9l\\xE9ments familiers aux supporteurs de la premi\\xE8re heure. Con\\xE7u sous la forme d\\u2019un \\xE9cusson traditionnel, il incorpore des symboles historiques forts de l\\u2019Impact de Montr\\xE9al (le nom d\\u2019origine du club).\"]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-703d5ba7-6f0c-46a1-a369-4ca0452571b7.mdx\",lineNumber:16,columnNumber:1},this),`\n`,(0,o.jsxDEV)(m,{src:\"/images/blog/logo-cf-montreal-2023/explication-logo-cf-montreal.webp\",alt:\"Explication du logo CF Montr\\xE9al\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-703d5ba7-6f0c-46a1-a369-4ca0452571b7.mdx\",lineNumber:18,columnNumber:1},this),`\n`,(0,o.jsxDEV)(t.ul,{children:[`\n`,(0,o.jsxDEV)(t.li,{children:\"Le bouclier qui rappelle les anciens logos du club\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-703d5ba7-6f0c-46a1-a369-4ca0452571b7.mdx\",lineNumber:23,columnNumber:1},this),`\n`,(0,o.jsxDEV)(t.li,{children:\"La fleur de lys, symbole du Qu\\xE9bec et figurant autrefois sur l'embl\\xE8me de l'Impact\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-703d5ba7-6f0c-46a1-a369-4ca0452571b7.mdx\",lineNumber:24,columnNumber:1},this),`\n`,(0,o.jsxDEV)(t.li,{children:\"Le bleu \\xAB\\xA0Impact\\xA0\\xBB redevient la couleur pr\\xE9dominante du logo, accompagn\\xE9 de noir\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-703d5ba7-6f0c-46a1-a369-4ca0452571b7.mdx\",lineNumber:25,columnNumber:1},this),`\n`,(0,o.jsxDEV)(t.li,{children:\"Les bandes noires et bleues qui rappellent les premiers maillots du club et les d\\xE9buts professionnels dans les ann\\xE9es 1990\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-703d5ba7-6f0c-46a1-a369-4ca0452571b7.mdx\",lineNumber:26,columnNumber:1},this),`\n`,(0,o.jsxDEV)(t.li,{children:\"\\xAB\\xA01993\\xA0\\xBB l'ann\\xE9e de la saison inaugurale du club\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-703d5ba7-6f0c-46a1-a369-4ca0452571b7.mdx\",lineNumber:27,columnNumber:1},this),`\n`]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-703d5ba7-6f0c-46a1-a369-4ca0452571b7.mdx\",lineNumber:23,columnNumber:1},this),`\n`,(0,o.jsxDEV)(t.p,{children:[\"En plus du nom officiel \",(0,o.jsxDEV)(t.strong,{children:\"CF Montr\\xE9al\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-703d5ba7-6f0c-46a1-a369-4ca0452571b7.mdx\",lineNumber:29,columnNumber:25},this),\" bien en vue, l'ensemble de ces \\xE9l\\xE9ments conf\\xE8re au blason une identit\\xE9 riche de sens, ancr\\xE9e dans l'histoire et la culture du club tout en restant tourn\\xE9e vers l'avenir.\"]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-703d5ba7-6f0c-46a1-a369-4ca0452571b7.mdx\",lineNumber:29,columnNumber:1},this),`\n`,(0,o.jsxDEV)(t.h2,{children:\"Pourquoi le CF Montr\\xE9al a chang\\xE9 de logo en 2023\\xA0?\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-703d5ba7-6f0c-46a1-a369-4ca0452571b7.mdx\",lineNumber:33,columnNumber:1},this),`\n`,(0,o.jsxDEV)(t.p,{children:[\"Le changement de logo du CF Montr\\xE9al en 2023 s'explique avant tout par la volont\\xE9 de \",(0,o.jsxDEV)(t.strong,{children:\"corriger le tir\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-703d5ba7-6f0c-46a1-a369-4ca0452571b7.mdx\",lineNumber:34,columnNumber:86},this),\" apr\\xE8s le rebranding controvers\\xE9 de 2021.\"]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-703d5ba7-6f0c-46a1-a369-4ca0452571b7.mdx\",lineNumber:34,columnNumber:1},this),`\n`,(0,o.jsxDEV)(t.p,{children:\"Pour rappel, le 14 janvier 2021, la direction de l'\\xE9poque avait annonc\\xE9 que l'Impact de Montr\\xE9al changeait de nom et d'identit\\xE9 visuelle pour devenir \\xAB\\xA0Club de Foot Montr\\xE9al\\xA0\\xBB. Ce rebranding radical \\u2013 nouveau nom et logo en forme de flocon de neige \\u2013 avait suscit\\xE9 une vive opposition chez une partie des supporteurs et de la communaut\\xE9 du soccer qu\\xE9b\\xE9cois.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-703d5ba7-6f0c-46a1-a369-4ca0452571b7.mdx\",lineNumber:36,columnNumber:1},this),`\n`,(0,o.jsxDEV)(t.p,{children:\"Beaucoup de fans historiques ne se reconnaissaient pas dans ce nouvel embl\\xE8me, jug\\xE9 trop \\xE9loign\\xE9 de l'histoire du club et de l'identit\\xE9 montr\\xE9alaise.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-703d5ba7-6f0c-46a1-a369-4ca0452571b7.mdx\",lineNumber:38,columnNumber:1},this),`\n`,(0,o.jsxDEV)(t.p,{children:[\"Face \\xE0 cette r\\xE9action n\\xE9gative, les dirigeants ont d\\xE9cid\\xE9 d\\u2019\\xE9couter la voix des partisans. L\\u2019arriv\\xE9e d\\u2019un nouveau pr\\xE9sident en 2022, Gabriel Gervais (lui-m\\xEAme ancien joueur du club), a marqu\\xE9 un tournant. Gervais a reconnu publiquement qu\\u2019il \\xE9tait \",(0,o.jsxDEV)(t.strong,{children:\"difficile de s\\u2019identifier \\xE0 l\\u2019ancien logo\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-703d5ba7-6f0c-46a1-a369-4ca0452571b7.mdx\",lineNumber:40,columnNumber:249},this),\"\\xA0: \\xAB\\xA0La fleur de lys est o\\xF9\\u202F? La couleur bleue est o\\xF9\\u202F? Comment sommes-nous repr\\xE9sent\\xE9s\\u202F?\\xA0\\xBB s'est-il interrog\\xE9, soulignant l'absence des symboles traditionnels. Cet aveu a mis en lumi\\xE8re \",(0,o.jsxDEV)(t.strong,{children:\"l'erreur de branding\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-703d5ba7-6f0c-46a1-a369-4ca0452571b7.mdx\",lineNumber:40,columnNumber:481},this),\" commise en 2021. Ainsi, moins de deux ans apr\\xE8s la pr\\xE9c\\xE9dente refonte, le CF Montr\\xE9al a choisi de changer \\xE0 nouveau de logo afin de \",(0,o.jsxDEV)(t.strong,{children:\"r\\xE9aligner son identit\\xE9 visuelle sur son ADN d'origine\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-703d5ba7-6f0c-46a1-a369-4ca0452571b7.mdx\",lineNumber:40,columnNumber:638},this),\".\"]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-703d5ba7-6f0c-46a1-a369-4ca0452571b7.mdx\",lineNumber:40,columnNumber:1},this),`\n`,(0,o.jsxDEV)(h,{id:\"1521993350240473091\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-703d5ba7-6f0c-46a1-a369-4ca0452571b7.mdx\",lineNumber:42,columnNumber:1},this),`\n`,(0,o.jsxDEV)(t.p,{children:\"Concr\\xE8tement, ce retour en arri\\xE8re \\xE9tait une r\\xE9ponse directe aux attentes des supporteurs. \\xAB\\u202FWe heard them loud and clear\\u202F\\xBB \\u2013 \\xAB\\u202Fnous les avons entendus haut et fort\\u202F\\xBB, a d\\xE9clar\\xE9 le propri\\xE9taire Joey Saputo en \\xE9voquant les fans.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-703d5ba7-6f0c-46a1-a369-4ca0452571b7.mdx\",lineNumber:44,columnNumber:1},this),`\n`,(0,o.jsxDEV)(t.p,{children:[\"Le club a donc assum\\xE9 de \",(0,o.jsxDEV)(t.strong,{children:\"faire marche arri\\xE8re\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-703d5ba7-6f0c-46a1-a369-4ca0452571b7.mdx\",lineNumber:46,columnNumber:26},this),\" sur certains choix esth\\xE9tiques pour \",(0,o.jsxDEV)(t.strong,{children:\"r\\xE9parer le lien\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-703d5ba7-6f0c-46a1-a369-4ca0452571b7.mdx\",lineNumber:46,columnNumber:87},this),\" avec sa base de partisans. Ce changement de logo en 2023 a pour but de rassembler la communaut\\xE9 apr\\xE8s une p\\xE9riode de division et de controverse. C\\u2019est une d\\xE9marche humble de la part de l\\u2019organisation, qui admet que l\\u2019identit\\xE9 pr\\xE9c\\xE9dente n\\u2019a pas fait l\\u2019unanimit\\xE9 et qui tente de \\xAB\\xA0recoller les pots cass\\xE9s\\xA0\\xBB avec ses fans les plus fid\\xE8les.\"]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-703d5ba7-6f0c-46a1-a369-4ca0452571b7.mdx\",lineNumber:46,columnNumber:1},this),`\n`,(0,o.jsxDEV)(m,{src:\"/images/blog/logo-cf-montreal-2023/comparaison-logo-cf-montreal.webp\",alt:\"Comparaisons de l'\\xE9volution du logo CF Montr\\xE9al\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-703d5ba7-6f0c-46a1-a369-4ca0452571b7.mdx\",lineNumber:48,columnNumber:1},this),`\n`,(0,o.jsxDEV)(t.h2,{children:\"Des r\\xE9actions globalement positives\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-703d5ba7-6f0c-46a1-a369-4ca0452571b7.mdx\",lineNumber:53,columnNumber:1},this),`\n`,(0,o.jsxDEV)(t.p,{children:\"Le d\\xE9voilement du nouveau logo a re\\xE7u un bon accueil. Sur les r\\xE9seaux sociaux, dans les m\\xE9dias sp\\xE9cialis\\xE9s et au sein des groupes de supporters, le retour du bleu et de la fleur de lys a \\xE9t\\xE9 salu\\xE9.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-703d5ba7-6f0c-46a1-a369-4ca0452571b7.mdx\",lineNumber:55,columnNumber:1},this),`\n`,(0,o.jsxDEV)(t.p,{children:[\"Certains fans esp\\xE9raient n\\xE9anmoins un retour complet \\xE0 l\\u2019appellation \\xAB Impact \\xBB. Ce ne sera pas le cas. Gabriel Gervais l\\u2019a confirm\\xE9 : \",(0,o.jsxDEV)(t.strong,{children:\"le nom CF Montr\\xE9al reste\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-703d5ba7-6f0c-46a1-a369-4ca0452571b7.mdx\",lineNumber:57,columnNumber:136},this),\".\"]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-703d5ba7-6f0c-46a1-a369-4ca0452571b7.mdx\",lineNumber:57,columnNumber:1},this),`\n`,(0,o.jsxDEV)(t.p,{children:\"Toutefois, le club se montre ouvert \\xE0 honorer l\\u2019h\\xE9ritage de l\\u2019Impact autrement : nom symbolique dans le stade, r\\xE9f\\xE9rences visuelles, clins d\\u2019\\u0153il dans les communications.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-703d5ba7-6f0c-46a1-a369-4ca0452571b7.mdx\",lineNumber:59,columnNumber:1},this),`\n`,(0,o.jsxDEV)(t.p,{children:\"Dans l\\u2019ensemble, cette nouvelle identit\\xE9 est per\\xE7ue comme un compromis \\xE9quilibr\\xE9 entre l\\u2019ancien et le nouveau. Elle redonne au club une image plus align\\xE9e avec sa communaut\\xE9.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-703d5ba7-6f0c-46a1-a369-4ca0452571b7.mdx\",lineNumber:61,columnNumber:1},this),`\n`,(0,o.jsxDEV)(t.h2,{children:\"Conclusion\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-703d5ba7-6f0c-46a1-a369-4ca0452571b7.mdx\",lineNumber:63,columnNumber:1},this),`\n`,(0,o.jsxDEV)(t.p,{children:\"Ce changement d\\u2019identit\\xE9 s\\u2019inscrit possiblement dans une tendance plus large de la MLS o\\xF9 plusieurs clubs ont r\\xE9cemment proc\\xE9d\\xE9 \\xE0 des refontes de logo pour mieux refl\\xE9ter leur identit\\xE9 (on peut penser aux rebrandings du Columbus Crew, du Chicago Fire, etc.). Mais dans le cas de Montr\\xE9al, la d\\xE9marche a surtout \\xE9t\\xE9 guid\\xE9e par un besoin local : reconnecter le club \\xE0 sa base et \\xE0 son patrimoine.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-703d5ba7-6f0c-46a1-a369-4ca0452571b7.mdx\",lineNumber:65,columnNumber:1},this)]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-703d5ba7-6f0c-46a1-a369-4ca0452571b7.mdx\",lineNumber:1,columnNumber:1},this)}function _r(s={}){let{wrapper:t}=s.components||{};return t?(0,o.jsxDEV)(t,Object.assign({},s,{children:(0,o.jsxDEV)(ke,s,void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-703d5ba7-6f0c-46a1-a369-4ca0452571b7.mdx\"},this)}),void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-703d5ba7-6f0c-46a1-a369-4ca0452571b7.mdx\"},this):ke(s)}var vr=_r;function Te(s,t,m){throw new Error(\"Expected \"+(t?\"component\":\"object\")+\" `\"+s+\"` to be defined: you likely forgot to import, pass, or provide it.\"+(m?\"\\nIt\\u2019s referenced in your code at `\"+m+\"` in `/home/<USER>/git/kapreon-website-1/src/content/blog/fr/_mdx_bundler_entry_point-703d5ba7-6f0c-46a1-a369-4ca0452571b7.mdx`\":\"\"))}return gr(yr);})();\n/*! Bundled license information:\n\nreact/cjs/react-jsx-dev-runtime.development.js:\n  (**\n   * @license React\n   * react-jsx-dev-runtime.development.js\n   *\n   * Copyright (c) Facebook, Inc. and its affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *)\n*/\n;return Component;"}, "_id": "blog/fr/logo-cf-montreal-2023.mdx", "_raw": {"sourceFilePath": "blog/fr/logo-cf-montreal-2023.mdx", "sourceFileName": "logo-cf-montreal-2023.mdx", "sourceFileDir": "blog/fr", "contentType": "mdx", "flattenedPath": "blog/fr/logo-cf-montreal-2023"}, "type": "Post", "locale": "fr", "slug": "logo-cf-montreal-2023"}