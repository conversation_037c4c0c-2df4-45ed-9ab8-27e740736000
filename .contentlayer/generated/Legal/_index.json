[{"title": "Cookie Usage | Kapreon", "description": "Kapreon's cookie usage policy compliant with Canadian and Quebec laws.", "heroTitle": "<PERSON><PERSON>", "heroSubtitle": "Legal", "lastUpdated": "2025-02-11T00:00:00.000Z", "body": {"raw": "\nThis policy aims to inform users of our website about the use of connection tokens, commonly called \"cookies\", in accordance with Canadian and Quebec laws, including Quebec's Bill 25. By accessing our site, the user agrees to the use of cookies as described below.\n\n## What is a cookie?\n\nA cookie is a text file that the website sends to your browser and is then stored on a device. Cookies allow the site to recognize the user's device during subsequent visits, thus facilitating navigation and improving the user experience.\n\n## Types of cookies\n\n<PERSON><PERSON><PERSON><PERSON> uses analytical cookies to collect anonymous information about how visitors use <PERSON><PERSON><PERSON><PERSON>'s site. This data helps <PERSON><PERSON><PERSON><PERSON> improve the content and performance of the site.\n\n## Cookie retention period\n\nCookies are retained for a period proportional to their purpose.\n\nWhen first visiting <PERSON><PERSON><PERSON><PERSON>'s site, a banner informs the user about the use of cookies and invites the user to accept or refuse them. The user can modify their cookie preferences at any time by adjusting their browser settings.\n\n## User rights\n\nIn accordance with Quebec's Bill 25 and PIPEDA, the user has the following rights:\n\n- **Right of access**: The user may request access to personal information we hold about the user.\n- **Right of rectification**: The user may request correction of inaccurate or incomplete personal information.\n- **Right to withdraw consent**: The user may withdraw their consent to the use of their personal information, including cookies, at any time.\n\n## Policy modification\n\nKa<PERSON><PERSON><PERSON> reserves the right to modify this policy based on legislative or technical developments. <PERSON><PERSON><PERSON><PERSON> encourages users to regularly consult this page to stay informed of any changes.\n\nFor any questions or concerns regarding this policy or <PERSON><PERSON><PERSON><PERSON>'s personal information protection practices, please contact the agency at [<EMAIL>](mailto:<EMAIL>).\n\nThis policy is effective as of February 11, 2025.\n", "code": "var Component=(()=>{var sr=Object.create;var F=Object.defineProperty;var ur=Object.getOwnPropertyDescriptor;var cr=Object.getOwnPropertyNames;var fr=Object.getPrototypeOf,dr=Object.prototype.hasOwnProperty;var q=(c,t)=>()=>(t||c((t={exports:{}}).exports,t),t.exports),mr=(c,t)=>{for(var p in t)F(c,p,{get:t[p],enumerable:!0})},Ee=(c,t,p,y)=>{if(t&&typeof t==\"object\"||typeof t==\"function\")for(let _ of cr(t))!dr.call(c,_)&&_!==p&&F(c,_,{get:()=>t[_],enumerable:!(y=ur(t,_))||y.enumerable});return c};var br=(c,t,p)=>(p=c!=null?sr(fr(c)):{},Ee(t||!c||!c.__esModule?F(p,\"default\",{value:c,enumerable:!0}):p,c)),hr=c=>Ee(F({},\"__esModule\",{value:!0}),c);var xe=q((Er,we)=>{we.exports=React});var Re=q(z=>{\"use strict\";(function(){\"use strict\";var c=xe(),t=Symbol.for(\"react.element\"),p=Symbol.for(\"react.portal\"),y=Symbol.for(\"react.fragment\"),_=Symbol.for(\"react.strict_mode\"),G=Symbol.for(\"react.profiler\"),X=Symbol.for(\"react.provider\"),Q=Symbol.for(\"react.context\"),T=Symbol.for(\"react.forward_ref\"),A=Symbol.for(\"react.suspense\"),I=Symbol.for(\"react.suspense_list\"),C=Symbol.for(\"react.memo\"),W=Symbol.for(\"react.lazy\"),Ce=Symbol.for(\"react.offscreen\"),H=Symbol.iterator,Pe=\"@@iterator\";function Oe(e){if(e===null||typeof e!=\"object\")return null;var r=H&&e[H]||e[Pe];return typeof r==\"function\"?r:null}var w=c.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;function m(e){{for(var r=arguments.length,n=new Array(r>1?r-1:0),a=1;a<r;a++)n[a-1]=arguments[a];Se(\"error\",e,n)}}function Se(e,r,n){{var a=w.ReactDebugCurrentFrame,l=a.getStackAddendum();l!==\"\"&&(r+=\"%s\",n=n.concat([l]));var u=n.map(function(o){return String(o)});u.unshift(\"Warning: \"+r),Function.prototype.apply.call(console[e],console,u)}}var je=!1,De=!1,Fe=!1,Ae=!1,Ie=!1,J;J=Symbol.for(\"react.module.reference\");function We(e){return!!(typeof e==\"string\"||typeof e==\"function\"||e===y||e===G||Ie||e===_||e===A||e===I||Ae||e===Ce||je||De||Fe||typeof e==\"object\"&&e!==null&&(e.$$typeof===W||e.$$typeof===C||e.$$typeof===X||e.$$typeof===Q||e.$$typeof===T||e.$$typeof===J||e.getModuleId!==void 0))}function Ye(e,r,n){var a=e.displayName;if(a)return a;var l=r.displayName||r.name||\"\";return l!==\"\"?n+\"(\"+l+\")\":n}function Z(e){return e.displayName||\"Context\"}function g(e){if(e==null)return null;if(typeof e.tag==\"number\"&&m(\"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"),typeof e==\"function\")return e.displayName||e.name||null;if(typeof e==\"string\")return e;switch(e){case y:return\"Fragment\";case p:return\"Portal\";case G:return\"Profiler\";case _:return\"StrictMode\";case A:return\"Suspense\";case I:return\"SuspenseList\"}if(typeof e==\"object\")switch(e.$$typeof){case Q:var r=e;return Z(r)+\".Consumer\";case X:var n=e;return Z(n._context)+\".Provider\";case T:return Ye(e,e.render,\"ForwardRef\");case C:var a=e.displayName||null;return a!==null?a:g(e.type)||\"Memo\";case W:{var l=e,u=l._payload,o=l._init;try{return g(o(u))}catch{return null}}}return null}var E=Object.assign,N=0,ee,re,ne,te,ae,ie,oe;function le(){}le.__reactDisabledLog=!0;function $e(){{if(N===0){ee=console.log,re=console.info,ne=console.warn,te=console.error,ae=console.group,ie=console.groupCollapsed,oe=console.groupEnd;var e={configurable:!0,enumerable:!0,value:le,writable:!0};Object.defineProperties(console,{info:e,log:e,warn:e,error:e,group:e,groupCollapsed:e,groupEnd:e})}N++}}function Me(){{if(N--,N===0){var e={configurable:!0,enumerable:!0,writable:!0};Object.defineProperties(console,{log:E({},e,{value:ee}),info:E({},e,{value:re}),warn:E({},e,{value:ne}),error:E({},e,{value:te}),group:E({},e,{value:ae}),groupCollapsed:E({},e,{value:ie}),groupEnd:E({},e,{value:oe})})}N<0&&m(\"disabledDepth fell below zero. This is a bug in React. Please file an issue.\")}}var Y=w.ReactCurrentDispatcher,$;function P(e,r,n){{if($===void 0)try{throw Error()}catch(l){var a=l.stack.trim().match(/\\n( *(at )?)/);$=a&&a[1]||\"\"}return`\n`+$+e}}var M=!1,O;{var Ve=typeof WeakMap==\"function\"?WeakMap:Map;O=new Ve}function se(e,r){if(!e||M)return\"\";{var n=O.get(e);if(n!==void 0)return n}var a;M=!0;var l=Error.prepareStackTrace;Error.prepareStackTrace=void 0;var u;u=Y.current,Y.current=null,$e();try{if(r){var o=function(){throw Error()};if(Object.defineProperty(o.prototype,\"props\",{set:function(){throw Error()}}),typeof Reflect==\"object\"&&Reflect.construct){try{Reflect.construct(o,[])}catch(v){a=v}Reflect.construct(e,[],o)}else{try{o.call()}catch(v){a=v}e.call(o.prototype)}}else{try{throw Error()}catch(v){a=v}e()}}catch(v){if(v&&a&&typeof v.stack==\"string\"){for(var i=v.stack.split(`\n`),b=a.stack.split(`\n`),f=i.length-1,d=b.length-1;f>=1&&d>=0&&i[f]!==b[d];)d--;for(;f>=1&&d>=0;f--,d--)if(i[f]!==b[d]){if(f!==1||d!==1)do if(f--,d--,d<0||i[f]!==b[d]){var h=`\n`+i[f].replace(\" at new \",\" at \");return e.displayName&&h.includes(\"<anonymous>\")&&(h=h.replace(\"<anonymous>\",e.displayName)),typeof e==\"function\"&&O.set(e,h),h}while(f>=1&&d>=0);break}}}finally{M=!1,Y.current=u,Me(),Error.prepareStackTrace=l}var R=e?e.displayName||e.name:\"\",ye=R?P(R):\"\";return typeof e==\"function\"&&O.set(e,ye),ye}function Ue(e,r,n){return se(e,!1)}function Le(e){var r=e.prototype;return!!(r&&r.isReactComponent)}function S(e,r,n){if(e==null)return\"\";if(typeof e==\"function\")return se(e,Le(e));if(typeof e==\"string\")return P(e);switch(e){case A:return P(\"Suspense\");case I:return P(\"SuspenseList\")}if(typeof e==\"object\")switch(e.$$typeof){case T:return Ue(e.render);case C:return S(e.type,r,n);case W:{var a=e,l=a._payload,u=a._init;try{return S(u(l),r,n)}catch{}}}return\"\"}var j=Object.prototype.hasOwnProperty,ue={},ce=w.ReactDebugCurrentFrame;function D(e){if(e){var r=e._owner,n=S(e.type,e._source,r?r.type:null);ce.setExtraStackFrame(n)}else ce.setExtraStackFrame(null)}function Ke(e,r,n,a,l){{var u=Function.call.bind(j);for(var o in e)if(u(e,o)){var i=void 0;try{if(typeof e[o]!=\"function\"){var b=Error((a||\"React class\")+\": \"+n+\" type `\"+o+\"` is invalid; it must be a function, usually from the `prop-types` package, but received `\"+typeof e[o]+\"`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.\");throw b.name=\"Invariant Violation\",b}i=e[o](r,o,a,n,null,\"SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED\")}catch(f){i=f}i&&!(i instanceof Error)&&(D(l),m(\"%s: type specification of %s `%s` is invalid; the type checker function must return `null` or an `Error` but returned a %s. You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument).\",a||\"React class\",n,o,typeof i),D(null)),i instanceof Error&&!(i.message in ue)&&(ue[i.message]=!0,D(l),m(\"Failed %s type: %s\",n,i.message),D(null))}}}var Be=Array.isArray;function V(e){return Be(e)}function qe(e){{var r=typeof Symbol==\"function\"&&Symbol.toStringTag,n=r&&e[Symbol.toStringTag]||e.constructor.name||\"Object\";return n}}function ze(e){try{return fe(e),!1}catch{return!0}}function fe(e){return\"\"+e}function de(e){if(ze(e))return m(\"The provided key is an unsupported type %s. This value must be coerced to a string before before using it here.\",qe(e)),fe(e)}var k=w.ReactCurrentOwner,Ge={key:!0,ref:!0,__self:!0,__source:!0},me,be,U;U={};function Xe(e){if(j.call(e,\"ref\")){var r=Object.getOwnPropertyDescriptor(e,\"ref\").get;if(r&&r.isReactWarning)return!1}return e.ref!==void 0}function Qe(e){if(j.call(e,\"key\")){var r=Object.getOwnPropertyDescriptor(e,\"key\").get;if(r&&r.isReactWarning)return!1}return e.key!==void 0}function He(e,r){if(typeof e.ref==\"string\"&&k.current&&r&&k.current.stateNode!==r){var n=g(k.current.type);U[n]||(m('Component \"%s\" contains the string ref \"%s\". Support for string refs will be removed in a future major release. This case cannot be automatically converted to an arrow function. We ask you to manually fix this case by using useRef() or createRef() instead. Learn more about using refs safely here: https://reactjs.org/link/strict-mode-string-ref',g(k.current.type),e.ref),U[n]=!0)}}function Je(e,r){{var n=function(){me||(me=!0,m(\"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)\",r))};n.isReactWarning=!0,Object.defineProperty(e,\"key\",{get:n,configurable:!0})}}function Ze(e,r){{var n=function(){be||(be=!0,m(\"%s: `ref` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)\",r))};n.isReactWarning=!0,Object.defineProperty(e,\"ref\",{get:n,configurable:!0})}}var er=function(e,r,n,a,l,u,o){var i={$$typeof:t,type:e,key:r,ref:n,props:o,_owner:u};return i._store={},Object.defineProperty(i._store,\"validated\",{configurable:!1,enumerable:!1,writable:!0,value:!1}),Object.defineProperty(i,\"_self\",{configurable:!1,enumerable:!1,writable:!1,value:a}),Object.defineProperty(i,\"_source\",{configurable:!1,enumerable:!1,writable:!1,value:l}),Object.freeze&&(Object.freeze(i.props),Object.freeze(i)),i};function rr(e,r,n,a,l){{var u,o={},i=null,b=null;n!==void 0&&(de(n),i=\"\"+n),Qe(r)&&(de(r.key),i=\"\"+r.key),Xe(r)&&(b=r.ref,He(r,l));for(u in r)j.call(r,u)&&!Ge.hasOwnProperty(u)&&(o[u]=r[u]);if(e&&e.defaultProps){var f=e.defaultProps;for(u in f)o[u]===void 0&&(o[u]=f[u])}if(i||b){var d=typeof e==\"function\"?e.displayName||e.name||\"Unknown\":e;i&&Je(o,d),b&&Ze(o,d)}return er(e,i,b,l,a,k.current,o)}}var L=w.ReactCurrentOwner,he=w.ReactDebugCurrentFrame;function x(e){if(e){var r=e._owner,n=S(e.type,e._source,r?r.type:null);he.setExtraStackFrame(n)}else he.setExtraStackFrame(null)}var K;K=!1;function B(e){return typeof e==\"object\"&&e!==null&&e.$$typeof===t}function pe(){{if(L.current){var e=g(L.current.type);if(e)return`\n\nCheck the render method of \\``+e+\"`.\"}return\"\"}}function nr(e){{if(e!==void 0){var r=e.fileName.replace(/^.*[\\\\\\/]/,\"\"),n=e.lineNumber;return`\n\nCheck your code at `+r+\":\"+n+\".\"}return\"\"}}var ge={};function tr(e){{var r=pe();if(!r){var n=typeof e==\"string\"?e:e.displayName||e.name;n&&(r=`\n\nCheck the top-level render call using <`+n+\">.\")}return r}}function ve(e,r){{if(!e._store||e._store.validated||e.key!=null)return;e._store.validated=!0;var n=tr(r);if(ge[n])return;ge[n]=!0;var a=\"\";e&&e._owner&&e._owner!==L.current&&(a=\" It was passed a child from \"+g(e._owner.type)+\".\"),x(e),m('Each child in a list should have a unique \"key\" prop.%s%s See https://reactjs.org/link/warning-keys for more information.',n,a),x(null)}}function _e(e,r){{if(typeof e!=\"object\")return;if(V(e))for(var n=0;n<e.length;n++){var a=e[n];B(a)&&ve(a,r)}else if(B(e))e._store&&(e._store.validated=!0);else if(e){var l=Oe(e);if(typeof l==\"function\"&&l!==e.entries)for(var u=l.call(e),o;!(o=u.next()).done;)B(o.value)&&ve(o.value,r)}}}function ar(e){{var r=e.type;if(r==null||typeof r==\"string\")return;var n;if(typeof r==\"function\")n=r.propTypes;else if(typeof r==\"object\"&&(r.$$typeof===T||r.$$typeof===C))n=r.propTypes;else return;if(n){var a=g(r);Ke(n,e.props,\"prop\",a,e)}else if(r.PropTypes!==void 0&&!K){K=!0;var l=g(r);m(\"Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?\",l||\"Unknown\")}typeof r.getDefaultProps==\"function\"&&!r.getDefaultProps.isReactClassApproved&&m(\"getDefaultProps is only used on classic React.createClass definitions. Use a static property named `defaultProps` instead.\")}}function ir(e){{for(var r=Object.keys(e.props),n=0;n<r.length;n++){var a=r[n];if(a!==\"children\"&&a!==\"key\"){x(e),m(\"Invalid prop `%s` supplied to `React.Fragment`. React.Fragment can only have `key` and `children` props.\",a),x(null);break}}e.ref!==null&&(x(e),m(\"Invalid attribute `ref` supplied to `React.Fragment`.\"),x(null))}}function or(e,r,n,a,l,u){{var o=We(e);if(!o){var i=\"\";(e===void 0||typeof e==\"object\"&&e!==null&&Object.keys(e).length===0)&&(i+=\" You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.\");var b=nr(l);b?i+=b:i+=pe();var f;e===null?f=\"null\":V(e)?f=\"array\":e!==void 0&&e.$$typeof===t?(f=\"<\"+(g(e.type)||\"Unknown\")+\" />\",i=\" Did you accidentally export a JSX literal instead of a component?\"):f=typeof e,m(\"React.jsx: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: %s.%s\",f,i)}var d=rr(e,r,n,l,u);if(d==null)return d;if(o){var h=r.children;if(h!==void 0)if(a)if(V(h)){for(var R=0;R<h.length;R++)_e(h[R],e);Object.freeze&&Object.freeze(h)}else m(\"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\");else _e(h,e)}return e===y?ir(d):ar(d),d}}var lr=or;z.Fragment=y,z.jsxDEV=lr})()});var ke=q((xr,Ne)=>{\"use strict\";Ne.exports=Re()});var _r={};mr(_r,{default:()=>vr,frontmatter:()=>pr});var s=br(ke()),pr={title:\"Cookie Usage | Kapreon\",description:\"Kapreon's cookie usage policy compliant with Canadian and Quebec laws.\",heroTitle:\"Cookie Usage\",heroSubtitle:\"Legal\",lastUpdated:new Date(1739232e6)};function Te(c){let t=Object.assign({p:\"p\",h2:\"h2\",ul:\"ul\",li:\"li\",strong:\"strong\",a:\"a\"},c.components);return(0,s.jsxDEV)(s.Fragment,{children:[(0,s.jsxDEV)(t.p,{children:`This policy aims to inform users of our website about the use of connection tokens, commonly called \"cookies\", in accordance with Canadian and Quebec laws, including Quebec's Bill 25. By accessing our site, the user agrees to the use of cookies as described below.`},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-3c85fe02-d1b8-4129-acba-dfe65f774308.mdx\",lineNumber:9,columnNumber:1},this),`\n`,(0,s.jsxDEV)(t.h2,{children:\"What is a cookie?\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-3c85fe02-d1b8-4129-acba-dfe65f774308.mdx\",lineNumber:11,columnNumber:1},this),`\n`,(0,s.jsxDEV)(t.p,{children:\"A cookie is a text file that the website sends to your browser and is then stored on a device. Cookies allow the site to recognize the user's device during subsequent visits, thus facilitating navigation and improving the user experience.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-3c85fe02-d1b8-4129-acba-dfe65f774308.mdx\",lineNumber:13,columnNumber:1},this),`\n`,(0,s.jsxDEV)(t.h2,{children:\"Types of cookies\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-3c85fe02-d1b8-4129-acba-dfe65f774308.mdx\",lineNumber:15,columnNumber:1},this),`\n`,(0,s.jsxDEV)(t.p,{children:\"Kapreon uses analytical cookies to collect anonymous information about how visitors use Kapreon's site. This data helps Kapreon improve the content and performance of the site.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-3c85fe02-d1b8-4129-acba-dfe65f774308.mdx\",lineNumber:17,columnNumber:1},this),`\n`,(0,s.jsxDEV)(t.h2,{children:\"Cookie retention period\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-3c85fe02-d1b8-4129-acba-dfe65f774308.mdx\",lineNumber:19,columnNumber:1},this),`\n`,(0,s.jsxDEV)(t.p,{children:\"Cookies are retained for a period proportional to their purpose.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-3c85fe02-d1b8-4129-acba-dfe65f774308.mdx\",lineNumber:21,columnNumber:1},this),`\n`,(0,s.jsxDEV)(t.p,{children:\"When first visiting Kapreon's site, a banner informs the user about the use of cookies and invites the user to accept or refuse them. The user can modify their cookie preferences at any time by adjusting their browser settings.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-3c85fe02-d1b8-4129-acba-dfe65f774308.mdx\",lineNumber:23,columnNumber:1},this),`\n`,(0,s.jsxDEV)(t.h2,{children:\"User rights\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-3c85fe02-d1b8-4129-acba-dfe65f774308.mdx\",lineNumber:25,columnNumber:1},this),`\n`,(0,s.jsxDEV)(t.p,{children:\"In accordance with Quebec's Bill 25 and PIPEDA, the user has the following rights:\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-3c85fe02-d1b8-4129-acba-dfe65f774308.mdx\",lineNumber:27,columnNumber:1},this),`\n`,(0,s.jsxDEV)(t.ul,{children:[`\n`,(0,s.jsxDEV)(t.li,{children:[(0,s.jsxDEV)(t.strong,{children:\"Right of access\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-3c85fe02-d1b8-4129-acba-dfe65f774308.mdx\",lineNumber:29,columnNumber:3},this),\": The user may request access to personal information we hold about the user.\"]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-3c85fe02-d1b8-4129-acba-dfe65f774308.mdx\",lineNumber:29,columnNumber:1},this),`\n`,(0,s.jsxDEV)(t.li,{children:[(0,s.jsxDEV)(t.strong,{children:\"Right of rectification\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-3c85fe02-d1b8-4129-acba-dfe65f774308.mdx\",lineNumber:30,columnNumber:3},this),\": The user may request correction of inaccurate or incomplete personal information.\"]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-3c85fe02-d1b8-4129-acba-dfe65f774308.mdx\",lineNumber:30,columnNumber:1},this),`\n`,(0,s.jsxDEV)(t.li,{children:[(0,s.jsxDEV)(t.strong,{children:\"Right to withdraw consent\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-3c85fe02-d1b8-4129-acba-dfe65f774308.mdx\",lineNumber:31,columnNumber:3},this),\": The user may withdraw their consent to the use of their personal information, including cookies, at any time.\"]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-3c85fe02-d1b8-4129-acba-dfe65f774308.mdx\",lineNumber:31,columnNumber:1},this),`\n`]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-3c85fe02-d1b8-4129-acba-dfe65f774308.mdx\",lineNumber:29,columnNumber:1},this),`\n`,(0,s.jsxDEV)(t.h2,{children:\"Policy modification\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-3c85fe02-d1b8-4129-acba-dfe65f774308.mdx\",lineNumber:33,columnNumber:1},this),`\n`,(0,s.jsxDEV)(t.p,{children:\"Kapreon reserves the right to modify this policy based on legislative or technical developments. Kapreon encourages users to regularly consult this page to stay informed of any changes.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-3c85fe02-d1b8-4129-acba-dfe65f774308.mdx\",lineNumber:35,columnNumber:1},this),`\n`,(0,s.jsxDEV)(t.p,{children:[\"For any questions or concerns regarding this policy or Kapreon's personal information protection practices, please contact the agency at \",(0,s.jsxDEV)(t.a,{href:\"mailto:<EMAIL>\",children:\"<EMAIL>\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-3c85fe02-d1b8-4129-acba-dfe65f774308.mdx\",lineNumber:37,columnNumber:138},this),\".\"]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-3c85fe02-d1b8-4129-acba-dfe65f774308.mdx\",lineNumber:37,columnNumber:1},this),`\n`,(0,s.jsxDEV)(t.p,{children:\"This policy is effective as of February 11, 2025.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-3c85fe02-d1b8-4129-acba-dfe65f774308.mdx\",lineNumber:39,columnNumber:1},this)]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-3c85fe02-d1b8-4129-acba-dfe65f774308.mdx\",lineNumber:1,columnNumber:1},this)}function gr(c={}){let{wrapper:t}=c.components||{};return t?(0,s.jsxDEV)(t,Object.assign({},c,{children:(0,s.jsxDEV)(Te,c,void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-3c85fe02-d1b8-4129-acba-dfe65f774308.mdx\"},this)}),void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-3c85fe02-d1b8-4129-acba-dfe65f774308.mdx\"},this):Te(c)}var vr=gr;return hr(_r);})();\n/*! Bundled license information:\n\nreact/cjs/react-jsx-dev-runtime.development.js:\n  (**\n   * @license React\n   * react-jsx-dev-runtime.development.js\n   *\n   * Copyright (c) Facebook, Inc. and its affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *)\n*/\n;return Component;"}, "_id": "legal/en/cookies.mdx", "_raw": {"sourceFilePath": "legal/en/cookies.mdx", "sourceFileName": "cookies.mdx", "sourceFileDir": "legal/en", "contentType": "mdx", "flattenedPath": "legal/en/cookies"}, "type": "Legal", "locale": "en", "slug": "cookies"}, {"title": "Terms and Conditions | Kapreon", "description": "Terms and conditions of use for Kapreon's website.", "heroTitle": "Terms and Conditions", "heroSubtitle": "Legal", "lastUpdated": "2025-02-11T00:00:00.000Z", "body": {"raw": "\nKapreon is a company incorporated in Canada. The information contained on this website is the property of <PERSON><PERSON><PERSON><PERSON> and is protected by copyright and trademark laws in force in Canada and Quebec.\n\n## Publication Manager\n\n- Kapreon Inc.\n- Quebec Enterprise Number (NEQ): **********\n- Head office: 100-50 Rue St-Charles O., Longueuil, J4H 1C6, Quebec, Canada\n- Phone number: [+****************](tel:+14388033053)\n- Email address: [<EMAIL>](mailto:<EMAIL>)\n\n## Liability\n\nThe information contained on this website is provided for informational purposes and is not intended to be exhaustive. Kapreon cannot guarantee the accuracy, completeness or timeliness of information published on its website.\n\nConsequently, Kapreon disclaims all liability:\n\n- for any inaccuracy, inexactitude or omission relating to information available on the website;\n- for all damages resulting from fraudulent intrusion by a third party that led to modification of information made available on the website;\n- for all damages, direct or indirect, caused due to access to or use of the site.\n\nKapreon does not guarantee uninterrupted access to the site. Kapreon reserves the right to suspend access for maintenance or any other technical reason.\n\nExternal hyperlinks present on the site cannot engage <PERSON><PERSON><PERSON><PERSON>'s liability.\n\n## Intellectual Property\n\nThe site may contain trademarks, logos, images, videos, texts and other content belonging to <PERSON><PERSON><PERSON><PERSON> or third parties. Ka<PERSON>reon holds exclusive rights to certain content, while others are the property of partners, clients or third parties and are used with their authorization or as part of completed projects, fictional or not.\n\nAny reproduction, distribution, modification or reuse, in whole or in part, of elements present on this site is prohibited without express authorization from rights holders. Use of site content, including third-party images and logos, does not confer any property or exploitation rights to users.\n\n## Personal Data Protection\n\nPersonal data collected on the site is processed in accordance with Canada's Personal Information Protection and Electronic Documents Act (PIPEDA) and Quebec's Act respecting the protection of personal information in the private sector.\n\n### Processing Purpose\n\nData collected is used exclusively to respond to information requests and manage registrations and services offered on the site.\n\n### Data Recipients\n\nData is intended for internal use exclusively and will under no circumstances be transferred, sold or rented to third parties.\n\n### Data Security\n\nKapreon implements strict security measures to protect personal information against unauthorized access, loss or modification. Data is stored on secure and encrypted servers.\n\n### Retention Period\n\nData is retained as long as necessary for intended purposes, in accordance with applicable legislation.\n\n### User Rights\n\nIn accordance with PIPEDA and Quebec legislation, you have the right to access, rectify and delete data concerning you. You can exercise this right by contacting us at [<EMAIL>](mailto:<EMAIL>).\n\n### Cookie Policy\n\nKapreon uses cookies to improve user experience and analyze site audience. You can consult our [Cookie Policy](/legal/cookies) for more information on their use and managing your preferences.\n\n### Applicable Law and Competent Jurisdiction\n\nThis site is subject to Canadian and Quebec laws. In case of dispute, the competent jurisdiction is that of Quebec courts, Montreal district.\n\nThese legal notices may be modified at any time. We therefore invite you to consult them regularly.\n\nLast update: February 11, 2025\n", "code": "var Component=(()=>{var sn=Object.create;var A=Object.defineProperty;var un=Object.getOwnPropertyDescriptor;var fn=Object.getOwnPropertyNames;var dn=Object.getPrototypeOf,mn=Object.prototype.hasOwnProperty;var B=(u,t)=>()=>(t||u((t={exports:{}}).exports,t),t.exports),bn=(u,t)=>{for(var p in t)A(u,p,{get:t[p],enumerable:!0})},Ne=(u,t,p,y)=>{if(t&&typeof t==\"object\"||typeof t==\"function\")for(let v of fn(t))!mn.call(u,v)&&v!==p&&A(u,v,{get:()=>t[v],enumerable:!(y=un(t,v))||y.enumerable});return u};var hn=(u,t,p)=>(p=u!=null?sn(dn(u)):{},Ne(t||!u||!u.__esModule?A(p,\"default\",{value:u,enumerable:!0}):p,u)),pn=u=>Ne(A({},\"__esModule\",{value:!0}),u);var we=B((xn,xe)=>{xe.exports=React});var Ee=B(Q=>{\"use strict\";(function(){\"use strict\";var u=we(),t=Symbol.for(\"react.element\"),p=Symbol.for(\"react.portal\"),y=Symbol.for(\"react.fragment\"),v=Symbol.for(\"react.strict_mode\"),q=Symbol.for(\"react.profiler\"),G=Symbol.for(\"react.provider\"),H=Symbol.for(\"react.context\"),T=Symbol.for(\"react.forward_ref\"),F=Symbol.for(\"react.suspense\"),I=Symbol.for(\"react.suspense_list\"),C=Symbol.for(\"react.memo\"),Y=Symbol.for(\"react.lazy\"),Ce=Symbol.for(\"react.offscreen\"),X=Symbol.iterator,Pe=\"@@iterator\";function Oe(e){if(e===null||typeof e!=\"object\")return null;var n=X&&e[X]||e[Pe];return typeof n==\"function\"?n:null}var x=u.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;function m(e){{for(var n=arguments.length,r=new Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];Se(\"error\",e,r)}}function Se(e,n,r){{var i=x.ReactDebugCurrentFrame,c=i.getStackAddendum();c!==\"\"&&(n+=\"%s\",r=r.concat([c]));var s=r.map(function(l){return String(l)});s.unshift(\"Warning: \"+n),Function.prototype.apply.call(console[e],console,s)}}var je=!1,De=!1,Ae=!1,Fe=!1,Ie=!1,J;J=Symbol.for(\"react.module.reference\");function Ye(e){return!!(typeof e==\"string\"||typeof e==\"function\"||e===y||e===q||Ie||e===v||e===F||e===I||Fe||e===Ce||je||De||Ae||typeof e==\"object\"&&e!==null&&(e.$$typeof===Y||e.$$typeof===C||e.$$typeof===G||e.$$typeof===H||e.$$typeof===T||e.$$typeof===J||e.getModuleId!==void 0))}function We(e,n,r){var i=e.displayName;if(i)return i;var c=n.displayName||n.name||\"\";return c!==\"\"?r+\"(\"+c+\")\":r}function Z(e){return e.displayName||\"Context\"}function _(e){if(e==null)return null;if(typeof e.tag==\"number\"&&m(\"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"),typeof e==\"function\")return e.displayName||e.name||null;if(typeof e==\"string\")return e;switch(e){case y:return\"Fragment\";case p:return\"Portal\";case q:return\"Profiler\";case v:return\"StrictMode\";case F:return\"Suspense\";case I:return\"SuspenseList\"}if(typeof e==\"object\")switch(e.$$typeof){case H:var n=e;return Z(n)+\".Consumer\";case G:var r=e;return Z(r._context)+\".Provider\";case T:return We(e,e.render,\"ForwardRef\");case C:var i=e.displayName||null;return i!==null?i:_(e.type)||\"Memo\";case Y:{var c=e,s=c._payload,l=c._init;try{return _(l(s))}catch{return null}}}return null}var N=Object.assign,k=0,ee,ne,re,te,ae,ie,oe;function le(){}le.__reactDisabledLog=!0;function $e(){{if(k===0){ee=console.log,ne=console.info,re=console.warn,te=console.error,ae=console.group,ie=console.groupCollapsed,oe=console.groupEnd;var e={configurable:!0,enumerable:!0,value:le,writable:!0};Object.defineProperties(console,{info:e,log:e,warn:e,error:e,group:e,groupCollapsed:e,groupEnd:e})}k++}}function Le(){{if(k--,k===0){var e={configurable:!0,enumerable:!0,writable:!0};Object.defineProperties(console,{log:N({},e,{value:ee}),info:N({},e,{value:ne}),warn:N({},e,{value:re}),error:N({},e,{value:te}),group:N({},e,{value:ae}),groupCollapsed:N({},e,{value:ie}),groupEnd:N({},e,{value:oe})})}k<0&&m(\"disabledDepth fell below zero. This is a bug in React. Please file an issue.\")}}var W=x.ReactCurrentDispatcher,$;function P(e,n,r){{if($===void 0)try{throw Error()}catch(c){var i=c.stack.trim().match(/\\n( *(at )?)/);$=i&&i[1]||\"\"}return`\n`+$+e}}var L=!1,O;{var Me=typeof WeakMap==\"function\"?WeakMap:Map;O=new Me}function ce(e,n){if(!e||L)return\"\";{var r=O.get(e);if(r!==void 0)return r}var i;L=!0;var c=Error.prepareStackTrace;Error.prepareStackTrace=void 0;var s;s=W.current,W.current=null,$e();try{if(n){var l=function(){throw Error()};if(Object.defineProperty(l.prototype,\"props\",{set:function(){throw Error()}}),typeof Reflect==\"object\"&&Reflect.construct){try{Reflect.construct(l,[])}catch(g){i=g}Reflect.construct(e,[],l)}else{try{l.call()}catch(g){i=g}e.call(l.prototype)}}else{try{throw Error()}catch(g){i=g}e()}}catch(g){if(g&&i&&typeof g.stack==\"string\"){for(var o=g.stack.split(`\n`),b=i.stack.split(`\n`),f=o.length-1,d=b.length-1;f>=1&&d>=0&&o[f]!==b[d];)d--;for(;f>=1&&d>=0;f--,d--)if(o[f]!==b[d]){if(f!==1||d!==1)do if(f--,d--,d<0||o[f]!==b[d]){var h=`\n`+o[f].replace(\" at new \",\" at \");return e.displayName&&h.includes(\"<anonymous>\")&&(h=h.replace(\"<anonymous>\",e.displayName)),typeof e==\"function\"&&O.set(e,h),h}while(f>=1&&d>=0);break}}}finally{L=!1,W.current=s,Le(),Error.prepareStackTrace=c}var E=e?e.displayName||e.name:\"\",ye=E?P(E):\"\";return typeof e==\"function\"&&O.set(e,ye),ye}function Ve(e,n,r){return ce(e,!1)}function Ke(e){var n=e.prototype;return!!(n&&n.isReactComponent)}function S(e,n,r){if(e==null)return\"\";if(typeof e==\"function\")return ce(e,Ke(e));if(typeof e==\"string\")return P(e);switch(e){case F:return P(\"Suspense\");case I:return P(\"SuspenseList\")}if(typeof e==\"object\")switch(e.$$typeof){case T:return Ve(e.render);case C:return S(e.type,n,r);case Y:{var i=e,c=i._payload,s=i._init;try{return S(s(c),n,r)}catch{}}}return\"\"}var j=Object.prototype.hasOwnProperty,se={},ue=x.ReactDebugCurrentFrame;function D(e){if(e){var n=e._owner,r=S(e.type,e._source,n?n.type:null);ue.setExtraStackFrame(r)}else ue.setExtraStackFrame(null)}function Ue(e,n,r,i,c){{var s=Function.call.bind(j);for(var l in e)if(s(e,l)){var o=void 0;try{if(typeof e[l]!=\"function\"){var b=Error((i||\"React class\")+\": \"+r+\" type `\"+l+\"` is invalid; it must be a function, usually from the `prop-types` package, but received `\"+typeof e[l]+\"`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.\");throw b.name=\"Invariant Violation\",b}o=e[l](n,l,i,r,null,\"SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED\")}catch(f){o=f}o&&!(o instanceof Error)&&(D(c),m(\"%s: type specification of %s `%s` is invalid; the type checker function must return `null` or an `Error` but returned a %s. You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument).\",i||\"React class\",r,l,typeof o),D(null)),o instanceof Error&&!(o.message in se)&&(se[o.message]=!0,D(c),m(\"Failed %s type: %s\",r,o.message),D(null))}}}var ze=Array.isArray;function M(e){return ze(e)}function Be(e){{var n=typeof Symbol==\"function\"&&Symbol.toStringTag,r=n&&e[Symbol.toStringTag]||e.constructor.name||\"Object\";return r}}function Qe(e){try{return fe(e),!1}catch{return!0}}function fe(e){return\"\"+e}function de(e){if(Qe(e))return m(\"The provided key is an unsupported type %s. This value must be coerced to a string before before using it here.\",Be(e)),fe(e)}var R=x.ReactCurrentOwner,qe={key:!0,ref:!0,__self:!0,__source:!0},me,be,V;V={};function Ge(e){if(j.call(e,\"ref\")){var n=Object.getOwnPropertyDescriptor(e,\"ref\").get;if(n&&n.isReactWarning)return!1}return e.ref!==void 0}function He(e){if(j.call(e,\"key\")){var n=Object.getOwnPropertyDescriptor(e,\"key\").get;if(n&&n.isReactWarning)return!1}return e.key!==void 0}function Xe(e,n){if(typeof e.ref==\"string\"&&R.current&&n&&R.current.stateNode!==n){var r=_(R.current.type);V[r]||(m('Component \"%s\" contains the string ref \"%s\". Support for string refs will be removed in a future major release. This case cannot be automatically converted to an arrow function. We ask you to manually fix this case by using useRef() or createRef() instead. Learn more about using refs safely here: https://reactjs.org/link/strict-mode-string-ref',_(R.current.type),e.ref),V[r]=!0)}}function Je(e,n){{var r=function(){me||(me=!0,m(\"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)\",n))};r.isReactWarning=!0,Object.defineProperty(e,\"key\",{get:r,configurable:!0})}}function Ze(e,n){{var r=function(){be||(be=!0,m(\"%s: `ref` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)\",n))};r.isReactWarning=!0,Object.defineProperty(e,\"ref\",{get:r,configurable:!0})}}var en=function(e,n,r,i,c,s,l){var o={$$typeof:t,type:e,key:n,ref:r,props:l,_owner:s};return o._store={},Object.defineProperty(o._store,\"validated\",{configurable:!1,enumerable:!1,writable:!0,value:!1}),Object.defineProperty(o,\"_self\",{configurable:!1,enumerable:!1,writable:!1,value:i}),Object.defineProperty(o,\"_source\",{configurable:!1,enumerable:!1,writable:!1,value:c}),Object.freeze&&(Object.freeze(o.props),Object.freeze(o)),o};function nn(e,n,r,i,c){{var s,l={},o=null,b=null;r!==void 0&&(de(r),o=\"\"+r),He(n)&&(de(n.key),o=\"\"+n.key),Ge(n)&&(b=n.ref,Xe(n,c));for(s in n)j.call(n,s)&&!qe.hasOwnProperty(s)&&(l[s]=n[s]);if(e&&e.defaultProps){var f=e.defaultProps;for(s in f)l[s]===void 0&&(l[s]=f[s])}if(o||b){var d=typeof e==\"function\"?e.displayName||e.name||\"Unknown\":e;o&&Je(l,d),b&&Ze(l,d)}return en(e,o,b,c,i,R.current,l)}}var K=x.ReactCurrentOwner,he=x.ReactDebugCurrentFrame;function w(e){if(e){var n=e._owner,r=S(e.type,e._source,n?n.type:null);he.setExtraStackFrame(r)}else he.setExtraStackFrame(null)}var U;U=!1;function z(e){return typeof e==\"object\"&&e!==null&&e.$$typeof===t}function pe(){{if(K.current){var e=_(K.current.type);if(e)return`\n\nCheck the render method of \\``+e+\"`.\"}return\"\"}}function rn(e){{if(e!==void 0){var n=e.fileName.replace(/^.*[\\\\\\/]/,\"\"),r=e.lineNumber;return`\n\nCheck your code at `+n+\":\"+r+\".\"}return\"\"}}var _e={};function tn(e){{var n=pe();if(!n){var r=typeof e==\"string\"?e:e.displayName||e.name;r&&(n=`\n\nCheck the top-level render call using <`+r+\">.\")}return n}}function ge(e,n){{if(!e._store||e._store.validated||e.key!=null)return;e._store.validated=!0;var r=tn(n);if(_e[r])return;_e[r]=!0;var i=\"\";e&&e._owner&&e._owner!==K.current&&(i=\" It was passed a child from \"+_(e._owner.type)+\".\"),w(e),m('Each child in a list should have a unique \"key\" prop.%s%s See https://reactjs.org/link/warning-keys for more information.',r,i),w(null)}}function ve(e,n){{if(typeof e!=\"object\")return;if(M(e))for(var r=0;r<e.length;r++){var i=e[r];z(i)&&ge(i,n)}else if(z(e))e._store&&(e._store.validated=!0);else if(e){var c=Oe(e);if(typeof c==\"function\"&&c!==e.entries)for(var s=c.call(e),l;!(l=s.next()).done;)z(l.value)&&ge(l.value,n)}}}function an(e){{var n=e.type;if(n==null||typeof n==\"string\")return;var r;if(typeof n==\"function\")r=n.propTypes;else if(typeof n==\"object\"&&(n.$$typeof===T||n.$$typeof===C))r=n.propTypes;else return;if(r){var i=_(n);Ue(r,e.props,\"prop\",i,e)}else if(n.PropTypes!==void 0&&!U){U=!0;var c=_(n);m(\"Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?\",c||\"Unknown\")}typeof n.getDefaultProps==\"function\"&&!n.getDefaultProps.isReactClassApproved&&m(\"getDefaultProps is only used on classic React.createClass definitions. Use a static property named `defaultProps` instead.\")}}function on(e){{for(var n=Object.keys(e.props),r=0;r<n.length;r++){var i=n[r];if(i!==\"children\"&&i!==\"key\"){w(e),m(\"Invalid prop `%s` supplied to `React.Fragment`. React.Fragment can only have `key` and `children` props.\",i),w(null);break}}e.ref!==null&&(w(e),m(\"Invalid attribute `ref` supplied to `React.Fragment`.\"),w(null))}}function ln(e,n,r,i,c,s){{var l=Ye(e);if(!l){var o=\"\";(e===void 0||typeof e==\"object\"&&e!==null&&Object.keys(e).length===0)&&(o+=\" You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.\");var b=rn(c);b?o+=b:o+=pe();var f;e===null?f=\"null\":M(e)?f=\"array\":e!==void 0&&e.$$typeof===t?(f=\"<\"+(_(e.type)||\"Unknown\")+\" />\",o=\" Did you accidentally export a JSX literal instead of a component?\"):f=typeof e,m(\"React.jsx: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: %s.%s\",f,o)}var d=nn(e,n,r,c,s);if(d==null)return d;if(l){var h=n.children;if(h!==void 0)if(i)if(M(h)){for(var E=0;E<h.length;E++)ve(h[E],e);Object.freeze&&Object.freeze(h)}else m(\"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\");else ve(h,e)}return e===y?on(d):an(d),d}}var cn=ln;Q.Fragment=y,Q.jsxDEV=cn})()});var Re=B((En,ke)=>{\"use strict\";ke.exports=Ee()});var yn={};bn(yn,{default:()=>vn,frontmatter:()=>_n});var a=hn(Re()),_n={title:\"Terms and Conditions | Kapreon\",description:\"Terms and conditions of use for Kapreon's website.\",heroTitle:\"Terms and Conditions\",heroSubtitle:\"Legal\",lastUpdated:new Date(1739232e6)};function Te(u){let t=Object.assign({p:\"p\",h2:\"h2\",ul:\"ul\",li:\"li\",a:\"a\",h3:\"h3\"},u.components);return(0,a.jsxDEV)(a.Fragment,{children:[(0,a.jsxDEV)(t.p,{children:\"Kapreon is a company incorporated in Canada. The information contained on this website is the property of Kapreon and is protected by copyright and trademark laws in force in Canada and Quebec.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\",lineNumber:9,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.h2,{children:\"Publication Manager\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\",lineNumber:11,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.ul,{children:[`\n`,(0,a.jsxDEV)(t.li,{children:\"Kapreon Inc.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\",lineNumber:13,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.li,{children:\"Quebec Enterprise Number (NEQ): **********\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\",lineNumber:14,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.li,{children:\"Head office: 100-50 Rue St-Charles O., Longueuil, J4H 1C6, Quebec, Canada\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\",lineNumber:15,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.li,{children:[\"Phone number: \",(0,a.jsxDEV)(t.a,{href:\"tel:+14388033053\",children:\"+****************\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\",lineNumber:16,columnNumber:17},this)]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\",lineNumber:16,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.li,{children:[\"Email address: \",(0,a.jsxDEV)(t.a,{href:\"mailto:<EMAIL>\",children:\"<EMAIL>\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\",lineNumber:17,columnNumber:18},this)]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\",lineNumber:17,columnNumber:1},this),`\n`]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\",lineNumber:13,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.h2,{children:\"Liability\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\",lineNumber:19,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:\"The information contained on this website is provided for informational purposes and is not intended to be exhaustive. Kapreon cannot guarantee the accuracy, completeness or timeliness of information published on its website.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\",lineNumber:21,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:\"Consequently, Kapreon disclaims all liability:\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\",lineNumber:23,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.ul,{children:[`\n`,(0,a.jsxDEV)(t.li,{children:\"for any inaccuracy, inexactitude or omission relating to information available on the website;\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\",lineNumber:25,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.li,{children:\"for all damages resulting from fraudulent intrusion by a third party that led to modification of information made available on the website;\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\",lineNumber:26,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.li,{children:\"for all damages, direct or indirect, caused due to access to or use of the site.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\",lineNumber:27,columnNumber:1},this),`\n`]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\",lineNumber:25,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:\"Kapreon does not guarantee uninterrupted access to the site. Kapreon reserves the right to suspend access for maintenance or any other technical reason.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\",lineNumber:29,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:\"External hyperlinks present on the site cannot engage Kapreon's liability.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\",lineNumber:31,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.h2,{children:\"Intellectual Property\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\",lineNumber:33,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:\"The site may contain trademarks, logos, images, videos, texts and other content belonging to Kapreon or third parties. Kapreon holds exclusive rights to certain content, while others are the property of partners, clients or third parties and are used with their authorization or as part of completed projects, fictional or not.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\",lineNumber:35,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:\"Any reproduction, distribution, modification or reuse, in whole or in part, of elements present on this site is prohibited without express authorization from rights holders. Use of site content, including third-party images and logos, does not confer any property or exploitation rights to users.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\",lineNumber:37,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.h2,{children:\"Personal Data Protection\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\",lineNumber:39,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:\"Personal data collected on the site is processed in accordance with Canada's Personal Information Protection and Electronic Documents Act (PIPEDA) and Quebec's Act respecting the protection of personal information in the private sector.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\",lineNumber:41,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.h3,{children:\"Processing Purpose\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\",lineNumber:43,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:\"Data collected is used exclusively to respond to information requests and manage registrations and services offered on the site.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\",lineNumber:45,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.h3,{children:\"Data Recipients\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\",lineNumber:47,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:\"Data is intended for internal use exclusively and will under no circumstances be transferred, sold or rented to third parties.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\",lineNumber:49,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.h3,{children:\"Data Security\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\",lineNumber:51,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:\"Kapreon implements strict security measures to protect personal information against unauthorized access, loss or modification. Data is stored on secure and encrypted servers.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\",lineNumber:53,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.h3,{children:\"Retention Period\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\",lineNumber:55,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:\"Data is retained as long as necessary for intended purposes, in accordance with applicable legislation.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\",lineNumber:57,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.h3,{children:\"User Rights\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\",lineNumber:59,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:[\"In accordance with PIPEDA and Quebec legislation, you have the right to access, rectify and delete data concerning you. You can exercise this right by contacting us at \",(0,a.jsxDEV)(t.a,{href:\"mailto:<EMAIL>\",children:\"<EMAIL>\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\",lineNumber:61,columnNumber:169},this),\".\"]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\",lineNumber:61,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.h3,{children:\"Cookie Policy\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\",lineNumber:63,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:[\"Kapreon uses cookies to improve user experience and analyze site audience. You can consult our \",(0,a.jsxDEV)(t.a,{href:\"/legal/cookies\",children:\"Cookie Policy\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\",lineNumber:65,columnNumber:96},this),\" for more information on their use and managing your preferences.\"]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\",lineNumber:65,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.h3,{children:\"Applicable Law and Competent Jurisdiction\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\",lineNumber:67,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:\"This site is subject to Canadian and Quebec laws. In case of dispute, the competent jurisdiction is that of Quebec courts, Montreal district.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\",lineNumber:69,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:\"These legal notices may be modified at any time. We therefore invite you to consult them regularly.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\",lineNumber:71,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:\"Last update: February 11, 2025\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\",lineNumber:73,columnNumber:1},this)]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\",lineNumber:1,columnNumber:1},this)}function gn(u={}){let{wrapper:t}=u.components||{};return t?(0,a.jsxDEV)(t,Object.assign({},u,{children:(0,a.jsxDEV)(Te,u,void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\"},this)}),void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\"},this):Te(u)}var vn=gn;return pn(yn);})();\n/*! Bundled license information:\n\nreact/cjs/react-jsx-dev-runtime.development.js:\n  (**\n   * @license React\n   * react-jsx-dev-runtime.development.js\n   *\n   * Copyright (c) Facebook, Inc. and its affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *)\n*/\n;return Component;"}, "_id": "legal/en/terms.mdx", "_raw": {"sourceFilePath": "legal/en/terms.mdx", "sourceFileName": "terms.mdx", "sourceFileDir": "legal/en", "contentType": "mdx", "flattenedPath": "legal/en/terms"}, "type": "Legal", "locale": "en", "slug": "terms"}, {"title": "Utilisation des cookies | Kapreon", "description": "Politique d'utilisation des cookies de Kapreon conforme aux lois canadiennes et québécoises.", "heroTitle": "Utilisation des cookies", "heroSubtitle": "Légal", "lastUpdated": "2025-02-11T00:00:00.000Z", "body": {"raw": "\nLa présente politique vise à informer les utilisateurs de notre site web sur l'utilisation des témoins de connexion, communément appelés « cookies », conformément aux lois canadiennes et québécoises, notamment la Loi 25 du Québec. En accédant à notre site, l'utilisateur est d'accord à l'utilisation des cookies tels que décrits ci-dessous.\n\n## Qu'est-ce qu'un cookie ?\n\nUn cookie est un fichier texte que le site web envoie à votre navigateur et qui est ensuite stocké sur un appareil. Les cookies permettent au site de reconnaître l'appareil de l'utilisateur lors de visites ultérieures, facilitant ainsi la navigation et améliorant l'expérience utilisateur.\n\n## Type de cookies\n\nKapreon utilise des cookies analytiques pour collecter des informations anonymes sur la manière dont les visiteurs utilisent le site de Kapreon. Ces données aident Kapreon à améliorer le contenu et les performances du site.\n\n## Durée de conservation des cookies\n\nLes cookies sont conservés pour une durée proportionnelle à leur finalité.\n\nLors de la première visite sur le site de Kapreon, une bannière informe l'utilisateur de l'utilisation des cookies et invite l'utilisateur à les accepter ou à les refuser. L'utilisateur peut à tout moment modifier ses préférences en matière de cookies en ajustant les paramètres de son navigateur.\n\n## Droits des utilisateurs\n\nConformément à la Loi 25 du Québec et à la LPRPDE, l'utilisateur dispose des droits suivants :\n\n- **Droit d'accès** : L'utilisateur peut demander l'accès aux renseignements personnels que nous détenons au sujet de l'utilisateur.\n- **Droit de rectification** : L'utilisateur peut demander la correction de renseignements personnels inexacts ou incomplets.\n- **Droit de retrait du consentement** : L'utilisateur peut retirer son consentement à l'utilisation de ses renseignements personnels, y compris les cookies, à tout moment.\n\n## Modification de la politique\n\nKapreon se réserve le droit de modifier la présente politique en fonction des évolutions législatives ou techniques. Kapreon encourage à consulter régulièrement cette page pour tenir informé ses utilisateurs des éventuels changements.\n\nPour toute question ou préoccupation concernant cette politique ou les pratiques de Kapreon en matière de protection des renseignements personnels, veuillez contacter l'agence à [<EMAIL>](mailto:<EMAIL>).\n\nCette politique est en vigueur à compter du 11 février 2025.\n", "code": "var Component=(()=>{var ur=Object.create;var F=Object.defineProperty;var sr=Object.getOwnPropertyDescriptor;var cr=Object.getOwnPropertyNames;var fr=Object.getPrototypeOf,dr=Object.prototype.hasOwnProperty;var B=(c,t)=>()=>(t||c((t={exports:{}}).exports,t),t.exports),mr=(c,t)=>{for(var v in t)F(c,v,{get:t[v],enumerable:!0})},Ee=(c,t,v,y)=>{if(t&&typeof t==\"object\"||typeof t==\"function\")for(let _ of cr(t))!dr.call(c,_)&&_!==v&&F(c,_,{get:()=>t[_],enumerable:!(y=sr(t,_))||y.enumerable});return c};var pr=(c,t,v)=>(v=c!=null?ur(fr(c)):{},Ee(t||!c||!c.__esModule?F(v,\"default\",{value:c,enumerable:!0}):v,c)),br=c=>Ee(F({},\"__esModule\",{value:!0}),c);var Re=B((Er,xe)=>{xe.exports=React});var Ne=B(z=>{\"use strict\";(function(){\"use strict\";var c=Re(),t=Symbol.for(\"react.element\"),v=Symbol.for(\"react.portal\"),y=Symbol.for(\"react.fragment\"),_=Symbol.for(\"react.strict_mode\"),G=Symbol.for(\"react.profiler\"),X=Symbol.for(\"react.provider\"),H=Symbol.for(\"react.context\"),T=Symbol.for(\"react.forward_ref\"),A=Symbol.for(\"react.suspense\"),L=Symbol.for(\"react.suspense_list\"),C=Symbol.for(\"react.memo\"),I=Symbol.for(\"react.lazy\"),Ce=Symbol.for(\"react.offscreen\"),Q=Symbol.iterator,Pe=\"@@iterator\";function Oe(e){if(e===null||typeof e!=\"object\")return null;var r=Q&&e[Q]||e[Pe];return typeof r==\"function\"?r:null}var x=c.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;function m(e){{for(var r=arguments.length,n=new Array(r>1?r-1:0),a=1;a<r;a++)n[a-1]=arguments[a];Se(\"error\",e,n)}}function Se(e,r,n){{var a=x.ReactDebugCurrentFrame,l=a.getStackAddendum();l!==\"\"&&(r+=\"%s\",n=n.concat([l]));var s=n.map(function(o){return String(o)});s.unshift(\"Warning: \"+r),Function.prototype.apply.call(console[e],console,s)}}var je=!1,De=!1,Fe=!1,Ae=!1,Le=!1,J;J=Symbol.for(\"react.module.reference\");function Ie(e){return!!(typeof e==\"string\"||typeof e==\"function\"||e===y||e===G||Le||e===_||e===A||e===L||Ae||e===Ce||je||De||Fe||typeof e==\"object\"&&e!==null&&(e.$$typeof===I||e.$$typeof===C||e.$$typeof===X||e.$$typeof===H||e.$$typeof===T||e.$$typeof===J||e.getModuleId!==void 0))}function qe(e,r,n){var a=e.displayName;if(a)return a;var l=r.displayName||r.name||\"\";return l!==\"\"?n+\"(\"+l+\")\":n}function Z(e){return e.displayName||\"Context\"}function h(e){if(e==null)return null;if(typeof e.tag==\"number\"&&m(\"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"),typeof e==\"function\")return e.displayName||e.name||null;if(typeof e==\"string\")return e;switch(e){case y:return\"Fragment\";case v:return\"Portal\";case G:return\"Profiler\";case _:return\"StrictMode\";case A:return\"Suspense\";case L:return\"SuspenseList\"}if(typeof e==\"object\")switch(e.$$typeof){case H:var r=e;return Z(r)+\".Consumer\";case X:var n=e;return Z(n._context)+\".Provider\";case T:return qe(e,e.render,\"ForwardRef\");case C:var a=e.displayName||null;return a!==null?a:h(e.type)||\"Memo\";case I:{var l=e,s=l._payload,o=l._init;try{return h(o(s))}catch{return null}}}return null}var E=Object.assign,w=0,ee,re,ne,te,ae,ie,oe;function le(){}le.__reactDisabledLog=!0;function Ye(){{if(w===0){ee=console.log,re=console.info,ne=console.warn,te=console.error,ae=console.group,ie=console.groupCollapsed,oe=console.groupEnd;var e={configurable:!0,enumerable:!0,value:le,writable:!0};Object.defineProperties(console,{info:e,log:e,warn:e,error:e,group:e,groupCollapsed:e,groupEnd:e})}w++}}function $e(){{if(w--,w===0){var e={configurable:!0,enumerable:!0,writable:!0};Object.defineProperties(console,{log:E({},e,{value:ee}),info:E({},e,{value:re}),warn:E({},e,{value:ne}),error:E({},e,{value:te}),group:E({},e,{value:ae}),groupCollapsed:E({},e,{value:ie}),groupEnd:E({},e,{value:oe})})}w<0&&m(\"disabledDepth fell below zero. This is a bug in React. Please file an issue.\")}}var q=x.ReactCurrentDispatcher,Y;function P(e,r,n){{if(Y===void 0)try{throw Error()}catch(l){var a=l.stack.trim().match(/\\n( *(at )?)/);Y=a&&a[1]||\"\"}return`\n`+Y+e}}var $=!1,O;{var We=typeof WeakMap==\"function\"?WeakMap:Map;O=new We}function ue(e,r){if(!e||$)return\"\";{var n=O.get(e);if(n!==void 0)return n}var a;$=!0;var l=Error.prepareStackTrace;Error.prepareStackTrace=void 0;var s;s=q.current,q.current=null,Ye();try{if(r){var o=function(){throw Error()};if(Object.defineProperty(o.prototype,\"props\",{set:function(){throw Error()}}),typeof Reflect==\"object\"&&Reflect.construct){try{Reflect.construct(o,[])}catch(g){a=g}Reflect.construct(e,[],o)}else{try{o.call()}catch(g){a=g}e.call(o.prototype)}}else{try{throw Error()}catch(g){a=g}e()}}catch(g){if(g&&a&&typeof g.stack==\"string\"){for(var i=g.stack.split(`\n`),p=a.stack.split(`\n`),f=i.length-1,d=p.length-1;f>=1&&d>=0&&i[f]!==p[d];)d--;for(;f>=1&&d>=0;f--,d--)if(i[f]!==p[d]){if(f!==1||d!==1)do if(f--,d--,d<0||i[f]!==p[d]){var b=`\n`+i[f].replace(\" at new \",\" at \");return e.displayName&&b.includes(\"<anonymous>\")&&(b=b.replace(\"<anonymous>\",e.displayName)),typeof e==\"function\"&&O.set(e,b),b}while(f>=1&&d>=0);break}}}finally{$=!1,q.current=s,$e(),Error.prepareStackTrace=l}var N=e?e.displayName||e.name:\"\",ye=N?P(N):\"\";return typeof e==\"function\"&&O.set(e,ye),ye}function Me(e,r,n){return ue(e,!1)}function Ve(e){var r=e.prototype;return!!(r&&r.isReactComponent)}function S(e,r,n){if(e==null)return\"\";if(typeof e==\"function\")return ue(e,Ve(e));if(typeof e==\"string\")return P(e);switch(e){case A:return P(\"Suspense\");case L:return P(\"SuspenseList\")}if(typeof e==\"object\")switch(e.$$typeof){case T:return Me(e.render);case C:return S(e.type,r,n);case I:{var a=e,l=a._payload,s=a._init;try{return S(s(l),r,n)}catch{}}}return\"\"}var j=Object.prototype.hasOwnProperty,se={},ce=x.ReactDebugCurrentFrame;function D(e){if(e){var r=e._owner,n=S(e.type,e._source,r?r.type:null);ce.setExtraStackFrame(n)}else ce.setExtraStackFrame(null)}function Ue(e,r,n,a,l){{var s=Function.call.bind(j);for(var o in e)if(s(e,o)){var i=void 0;try{if(typeof e[o]!=\"function\"){var p=Error((a||\"React class\")+\": \"+n+\" type `\"+o+\"` is invalid; it must be a function, usually from the `prop-types` package, but received `\"+typeof e[o]+\"`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.\");throw p.name=\"Invariant Violation\",p}i=e[o](r,o,a,n,null,\"SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED\")}catch(f){i=f}i&&!(i instanceof Error)&&(D(l),m(\"%s: type specification of %s `%s` is invalid; the type checker function must return `null` or an `Error` but returned a %s. You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument).\",a||\"React class\",n,o,typeof i),D(null)),i instanceof Error&&!(i.message in se)&&(se[i.message]=!0,D(l),m(\"Failed %s type: %s\",n,i.message),D(null))}}}var Ke=Array.isArray;function W(e){return Ke(e)}function Be(e){{var r=typeof Symbol==\"function\"&&Symbol.toStringTag,n=r&&e[Symbol.toStringTag]||e.constructor.name||\"Object\";return n}}function ze(e){try{return fe(e),!1}catch{return!0}}function fe(e){return\"\"+e}function de(e){if(ze(e))return m(\"The provided key is an unsupported type %s. This value must be coerced to a string before before using it here.\",Be(e)),fe(e)}var k=x.ReactCurrentOwner,Ge={key:!0,ref:!0,__self:!0,__source:!0},me,pe,M;M={};function Xe(e){if(j.call(e,\"ref\")){var r=Object.getOwnPropertyDescriptor(e,\"ref\").get;if(r&&r.isReactWarning)return!1}return e.ref!==void 0}function He(e){if(j.call(e,\"key\")){var r=Object.getOwnPropertyDescriptor(e,\"key\").get;if(r&&r.isReactWarning)return!1}return e.key!==void 0}function Qe(e,r){if(typeof e.ref==\"string\"&&k.current&&r&&k.current.stateNode!==r){var n=h(k.current.type);M[n]||(m('Component \"%s\" contains the string ref \"%s\". Support for string refs will be removed in a future major release. This case cannot be automatically converted to an arrow function. We ask you to manually fix this case by using useRef() or createRef() instead. Learn more about using refs safely here: https://reactjs.org/link/strict-mode-string-ref',h(k.current.type),e.ref),M[n]=!0)}}function Je(e,r){{var n=function(){me||(me=!0,m(\"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)\",r))};n.isReactWarning=!0,Object.defineProperty(e,\"key\",{get:n,configurable:!0})}}function Ze(e,r){{var n=function(){pe||(pe=!0,m(\"%s: `ref` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)\",r))};n.isReactWarning=!0,Object.defineProperty(e,\"ref\",{get:n,configurable:!0})}}var er=function(e,r,n,a,l,s,o){var i={$$typeof:t,type:e,key:r,ref:n,props:o,_owner:s};return i._store={},Object.defineProperty(i._store,\"validated\",{configurable:!1,enumerable:!1,writable:!0,value:!1}),Object.defineProperty(i,\"_self\",{configurable:!1,enumerable:!1,writable:!1,value:a}),Object.defineProperty(i,\"_source\",{configurable:!1,enumerable:!1,writable:!1,value:l}),Object.freeze&&(Object.freeze(i.props),Object.freeze(i)),i};function rr(e,r,n,a,l){{var s,o={},i=null,p=null;n!==void 0&&(de(n),i=\"\"+n),He(r)&&(de(r.key),i=\"\"+r.key),Xe(r)&&(p=r.ref,Qe(r,l));for(s in r)j.call(r,s)&&!Ge.hasOwnProperty(s)&&(o[s]=r[s]);if(e&&e.defaultProps){var f=e.defaultProps;for(s in f)o[s]===void 0&&(o[s]=f[s])}if(i||p){var d=typeof e==\"function\"?e.displayName||e.name||\"Unknown\":e;i&&Je(o,d),p&&Ze(o,d)}return er(e,i,p,l,a,k.current,o)}}var V=x.ReactCurrentOwner,be=x.ReactDebugCurrentFrame;function R(e){if(e){var r=e._owner,n=S(e.type,e._source,r?r.type:null);be.setExtraStackFrame(n)}else be.setExtraStackFrame(null)}var U;U=!1;function K(e){return typeof e==\"object\"&&e!==null&&e.$$typeof===t}function ve(){{if(V.current){var e=h(V.current.type);if(e)return`\n\nCheck the render method of \\``+e+\"`.\"}return\"\"}}function nr(e){{if(e!==void 0){var r=e.fileName.replace(/^.*[\\\\\\/]/,\"\"),n=e.lineNumber;return`\n\nCheck your code at `+r+\":\"+n+\".\"}return\"\"}}var he={};function tr(e){{var r=ve();if(!r){var n=typeof e==\"string\"?e:e.displayName||e.name;n&&(r=`\n\nCheck the top-level render call using <`+n+\">.\")}return r}}function ge(e,r){{if(!e._store||e._store.validated||e.key!=null)return;e._store.validated=!0;var n=tr(r);if(he[n])return;he[n]=!0;var a=\"\";e&&e._owner&&e._owner!==V.current&&(a=\" It was passed a child from \"+h(e._owner.type)+\".\"),R(e),m('Each child in a list should have a unique \"key\" prop.%s%s See https://reactjs.org/link/warning-keys for more information.',n,a),R(null)}}function _e(e,r){{if(typeof e!=\"object\")return;if(W(e))for(var n=0;n<e.length;n++){var a=e[n];K(a)&&ge(a,r)}else if(K(e))e._store&&(e._store.validated=!0);else if(e){var l=Oe(e);if(typeof l==\"function\"&&l!==e.entries)for(var s=l.call(e),o;!(o=s.next()).done;)K(o.value)&&ge(o.value,r)}}}function ar(e){{var r=e.type;if(r==null||typeof r==\"string\")return;var n;if(typeof r==\"function\")n=r.propTypes;else if(typeof r==\"object\"&&(r.$$typeof===T||r.$$typeof===C))n=r.propTypes;else return;if(n){var a=h(r);Ue(n,e.props,\"prop\",a,e)}else if(r.PropTypes!==void 0&&!U){U=!0;var l=h(r);m(\"Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?\",l||\"Unknown\")}typeof r.getDefaultProps==\"function\"&&!r.getDefaultProps.isReactClassApproved&&m(\"getDefaultProps is only used on classic React.createClass definitions. Use a static property named `defaultProps` instead.\")}}function ir(e){{for(var r=Object.keys(e.props),n=0;n<r.length;n++){var a=r[n];if(a!==\"children\"&&a!==\"key\"){R(e),m(\"Invalid prop `%s` supplied to `React.Fragment`. React.Fragment can only have `key` and `children` props.\",a),R(null);break}}e.ref!==null&&(R(e),m(\"Invalid attribute `ref` supplied to `React.Fragment`.\"),R(null))}}function or(e,r,n,a,l,s){{var o=Ie(e);if(!o){var i=\"\";(e===void 0||typeof e==\"object\"&&e!==null&&Object.keys(e).length===0)&&(i+=\" You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.\");var p=nr(l);p?i+=p:i+=ve();var f;e===null?f=\"null\":W(e)?f=\"array\":e!==void 0&&e.$$typeof===t?(f=\"<\"+(h(e.type)||\"Unknown\")+\" />\",i=\" Did you accidentally export a JSX literal instead of a component?\"):f=typeof e,m(\"React.jsx: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: %s.%s\",f,i)}var d=rr(e,r,n,l,s);if(d==null)return d;if(o){var b=r.children;if(b!==void 0)if(a)if(W(b)){for(var N=0;N<b.length;N++)_e(b[N],e);Object.freeze&&Object.freeze(b)}else m(\"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\");else _e(b,e)}return e===y?ir(d):ar(d),d}}var lr=or;z.Fragment=y,z.jsxDEV=lr})()});var ke=B((Rr,we)=>{\"use strict\";we.exports=Ne()});var _r={};mr(_r,{default:()=>gr,frontmatter:()=>vr});var u=pr(ke()),vr={title:\"Utilisation des cookies | Kapreon\",description:\"Politique d'utilisation des cookies de Kapreon conforme aux lois canadiennes et qu\\xE9b\\xE9coises.\",heroTitle:\"Utilisation des cookies\",heroSubtitle:\"L\\xE9gal\",lastUpdated:new Date(1739232e6)};function Te(c){let t=Object.assign({p:\"p\",h2:\"h2\",ul:\"ul\",li:\"li\",strong:\"strong\",a:\"a\"},c.components);return(0,u.jsxDEV)(u.Fragment,{children:[(0,u.jsxDEV)(t.p,{children:\"La pr\\xE9sente politique vise \\xE0 informer les utilisateurs de notre site web sur l'utilisation des t\\xE9moins de connexion, commun\\xE9ment appel\\xE9s \\xAB cookies \\xBB, conform\\xE9ment aux lois canadiennes et qu\\xE9b\\xE9coises, notamment la Loi 25 du Qu\\xE9bec. En acc\\xE9dant \\xE0 notre site, l'utilisateur est d'accord \\xE0 l'utilisation des cookies tels que d\\xE9crits ci-dessous.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-ae02f340-9368-4299-8885-53020007715a.mdx\",lineNumber:9,columnNumber:1},this),`\n`,(0,u.jsxDEV)(t.h2,{children:\"Qu'est-ce qu'un cookie ?\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-ae02f340-9368-4299-8885-53020007715a.mdx\",lineNumber:11,columnNumber:1},this),`\n`,(0,u.jsxDEV)(t.p,{children:\"Un cookie est un fichier texte que le site web envoie \\xE0 votre navigateur et qui est ensuite stock\\xE9 sur un appareil. Les cookies permettent au site de reconna\\xEEtre l'appareil de l'utilisateur lors de visites ult\\xE9rieures, facilitant ainsi la navigation et am\\xE9liorant l'exp\\xE9rience utilisateur.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-ae02f340-9368-4299-8885-53020007715a.mdx\",lineNumber:13,columnNumber:1},this),`\n`,(0,u.jsxDEV)(t.h2,{children:\"Type de cookies\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-ae02f340-9368-4299-8885-53020007715a.mdx\",lineNumber:15,columnNumber:1},this),`\n`,(0,u.jsxDEV)(t.p,{children:\"Kapreon utilise des cookies analytiques pour collecter des informations anonymes sur la mani\\xE8re dont les visiteurs utilisent le site de Kapreon. Ces donn\\xE9es aident Kapreon \\xE0 am\\xE9liorer le contenu et les performances du site.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-ae02f340-9368-4299-8885-53020007715a.mdx\",lineNumber:17,columnNumber:1},this),`\n`,(0,u.jsxDEV)(t.h2,{children:\"Dur\\xE9e de conservation des cookies\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-ae02f340-9368-4299-8885-53020007715a.mdx\",lineNumber:19,columnNumber:1},this),`\n`,(0,u.jsxDEV)(t.p,{children:\"Les cookies sont conserv\\xE9s pour une dur\\xE9e proportionnelle \\xE0 leur finalit\\xE9.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-ae02f340-9368-4299-8885-53020007715a.mdx\",lineNumber:21,columnNumber:1},this),`\n`,(0,u.jsxDEV)(t.p,{children:\"Lors de la premi\\xE8re visite sur le site de Kapreon, une banni\\xE8re informe l'utilisateur de l'utilisation des cookies et invite l'utilisateur \\xE0 les accepter ou \\xE0 les refuser. L'utilisateur peut \\xE0 tout moment modifier ses pr\\xE9f\\xE9rences en mati\\xE8re de cookies en ajustant les param\\xE8tres de son navigateur.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-ae02f340-9368-4299-8885-53020007715a.mdx\",lineNumber:23,columnNumber:1},this),`\n`,(0,u.jsxDEV)(t.h2,{children:\"Droits des utilisateurs\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-ae02f340-9368-4299-8885-53020007715a.mdx\",lineNumber:25,columnNumber:1},this),`\n`,(0,u.jsxDEV)(t.p,{children:\"Conform\\xE9ment \\xE0 la Loi 25 du Qu\\xE9bec et \\xE0 la LPRPDE, l'utilisateur dispose des droits suivants :\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-ae02f340-9368-4299-8885-53020007715a.mdx\",lineNumber:27,columnNumber:1},this),`\n`,(0,u.jsxDEV)(t.ul,{children:[`\n`,(0,u.jsxDEV)(t.li,{children:[(0,u.jsxDEV)(t.strong,{children:\"Droit d'acc\\xE8s\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-ae02f340-9368-4299-8885-53020007715a.mdx\",lineNumber:29,columnNumber:3},this),\" : L'utilisateur peut demander l'acc\\xE8s aux renseignements personnels que nous d\\xE9tenons au sujet de l'utilisateur.\"]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-ae02f340-9368-4299-8885-53020007715a.mdx\",lineNumber:29,columnNumber:1},this),`\n`,(0,u.jsxDEV)(t.li,{children:[(0,u.jsxDEV)(t.strong,{children:\"Droit de rectification\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-ae02f340-9368-4299-8885-53020007715a.mdx\",lineNumber:30,columnNumber:3},this),\" : L'utilisateur peut demander la correction de renseignements personnels inexacts ou incomplets.\"]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-ae02f340-9368-4299-8885-53020007715a.mdx\",lineNumber:30,columnNumber:1},this),`\n`,(0,u.jsxDEV)(t.li,{children:[(0,u.jsxDEV)(t.strong,{children:\"Droit de retrait du consentement\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-ae02f340-9368-4299-8885-53020007715a.mdx\",lineNumber:31,columnNumber:3},this),\" : L'utilisateur peut retirer son consentement \\xE0 l'utilisation de ses renseignements personnels, y compris les cookies, \\xE0 tout moment.\"]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-ae02f340-9368-4299-8885-53020007715a.mdx\",lineNumber:31,columnNumber:1},this),`\n`]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-ae02f340-9368-4299-8885-53020007715a.mdx\",lineNumber:29,columnNumber:1},this),`\n`,(0,u.jsxDEV)(t.h2,{children:\"Modification de la politique\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-ae02f340-9368-4299-8885-53020007715a.mdx\",lineNumber:33,columnNumber:1},this),`\n`,(0,u.jsxDEV)(t.p,{children:\"Kapreon se r\\xE9serve le droit de modifier la pr\\xE9sente politique en fonction des \\xE9volutions l\\xE9gislatives ou techniques. Kapreon encourage \\xE0 consulter r\\xE9guli\\xE8rement cette page pour tenir inform\\xE9 ses utilisateurs des \\xE9ventuels changements.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-ae02f340-9368-4299-8885-53020007715a.mdx\",lineNumber:35,columnNumber:1},this),`\n`,(0,u.jsxDEV)(t.p,{children:[\"Pour toute question ou pr\\xE9occupation concernant cette politique ou les pratiques de Kapreon en mati\\xE8re de protection des renseignements personnels, veuillez contacter l'agence \\xE0 \",(0,u.jsxDEV)(t.a,{href:\"mailto:<EMAIL>\",children:\"<EMAIL>\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-ae02f340-9368-4299-8885-53020007715a.mdx\",lineNumber:37,columnNumber:179},this),\".\"]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-ae02f340-9368-4299-8885-53020007715a.mdx\",lineNumber:37,columnNumber:1},this),`\n`,(0,u.jsxDEV)(t.p,{children:\"Cette politique est en vigueur \\xE0 compter du 11 f\\xE9vrier 2025.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-ae02f340-9368-4299-8885-53020007715a.mdx\",lineNumber:39,columnNumber:1},this)]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-ae02f340-9368-4299-8885-53020007715a.mdx\",lineNumber:1,columnNumber:1},this)}function hr(c={}){let{wrapper:t}=c.components||{};return t?(0,u.jsxDEV)(t,Object.assign({},c,{children:(0,u.jsxDEV)(Te,c,void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-ae02f340-9368-4299-8885-53020007715a.mdx\"},this)}),void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-ae02f340-9368-4299-8885-53020007715a.mdx\"},this):Te(c)}var gr=hr;return br(_r);})();\n/*! Bundled license information:\n\nreact/cjs/react-jsx-dev-runtime.development.js:\n  (**\n   * @license React\n   * react-jsx-dev-runtime.development.js\n   *\n   * Copyright (c) Facebook, Inc. and its affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *)\n*/\n;return Component;"}, "_id": "legal/fr/cookies.mdx", "_raw": {"sourceFilePath": "legal/fr/cookies.mdx", "sourceFileName": "cookies.mdx", "sourceFileDir": "legal/fr", "contentType": "mdx", "flattenedPath": "legal/fr/cookies"}, "type": "Legal", "locale": "fr", "slug": "cookies"}, {"title": "Termes et conditions | Kapreon", "description": "Termes et conditions d'utilisation du site web de Kapreon.", "heroTitle": "Te<PERSON><PERSON> et conditions", "heroSubtitle": "Légal", "lastUpdated": "2025-02-11T00:00:00.000Z", "body": {"raw": "\nKapreon est une entreprise immatriculée au Canada. Les informations contenues sur ce site web sont la propriété de Kapreon et sont protégées par les lois sur les droits d'auteur et les marques en vigueur au Canada et au Québec.\n\n## Responsable de la publication\n\n- Kapreon Inc.\n- Numéro d'entreprise du Québec (NEQ) : **********\n- Siège social: 100-50 Rue St-Charles O., Longueuil, J4H 1C6, Québec, Canada\n- Numéro de téléphone : [+****************](tel:+14388033053)\n- <PERSON>ress<PERSON> cour<PERSON> : [<EMAIL>](mailto:<EMAIL>)\n\n## Responsabilité\n\nLes informations contenues sur ce site web sont fournies à titre indicatif et ne sont pas destinées à être exhaustives. Kapreon ne peut garantir l'exactitude, la complétude ou l'actualité des informations diffusées sur son site web.\n\nEn conséquence, Kapreon décline toute responsabilité :\n\n- pour toute imprécision, inexactitude ou omission portant sur des informations disponibles sur le site web;\n- pour tous dommages résultant d'une intrusion frauduleuse d'un tiers ayant entraîné une modification des informations mises à disposition sur le site web;\n- pour tous dommages, directs ou indirects, provoqués en raison de l'accès ou de l'utilisation du site.\n\nKapreon ne garantit pas un accès ininterrompu au site. Kapreon se réserve le droit de suspendre l'accès pour maintenance ou toute autre raison technique.\n\nLes liens hypertextes externes présents sur le site ne sauraient engager la responsabilité de Kapreon.\n\n## Propriété intellectuelle\n\nLe site peut contenir des marques, logos, images, vidéos, textes et autres contenus appartenant à Kapreon ou à des tiers. Kapreon détient les droits exclusifs sur certains contenus, tandis que d'autres sont la propriété de partenaires, clients ou tiers et sont utilisés avec leur autorisation ou dans le cadre de projets réalisés, fictifs ou non.\n\nToute reproduction, diffusion, modification ou réutilisation, en tout ou en partie, des éléments présents sur ce site est interdite sans l'autorisation expresse des titulaires des droits. L'usage des contenus du site, y compris les images et logos de tiers, ne confère aucun droit de propriété ni d'exploitation aux utilisateurs.\n\n## Protection des données personnelles\n\nLes données personnelles collectées sur le site sont traitées conformément à la Loi sur la protection des renseignements personnels et les documents électroniques (LPRPDE) du Canada et à la Loi sur la protection des renseignements personnels dans le secteur privé du Québec.\n\n### Finalité du traitement\n\nLes données collectées sont utilisées exclusivement pour répondre aux demandes d'information et gérer les inscriptions et services offerts sur le site.\n\n### Destinataire des données\n\nLes données sont destinées à un usage interne exclusivement et ne seront en aucun cas cédées, vendues ou louées à des tiers.\n\n### Sécurité des données\n\nKapreon met en œuvre des mesures de sécurité strictes pour protéger les informations personnelles contre tout accès non autorisé, perte ou modification. Les données sont stockées sur des serveurs sécurisés et chiffrées.\n\n### Durée de conservation\n\nLes données sont conservées aussi longtemps que nécessaire pour les finalités prévues, conformément à la législation en vigueur.\n\n### Droits des utilisateurs\n\nConformément à la LPRPDE et à la législation québécoise, vous bénéficiez d'un droit d'accès, de rectification et de suppression des données vous concernant. Vous pouvez exercer ce droit en nous contactant à [<EMAIL>](mailto:<EMAIL>).\n\n### Politique des cookies\n\nKapreon utilise des cookies pour améliorer l'expérience utilisateur et analyser l'audience du site. Vous pouvez consulter notre [Politique de cookies](/legal/cookies) pour plus d'informations sur leur utilisation et la gestion de vos préférences.\n\n### Droit applicable et juridiction compétente\n\nCe site est soumis aux lois canadiennes et québécoises. En cas de litige, la juridiction compétente est celle des tribunaux du Québec, district de Montréal.\n\nCes mentions légales peuvent être modifiées à tout moment. Nous vous invitons donc à les consulter régulièrement.\n\nDernière mise à jour : 11 février 2025\n", "code": "var Component=(()=>{var un=Object.create;var F=Object.defineProperty;var cn=Object.getOwnPropertyDescriptor;var dn=Object.getOwnPropertyNames;var fn=Object.getPrototypeOf,mn=Object.prototype.hasOwnProperty;var z=(c,t)=>()=>(t||c((t={exports:{}}).exports,t),t.exports),bn=(c,t)=>{for(var _ in t)F(c,_,{get:t[_],enumerable:!0})},xe=(c,t,_,N)=>{if(t&&typeof t==\"object\"||typeof t==\"function\")for(let v of dn(t))!mn.call(c,v)&&v!==_&&F(c,v,{get:()=>t[v],enumerable:!(N=cn(t,v))||N.enumerable});return c};var pn=(c,t,_)=>(_=c!=null?un(fn(c)):{},xe(t||!c||!c.__esModule?F(_,\"default\",{value:c,enumerable:!0}):_,c)),_n=c=>xe(F({},\"__esModule\",{value:!0}),c);var Ee=z((yn,ye)=>{ye.exports=React});var we=z(B=>{\"use strict\";(function(){\"use strict\";var c=Ee(),t=Symbol.for(\"react.element\"),_=Symbol.for(\"react.portal\"),N=Symbol.for(\"react.fragment\"),v=Symbol.for(\"react.strict_mode\"),G=Symbol.for(\"react.profiler\"),Q=Symbol.for(\"react.provider\"),X=Symbol.for(\"react.context\"),T=Symbol.for(\"react.forward_ref\"),A=Symbol.for(\"react.suspense\"),L=Symbol.for(\"react.suspense_list\"),C=Symbol.for(\"react.memo\"),I=Symbol.for(\"react.lazy\"),Ce=Symbol.for(\"react.offscreen\"),H=Symbol.iterator,Pe=\"@@iterator\";function Oe(e){if(e===null||typeof e!=\"object\")return null;var n=H&&e[H]||e[Pe];return typeof n==\"function\"?n:null}var y=c.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;function m(e){{for(var n=arguments.length,r=new Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];Se(\"error\",e,r)}}function Se(e,n,r){{var i=y.ReactDebugCurrentFrame,l=i.getStackAddendum();l!==\"\"&&(n+=\"%s\",r=r.concat([l]));var u=r.map(function(s){return String(s)});u.unshift(\"Warning: \"+n),Function.prototype.apply.call(console[e],console,u)}}var je=!1,De=!1,Fe=!1,Ae=!1,Le=!1,J;J=Symbol.for(\"react.module.reference\");function Ie(e){return!!(typeof e==\"string\"||typeof e==\"function\"||e===N||e===G||Le||e===v||e===A||e===L||Ae||e===Ce||je||De||Fe||typeof e==\"object\"&&e!==null&&(e.$$typeof===I||e.$$typeof===C||e.$$typeof===Q||e.$$typeof===X||e.$$typeof===T||e.$$typeof===J||e.getModuleId!==void 0))}function Ye(e,n,r){var i=e.displayName;if(i)return i;var l=n.displayName||n.name||\"\";return l!==\"\"?r+\"(\"+l+\")\":r}function Z(e){return e.displayName||\"Context\"}function h(e){if(e==null)return null;if(typeof e.tag==\"number\"&&m(\"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"),typeof e==\"function\")return e.displayName||e.name||null;if(typeof e==\"string\")return e;switch(e){case N:return\"Fragment\";case _:return\"Portal\";case G:return\"Profiler\";case v:return\"StrictMode\";case A:return\"Suspense\";case L:return\"SuspenseList\"}if(typeof e==\"object\")switch(e.$$typeof){case X:var n=e;return Z(n)+\".Consumer\";case Q:var r=e;return Z(r._context)+\".Provider\";case T:return Ye(e,e.render,\"ForwardRef\");case C:var i=e.displayName||null;return i!==null?i:h(e.type)||\"Memo\";case I:{var l=e,u=l._payload,s=l._init;try{return h(s(u))}catch{return null}}}return null}var x=Object.assign,k=0,ee,ne,re,te,ae,ie,oe;function se(){}se.__reactDisabledLog=!0;function $e(){{if(k===0){ee=console.log,ne=console.info,re=console.warn,te=console.error,ae=console.group,ie=console.groupCollapsed,oe=console.groupEnd;var e={configurable:!0,enumerable:!0,value:se,writable:!0};Object.defineProperties(console,{info:e,log:e,warn:e,error:e,group:e,groupCollapsed:e,groupEnd:e})}k++}}function Ve(){{if(k--,k===0){var e={configurable:!0,enumerable:!0,writable:!0};Object.defineProperties(console,{log:x({},e,{value:ee}),info:x({},e,{value:ne}),warn:x({},e,{value:re}),error:x({},e,{value:te}),group:x({},e,{value:ae}),groupCollapsed:x({},e,{value:ie}),groupEnd:x({},e,{value:oe})})}k<0&&m(\"disabledDepth fell below zero. This is a bug in React. Please file an issue.\")}}var Y=y.ReactCurrentDispatcher,$;function P(e,n,r){{if($===void 0)try{throw Error()}catch(l){var i=l.stack.trim().match(/\\n( *(at )?)/);$=i&&i[1]||\"\"}return`\n`+$+e}}var V=!1,O;{var We=typeof WeakMap==\"function\"?WeakMap:Map;O=new We}function le(e,n){if(!e||V)return\"\";{var r=O.get(e);if(r!==void 0)return r}var i;V=!0;var l=Error.prepareStackTrace;Error.prepareStackTrace=void 0;var u;u=Y.current,Y.current=null,$e();try{if(n){var s=function(){throw Error()};if(Object.defineProperty(s.prototype,\"props\",{set:function(){throw Error()}}),typeof Reflect==\"object\"&&Reflect.construct){try{Reflect.construct(s,[])}catch(g){i=g}Reflect.construct(e,[],s)}else{try{s.call()}catch(g){i=g}e.call(s.prototype)}}else{try{throw Error()}catch(g){i=g}e()}}catch(g){if(g&&i&&typeof g.stack==\"string\"){for(var o=g.stack.split(`\n`),b=i.stack.split(`\n`),d=o.length-1,f=b.length-1;d>=1&&f>=0&&o[d]!==b[f];)f--;for(;d>=1&&f>=0;d--,f--)if(o[d]!==b[f]){if(d!==1||f!==1)do if(d--,f--,f<0||o[d]!==b[f]){var p=`\n`+o[d].replace(\" at new \",\" at \");return e.displayName&&p.includes(\"<anonymous>\")&&(p=p.replace(\"<anonymous>\",e.displayName)),typeof e==\"function\"&&O.set(e,p),p}while(d>=1&&f>=0);break}}}finally{V=!1,Y.current=u,Ve(),Error.prepareStackTrace=l}var w=e?e.displayName||e.name:\"\",Ne=w?P(w):\"\";return typeof e==\"function\"&&O.set(e,Ne),Ne}function Me(e,n,r){return le(e,!1)}function Ke(e){var n=e.prototype;return!!(n&&n.isReactComponent)}function S(e,n,r){if(e==null)return\"\";if(typeof e==\"function\")return le(e,Ke(e));if(typeof e==\"string\")return P(e);switch(e){case A:return P(\"Suspense\");case L:return P(\"SuspenseList\")}if(typeof e==\"object\")switch(e.$$typeof){case T:return Me(e.render);case C:return S(e.type,n,r);case I:{var i=e,l=i._payload,u=i._init;try{return S(u(l),n,r)}catch{}}}return\"\"}var j=Object.prototype.hasOwnProperty,ue={},ce=y.ReactDebugCurrentFrame;function D(e){if(e){var n=e._owner,r=S(e.type,e._source,n?n.type:null);ce.setExtraStackFrame(r)}else ce.setExtraStackFrame(null)}function qe(e,n,r,i,l){{var u=Function.call.bind(j);for(var s in e)if(u(e,s)){var o=void 0;try{if(typeof e[s]!=\"function\"){var b=Error((i||\"React class\")+\": \"+r+\" type `\"+s+\"` is invalid; it must be a function, usually from the `prop-types` package, but received `\"+typeof e[s]+\"`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.\");throw b.name=\"Invariant Violation\",b}o=e[s](n,s,i,r,null,\"SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED\")}catch(d){o=d}o&&!(o instanceof Error)&&(D(l),m(\"%s: type specification of %s `%s` is invalid; the type checker function must return `null` or an `Error` but returned a %s. You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument).\",i||\"React class\",r,s,typeof o),D(null)),o instanceof Error&&!(o.message in ue)&&(ue[o.message]=!0,D(l),m(\"Failed %s type: %s\",r,o.message),D(null))}}}var Ue=Array.isArray;function W(e){return Ue(e)}function ze(e){{var n=typeof Symbol==\"function\"&&Symbol.toStringTag,r=n&&e[Symbol.toStringTag]||e.constructor.name||\"Object\";return r}}function Be(e){try{return de(e),!1}catch{return!0}}function de(e){return\"\"+e}function fe(e){if(Be(e))return m(\"The provided key is an unsupported type %s. This value must be coerced to a string before before using it here.\",ze(e)),de(e)}var R=y.ReactCurrentOwner,Ge={key:!0,ref:!0,__self:!0,__source:!0},me,be,M;M={};function Qe(e){if(j.call(e,\"ref\")){var n=Object.getOwnPropertyDescriptor(e,\"ref\").get;if(n&&n.isReactWarning)return!1}return e.ref!==void 0}function Xe(e){if(j.call(e,\"key\")){var n=Object.getOwnPropertyDescriptor(e,\"key\").get;if(n&&n.isReactWarning)return!1}return e.key!==void 0}function He(e,n){if(typeof e.ref==\"string\"&&R.current&&n&&R.current.stateNode!==n){var r=h(R.current.type);M[r]||(m('Component \"%s\" contains the string ref \"%s\". Support for string refs will be removed in a future major release. This case cannot be automatically converted to an arrow function. We ask you to manually fix this case by using useRef() or createRef() instead. Learn more about using refs safely here: https://reactjs.org/link/strict-mode-string-ref',h(R.current.type),e.ref),M[r]=!0)}}function Je(e,n){{var r=function(){me||(me=!0,m(\"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)\",n))};r.isReactWarning=!0,Object.defineProperty(e,\"key\",{get:r,configurable:!0})}}function Ze(e,n){{var r=function(){be||(be=!0,m(\"%s: `ref` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)\",n))};r.isReactWarning=!0,Object.defineProperty(e,\"ref\",{get:r,configurable:!0})}}var en=function(e,n,r,i,l,u,s){var o={$$typeof:t,type:e,key:n,ref:r,props:s,_owner:u};return o._store={},Object.defineProperty(o._store,\"validated\",{configurable:!1,enumerable:!1,writable:!0,value:!1}),Object.defineProperty(o,\"_self\",{configurable:!1,enumerable:!1,writable:!1,value:i}),Object.defineProperty(o,\"_source\",{configurable:!1,enumerable:!1,writable:!1,value:l}),Object.freeze&&(Object.freeze(o.props),Object.freeze(o)),o};function nn(e,n,r,i,l){{var u,s={},o=null,b=null;r!==void 0&&(fe(r),o=\"\"+r),Xe(n)&&(fe(n.key),o=\"\"+n.key),Qe(n)&&(b=n.ref,He(n,l));for(u in n)j.call(n,u)&&!Ge.hasOwnProperty(u)&&(s[u]=n[u]);if(e&&e.defaultProps){var d=e.defaultProps;for(u in d)s[u]===void 0&&(s[u]=d[u])}if(o||b){var f=typeof e==\"function\"?e.displayName||e.name||\"Unknown\":e;o&&Je(s,f),b&&Ze(s,f)}return en(e,o,b,l,i,R.current,s)}}var K=y.ReactCurrentOwner,pe=y.ReactDebugCurrentFrame;function E(e){if(e){var n=e._owner,r=S(e.type,e._source,n?n.type:null);pe.setExtraStackFrame(r)}else pe.setExtraStackFrame(null)}var q;q=!1;function U(e){return typeof e==\"object\"&&e!==null&&e.$$typeof===t}function _e(){{if(K.current){var e=h(K.current.type);if(e)return`\n\nCheck the render method of \\``+e+\"`.\"}return\"\"}}function rn(e){{if(e!==void 0){var n=e.fileName.replace(/^.*[\\\\\\/]/,\"\"),r=e.lineNumber;return`\n\nCheck your code at `+n+\":\"+r+\".\"}return\"\"}}var he={};function tn(e){{var n=_e();if(!n){var r=typeof e==\"string\"?e:e.displayName||e.name;r&&(n=`\n\nCheck the top-level render call using <`+r+\">.\")}return n}}function ge(e,n){{if(!e._store||e._store.validated||e.key!=null)return;e._store.validated=!0;var r=tn(n);if(he[r])return;he[r]=!0;var i=\"\";e&&e._owner&&e._owner!==K.current&&(i=\" It was passed a child from \"+h(e._owner.type)+\".\"),E(e),m('Each child in a list should have a unique \"key\" prop.%s%s See https://reactjs.org/link/warning-keys for more information.',r,i),E(null)}}function ve(e,n){{if(typeof e!=\"object\")return;if(W(e))for(var r=0;r<e.length;r++){var i=e[r];U(i)&&ge(i,n)}else if(U(e))e._store&&(e._store.validated=!0);else if(e){var l=Oe(e);if(typeof l==\"function\"&&l!==e.entries)for(var u=l.call(e),s;!(s=u.next()).done;)U(s.value)&&ge(s.value,n)}}}function an(e){{var n=e.type;if(n==null||typeof n==\"string\")return;var r;if(typeof n==\"function\")r=n.propTypes;else if(typeof n==\"object\"&&(n.$$typeof===T||n.$$typeof===C))r=n.propTypes;else return;if(r){var i=h(n);qe(r,e.props,\"prop\",i,e)}else if(n.PropTypes!==void 0&&!q){q=!0;var l=h(n);m(\"Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?\",l||\"Unknown\")}typeof n.getDefaultProps==\"function\"&&!n.getDefaultProps.isReactClassApproved&&m(\"getDefaultProps is only used on classic React.createClass definitions. Use a static property named `defaultProps` instead.\")}}function on(e){{for(var n=Object.keys(e.props),r=0;r<n.length;r++){var i=n[r];if(i!==\"children\"&&i!==\"key\"){E(e),m(\"Invalid prop `%s` supplied to `React.Fragment`. React.Fragment can only have `key` and `children` props.\",i),E(null);break}}e.ref!==null&&(E(e),m(\"Invalid attribute `ref` supplied to `React.Fragment`.\"),E(null))}}function sn(e,n,r,i,l,u){{var s=Ie(e);if(!s){var o=\"\";(e===void 0||typeof e==\"object\"&&e!==null&&Object.keys(e).length===0)&&(o+=\" You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.\");var b=rn(l);b?o+=b:o+=_e();var d;e===null?d=\"null\":W(e)?d=\"array\":e!==void 0&&e.$$typeof===t?(d=\"<\"+(h(e.type)||\"Unknown\")+\" />\",o=\" Did you accidentally export a JSX literal instead of a component?\"):d=typeof e,m(\"React.jsx: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: %s.%s\",d,o)}var f=nn(e,n,r,l,u);if(f==null)return f;if(s){var p=n.children;if(p!==void 0)if(i)if(W(p)){for(var w=0;w<p.length;w++)ve(p[w],e);Object.freeze&&Object.freeze(p)}else m(\"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\");else ve(p,e)}return e===N?on(f):an(f),f}}var ln=sn;B.Fragment=N,B.jsxDEV=ln})()});var Re=z((wn,ke)=>{\"use strict\";ke.exports=we()});var Nn={};bn(Nn,{default:()=>vn,frontmatter:()=>hn});var a=pn(Re()),hn={title:\"Termes et conditions | Kapreon\",description:\"Termes et conditions d'utilisation du site web de Kapreon.\",heroTitle:\"Termes et conditions\",heroSubtitle:\"L\\xE9gal\",lastUpdated:new Date(1739232e6)};function Te(c){let t=Object.assign({p:\"p\",h2:\"h2\",ul:\"ul\",li:\"li\",a:\"a\",h3:\"h3\"},c.components);return(0,a.jsxDEV)(a.Fragment,{children:[(0,a.jsxDEV)(t.p,{children:\"Kapreon est une entreprise immatricul\\xE9e au Canada. Les informations contenues sur ce site web sont la propri\\xE9t\\xE9 de Kapreon et sont prot\\xE9g\\xE9es par les lois sur les droits d'auteur et les marques en vigueur au Canada et au Qu\\xE9bec.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\",lineNumber:9,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.h2,{children:\"Responsable de la publication\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\",lineNumber:11,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.ul,{children:[`\n`,(0,a.jsxDEV)(t.li,{children:\"Kapreon Inc.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\",lineNumber:13,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.li,{children:\"Num\\xE9ro d'entreprise du Qu\\xE9bec (NEQ) : **********\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\",lineNumber:14,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.li,{children:\"Si\\xE8ge social: 100-50 Rue St-Charles O., Longueuil, J4H 1C6, Qu\\xE9bec, Canada\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\",lineNumber:15,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.li,{children:[\"Num\\xE9ro de t\\xE9l\\xE9phone : \",(0,a.jsxDEV)(t.a,{href:\"tel:+14388033053\",children:\"+****************\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\",lineNumber:16,columnNumber:25},this)]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\",lineNumber:16,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.li,{children:[\"Adresse courriel : \",(0,a.jsxDEV)(t.a,{href:\"mailto:<EMAIL>\",children:\"<EMAIL>\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\",lineNumber:17,columnNumber:22},this)]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\",lineNumber:17,columnNumber:1},this),`\n`]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\",lineNumber:13,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.h2,{children:\"Responsabilit\\xE9\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\",lineNumber:19,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:\"Les informations contenues sur ce site web sont fournies \\xE0 titre indicatif et ne sont pas destin\\xE9es \\xE0 \\xEAtre exhaustives. Kapreon ne peut garantir l'exactitude, la compl\\xE9tude ou l'actualit\\xE9 des informations diffus\\xE9es sur son site web.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\",lineNumber:21,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:\"En cons\\xE9quence, Kapreon d\\xE9cline toute responsabilit\\xE9 :\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\",lineNumber:23,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.ul,{children:[`\n`,(0,a.jsxDEV)(t.li,{children:\"pour toute impr\\xE9cision, inexactitude ou omission portant sur des informations disponibles sur le site web;\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\",lineNumber:25,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.li,{children:\"pour tous dommages r\\xE9sultant d'une intrusion frauduleuse d'un tiers ayant entra\\xEEn\\xE9 une modification des informations mises \\xE0 disposition sur le site web;\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\",lineNumber:26,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.li,{children:\"pour tous dommages, directs ou indirects, provoqu\\xE9s en raison de l'acc\\xE8s ou de l'utilisation du site.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\",lineNumber:27,columnNumber:1},this),`\n`]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\",lineNumber:25,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:\"Kapreon ne garantit pas un acc\\xE8s ininterrompu au site. Kapreon se r\\xE9serve le droit de suspendre l'acc\\xE8s pour maintenance ou toute autre raison technique.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\",lineNumber:29,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:\"Les liens hypertextes externes pr\\xE9sents sur le site ne sauraient engager la responsabilit\\xE9 de Kapreon.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\",lineNumber:31,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.h2,{children:\"Propri\\xE9t\\xE9 intellectuelle\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\",lineNumber:33,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:\"Le site peut contenir des marques, logos, images, vid\\xE9os, textes et autres contenus appartenant \\xE0 Kapreon ou \\xE0 des tiers. Kapreon d\\xE9tient les droits exclusifs sur certains contenus, tandis que d'autres sont la propri\\xE9t\\xE9 de partenaires, clients ou tiers et sont utilis\\xE9s avec leur autorisation ou dans le cadre de projets r\\xE9alis\\xE9s, fictifs ou non.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\",lineNumber:35,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:\"Toute reproduction, diffusion, modification ou r\\xE9utilisation, en tout ou en partie, des \\xE9l\\xE9ments pr\\xE9sents sur ce site est interdite sans l'autorisation expresse des titulaires des droits. L'usage des contenus du site, y compris les images et logos de tiers, ne conf\\xE8re aucun droit de propri\\xE9t\\xE9 ni d'exploitation aux utilisateurs.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\",lineNumber:37,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.h2,{children:\"Protection des donn\\xE9es personnelles\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\",lineNumber:39,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:\"Les donn\\xE9es personnelles collect\\xE9es sur le site sont trait\\xE9es conform\\xE9ment \\xE0 la Loi sur la protection des renseignements personnels et les documents \\xE9lectroniques (LPRPDE) du Canada et \\xE0 la Loi sur la protection des renseignements personnels dans le secteur priv\\xE9 du Qu\\xE9bec.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\",lineNumber:41,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.h3,{children:\"Finalit\\xE9 du traitement\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\",lineNumber:43,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:\"Les donn\\xE9es collect\\xE9es sont utilis\\xE9es exclusivement pour r\\xE9pondre aux demandes d'information et g\\xE9rer les inscriptions et services offerts sur le site.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\",lineNumber:45,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.h3,{children:\"Destinataire des donn\\xE9es\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\",lineNumber:47,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:\"Les donn\\xE9es sont destin\\xE9es \\xE0 un usage interne exclusivement et ne seront en aucun cas c\\xE9d\\xE9es, vendues ou lou\\xE9es \\xE0 des tiers.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\",lineNumber:49,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.h3,{children:\"S\\xE9curit\\xE9 des donn\\xE9es\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\",lineNumber:51,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:\"Kapreon met en \\u0153uvre des mesures de s\\xE9curit\\xE9 strictes pour prot\\xE9ger les informations personnelles contre tout acc\\xE8s non autoris\\xE9, perte ou modification. Les donn\\xE9es sont stock\\xE9es sur des serveurs s\\xE9curis\\xE9s et chiffr\\xE9es.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\",lineNumber:53,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.h3,{children:\"Dur\\xE9e de conservation\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\",lineNumber:55,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:\"Les donn\\xE9es sont conserv\\xE9es aussi longtemps que n\\xE9cessaire pour les finalit\\xE9s pr\\xE9vues, conform\\xE9ment \\xE0 la l\\xE9gislation en vigueur.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\",lineNumber:57,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.h3,{children:\"Droits des utilisateurs\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\",lineNumber:59,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:[\"Conform\\xE9ment \\xE0 la LPRPDE et \\xE0 la l\\xE9gislation qu\\xE9b\\xE9coise, vous b\\xE9n\\xE9ficiez d'un droit d'acc\\xE8s, de rectification et de suppression des donn\\xE9es vous concernant. Vous pouvez exercer ce droit en nous contactant \\xE0 \",(0,a.jsxDEV)(t.a,{href:\"mailto:<EMAIL>\",children:\"<EMAIL>\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\",lineNumber:61,columnNumber:208},this),\".\"]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\",lineNumber:61,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.h3,{children:\"Politique des cookies\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\",lineNumber:63,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:[\"Kapreon utilise des cookies pour am\\xE9liorer l'exp\\xE9rience utilisateur et analyser l'audience du site. Vous pouvez consulter notre \",(0,a.jsxDEV)(t.a,{href:\"/legal/cookies\",children:\"Politique de cookies\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\",lineNumber:65,columnNumber:129},this),\" pour plus d'informations sur leur utilisation et la gestion de vos pr\\xE9f\\xE9rences.\"]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\",lineNumber:65,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.h3,{children:\"Droit applicable et juridiction comp\\xE9tente\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\",lineNumber:67,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:\"Ce site est soumis aux lois canadiennes et qu\\xE9b\\xE9coises. En cas de litige, la juridiction comp\\xE9tente est celle des tribunaux du Qu\\xE9bec, district de Montr\\xE9al.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\",lineNumber:69,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:\"Ces mentions l\\xE9gales peuvent \\xEAtre modifi\\xE9es \\xE0 tout moment. Nous vous invitons donc \\xE0 les consulter r\\xE9guli\\xE8rement.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\",lineNumber:71,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:\"Derni\\xE8re mise \\xE0 jour : 11 f\\xE9vrier 2025\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\",lineNumber:73,columnNumber:1},this)]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\",lineNumber:1,columnNumber:1},this)}function gn(c={}){let{wrapper:t}=c.components||{};return t?(0,a.jsxDEV)(t,Object.assign({},c,{children:(0,a.jsxDEV)(Te,c,void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\"},this)}),void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\"},this):Te(c)}var vn=gn;return _n(Nn);})();\n/*! Bundled license information:\n\nreact/cjs/react-jsx-dev-runtime.development.js:\n  (**\n   * @license React\n   * react-jsx-dev-runtime.development.js\n   *\n   * Copyright (c) Facebook, Inc. and its affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *)\n*/\n;return Component;"}, "_id": "legal/fr/terms.mdx", "_raw": {"sourceFilePath": "legal/fr/terms.mdx", "sourceFileName": "terms.mdx", "sourceFileDir": "legal/fr", "contentType": "mdx", "flattenedPath": "legal/fr/terms"}, "type": "Legal", "locale": "fr", "slug": "terms"}]