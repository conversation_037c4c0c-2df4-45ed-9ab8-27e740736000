{"title": "Cookie Usage | Kapreon", "description": "Kapreon's cookie usage policy compliant with Canadian and Quebec laws.", "heroTitle": "<PERSON><PERSON>", "heroSubtitle": "Legal", "lastUpdated": "2025-02-11T00:00:00.000Z", "body": {"raw": "\nThis policy aims to inform users of our website about the use of connection tokens, commonly called \"cookies\", in accordance with Canadian and Quebec laws, including Quebec's Bill 25. By accessing our site, the user agrees to the use of cookies as described below.\n\n## What is a cookie?\n\nA cookie is a text file that the website sends to your browser and is then stored on a device. Cookies allow the site to recognize the user's device during subsequent visits, thus facilitating navigation and improving the user experience.\n\n## Types of cookies\n\n<PERSON><PERSON><PERSON><PERSON> uses analytical cookies to collect anonymous information about how visitors use <PERSON><PERSON><PERSON><PERSON>'s site. This data helps <PERSON><PERSON><PERSON><PERSON> improve the content and performance of the site.\n\n## Cookie retention period\n\nCookies are retained for a period proportional to their purpose.\n\nWhen first visiting <PERSON><PERSON><PERSON><PERSON>'s site, a banner informs the user about the use of cookies and invites the user to accept or refuse them. The user can modify their cookie preferences at any time by adjusting their browser settings.\n\n## User rights\n\nIn accordance with Quebec's Bill 25 and PIPEDA, the user has the following rights:\n\n- **Right of access**: The user may request access to personal information we hold about the user.\n- **Right of rectification**: The user may request correction of inaccurate or incomplete personal information.\n- **Right to withdraw consent**: The user may withdraw their consent to the use of their personal information, including cookies, at any time.\n\n## Policy modification\n\nKa<PERSON><PERSON><PERSON> reserves the right to modify this policy based on legislative or technical developments. <PERSON><PERSON><PERSON><PERSON> encourages users to regularly consult this page to stay informed of any changes.\n\nFor any questions or concerns regarding this policy or <PERSON><PERSON><PERSON><PERSON>'s personal information protection practices, please contact the agency at [<EMAIL>](mailto:<EMAIL>).\n\nThis policy is effective as of February 11, 2025.\n", "code": "var Component=(()=>{var sr=Object.create;var F=Object.defineProperty;var ur=Object.getOwnPropertyDescriptor;var cr=Object.getOwnPropertyNames;var fr=Object.getPrototypeOf,dr=Object.prototype.hasOwnProperty;var q=(c,t)=>()=>(t||c((t={exports:{}}).exports,t),t.exports),mr=(c,t)=>{for(var p in t)F(c,p,{get:t[p],enumerable:!0})},Ee=(c,t,p,y)=>{if(t&&typeof t==\"object\"||typeof t==\"function\")for(let _ of cr(t))!dr.call(c,_)&&_!==p&&F(c,_,{get:()=>t[_],enumerable:!(y=ur(t,_))||y.enumerable});return c};var br=(c,t,p)=>(p=c!=null?sr(fr(c)):{},Ee(t||!c||!c.__esModule?F(p,\"default\",{value:c,enumerable:!0}):p,c)),hr=c=>Ee(F({},\"__esModule\",{value:!0}),c);var xe=q((Er,we)=>{we.exports=React});var Re=q(z=>{\"use strict\";(function(){\"use strict\";var c=xe(),t=Symbol.for(\"react.element\"),p=Symbol.for(\"react.portal\"),y=Symbol.for(\"react.fragment\"),_=Symbol.for(\"react.strict_mode\"),G=Symbol.for(\"react.profiler\"),X=Symbol.for(\"react.provider\"),Q=Symbol.for(\"react.context\"),T=Symbol.for(\"react.forward_ref\"),A=Symbol.for(\"react.suspense\"),I=Symbol.for(\"react.suspense_list\"),C=Symbol.for(\"react.memo\"),W=Symbol.for(\"react.lazy\"),Ce=Symbol.for(\"react.offscreen\"),H=Symbol.iterator,Pe=\"@@iterator\";function Oe(e){if(e===null||typeof e!=\"object\")return null;var r=H&&e[H]||e[Pe];return typeof r==\"function\"?r:null}var w=c.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;function m(e){{for(var r=arguments.length,n=new Array(r>1?r-1:0),a=1;a<r;a++)n[a-1]=arguments[a];Se(\"error\",e,n)}}function Se(e,r,n){{var a=w.ReactDebugCurrentFrame,l=a.getStackAddendum();l!==\"\"&&(r+=\"%s\",n=n.concat([l]));var u=n.map(function(o){return String(o)});u.unshift(\"Warning: \"+r),Function.prototype.apply.call(console[e],console,u)}}var je=!1,De=!1,Fe=!1,Ae=!1,Ie=!1,J;J=Symbol.for(\"react.module.reference\");function We(e){return!!(typeof e==\"string\"||typeof e==\"function\"||e===y||e===G||Ie||e===_||e===A||e===I||Ae||e===Ce||je||De||Fe||typeof e==\"object\"&&e!==null&&(e.$$typeof===W||e.$$typeof===C||e.$$typeof===X||e.$$typeof===Q||e.$$typeof===T||e.$$typeof===J||e.getModuleId!==void 0))}function Ye(e,r,n){var a=e.displayName;if(a)return a;var l=r.displayName||r.name||\"\";return l!==\"\"?n+\"(\"+l+\")\":n}function Z(e){return e.displayName||\"Context\"}function g(e){if(e==null)return null;if(typeof e.tag==\"number\"&&m(\"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"),typeof e==\"function\")return e.displayName||e.name||null;if(typeof e==\"string\")return e;switch(e){case y:return\"Fragment\";case p:return\"Portal\";case G:return\"Profiler\";case _:return\"StrictMode\";case A:return\"Suspense\";case I:return\"SuspenseList\"}if(typeof e==\"object\")switch(e.$$typeof){case Q:var r=e;return Z(r)+\".Consumer\";case X:var n=e;return Z(n._context)+\".Provider\";case T:return Ye(e,e.render,\"ForwardRef\");case C:var a=e.displayName||null;return a!==null?a:g(e.type)||\"Memo\";case W:{var l=e,u=l._payload,o=l._init;try{return g(o(u))}catch{return null}}}return null}var E=Object.assign,N=0,ee,re,ne,te,ae,ie,oe;function le(){}le.__reactDisabledLog=!0;function $e(){{if(N===0){ee=console.log,re=console.info,ne=console.warn,te=console.error,ae=console.group,ie=console.groupCollapsed,oe=console.groupEnd;var e={configurable:!0,enumerable:!0,value:le,writable:!0};Object.defineProperties(console,{info:e,log:e,warn:e,error:e,group:e,groupCollapsed:e,groupEnd:e})}N++}}function Me(){{if(N--,N===0){var e={configurable:!0,enumerable:!0,writable:!0};Object.defineProperties(console,{log:E({},e,{value:ee}),info:E({},e,{value:re}),warn:E({},e,{value:ne}),error:E({},e,{value:te}),group:E({},e,{value:ae}),groupCollapsed:E({},e,{value:ie}),groupEnd:E({},e,{value:oe})})}N<0&&m(\"disabledDepth fell below zero. This is a bug in React. Please file an issue.\")}}var Y=w.ReactCurrentDispatcher,$;function P(e,r,n){{if($===void 0)try{throw Error()}catch(l){var a=l.stack.trim().match(/\\n( *(at )?)/);$=a&&a[1]||\"\"}return`\n`+$+e}}var M=!1,O;{var Ve=typeof WeakMap==\"function\"?WeakMap:Map;O=new Ve}function se(e,r){if(!e||M)return\"\";{var n=O.get(e);if(n!==void 0)return n}var a;M=!0;var l=Error.prepareStackTrace;Error.prepareStackTrace=void 0;var u;u=Y.current,Y.current=null,$e();try{if(r){var o=function(){throw Error()};if(Object.defineProperty(o.prototype,\"props\",{set:function(){throw Error()}}),typeof Reflect==\"object\"&&Reflect.construct){try{Reflect.construct(o,[])}catch(v){a=v}Reflect.construct(e,[],o)}else{try{o.call()}catch(v){a=v}e.call(o.prototype)}}else{try{throw Error()}catch(v){a=v}e()}}catch(v){if(v&&a&&typeof v.stack==\"string\"){for(var i=v.stack.split(`\n`),b=a.stack.split(`\n`),f=i.length-1,d=b.length-1;f>=1&&d>=0&&i[f]!==b[d];)d--;for(;f>=1&&d>=0;f--,d--)if(i[f]!==b[d]){if(f!==1||d!==1)do if(f--,d--,d<0||i[f]!==b[d]){var h=`\n`+i[f].replace(\" at new \",\" at \");return e.displayName&&h.includes(\"<anonymous>\")&&(h=h.replace(\"<anonymous>\",e.displayName)),typeof e==\"function\"&&O.set(e,h),h}while(f>=1&&d>=0);break}}}finally{M=!1,Y.current=u,Me(),Error.prepareStackTrace=l}var R=e?e.displayName||e.name:\"\",ye=R?P(R):\"\";return typeof e==\"function\"&&O.set(e,ye),ye}function Ue(e,r,n){return se(e,!1)}function Le(e){var r=e.prototype;return!!(r&&r.isReactComponent)}function S(e,r,n){if(e==null)return\"\";if(typeof e==\"function\")return se(e,Le(e));if(typeof e==\"string\")return P(e);switch(e){case A:return P(\"Suspense\");case I:return P(\"SuspenseList\")}if(typeof e==\"object\")switch(e.$$typeof){case T:return Ue(e.render);case C:return S(e.type,r,n);case W:{var a=e,l=a._payload,u=a._init;try{return S(u(l),r,n)}catch{}}}return\"\"}var j=Object.prototype.hasOwnProperty,ue={},ce=w.ReactDebugCurrentFrame;function D(e){if(e){var r=e._owner,n=S(e.type,e._source,r?r.type:null);ce.setExtraStackFrame(n)}else ce.setExtraStackFrame(null)}function Ke(e,r,n,a,l){{var u=Function.call.bind(j);for(var o in e)if(u(e,o)){var i=void 0;try{if(typeof e[o]!=\"function\"){var b=Error((a||\"React class\")+\": \"+n+\" type `\"+o+\"` is invalid; it must be a function, usually from the `prop-types` package, but received `\"+typeof e[o]+\"`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.\");throw b.name=\"Invariant Violation\",b}i=e[o](r,o,a,n,null,\"SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED\")}catch(f){i=f}i&&!(i instanceof Error)&&(D(l),m(\"%s: type specification of %s `%s` is invalid; the type checker function must return `null` or an `Error` but returned a %s. You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument).\",a||\"React class\",n,o,typeof i),D(null)),i instanceof Error&&!(i.message in ue)&&(ue[i.message]=!0,D(l),m(\"Failed %s type: %s\",n,i.message),D(null))}}}var Be=Array.isArray;function V(e){return Be(e)}function qe(e){{var r=typeof Symbol==\"function\"&&Symbol.toStringTag,n=r&&e[Symbol.toStringTag]||e.constructor.name||\"Object\";return n}}function ze(e){try{return fe(e),!1}catch{return!0}}function fe(e){return\"\"+e}function de(e){if(ze(e))return m(\"The provided key is an unsupported type %s. This value must be coerced to a string before before using it here.\",qe(e)),fe(e)}var k=w.ReactCurrentOwner,Ge={key:!0,ref:!0,__self:!0,__source:!0},me,be,U;U={};function Xe(e){if(j.call(e,\"ref\")){var r=Object.getOwnPropertyDescriptor(e,\"ref\").get;if(r&&r.isReactWarning)return!1}return e.ref!==void 0}function Qe(e){if(j.call(e,\"key\")){var r=Object.getOwnPropertyDescriptor(e,\"key\").get;if(r&&r.isReactWarning)return!1}return e.key!==void 0}function He(e,r){if(typeof e.ref==\"string\"&&k.current&&r&&k.current.stateNode!==r){var n=g(k.current.type);U[n]||(m('Component \"%s\" contains the string ref \"%s\". Support for string refs will be removed in a future major release. This case cannot be automatically converted to an arrow function. We ask you to manually fix this case by using useRef() or createRef() instead. Learn more about using refs safely here: https://reactjs.org/link/strict-mode-string-ref',g(k.current.type),e.ref),U[n]=!0)}}function Je(e,r){{var n=function(){me||(me=!0,m(\"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)\",r))};n.isReactWarning=!0,Object.defineProperty(e,\"key\",{get:n,configurable:!0})}}function Ze(e,r){{var n=function(){be||(be=!0,m(\"%s: `ref` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)\",r))};n.isReactWarning=!0,Object.defineProperty(e,\"ref\",{get:n,configurable:!0})}}var er=function(e,r,n,a,l,u,o){var i={$$typeof:t,type:e,key:r,ref:n,props:o,_owner:u};return i._store={},Object.defineProperty(i._store,\"validated\",{configurable:!1,enumerable:!1,writable:!0,value:!1}),Object.defineProperty(i,\"_self\",{configurable:!1,enumerable:!1,writable:!1,value:a}),Object.defineProperty(i,\"_source\",{configurable:!1,enumerable:!1,writable:!1,value:l}),Object.freeze&&(Object.freeze(i.props),Object.freeze(i)),i};function rr(e,r,n,a,l){{var u,o={},i=null,b=null;n!==void 0&&(de(n),i=\"\"+n),Qe(r)&&(de(r.key),i=\"\"+r.key),Xe(r)&&(b=r.ref,He(r,l));for(u in r)j.call(r,u)&&!Ge.hasOwnProperty(u)&&(o[u]=r[u]);if(e&&e.defaultProps){var f=e.defaultProps;for(u in f)o[u]===void 0&&(o[u]=f[u])}if(i||b){var d=typeof e==\"function\"?e.displayName||e.name||\"Unknown\":e;i&&Je(o,d),b&&Ze(o,d)}return er(e,i,b,l,a,k.current,o)}}var L=w.ReactCurrentOwner,he=w.ReactDebugCurrentFrame;function x(e){if(e){var r=e._owner,n=S(e.type,e._source,r?r.type:null);he.setExtraStackFrame(n)}else he.setExtraStackFrame(null)}var K;K=!1;function B(e){return typeof e==\"object\"&&e!==null&&e.$$typeof===t}function pe(){{if(L.current){var e=g(L.current.type);if(e)return`\n\nCheck the render method of \\``+e+\"`.\"}return\"\"}}function nr(e){{if(e!==void 0){var r=e.fileName.replace(/^.*[\\\\\\/]/,\"\"),n=e.lineNumber;return`\n\nCheck your code at `+r+\":\"+n+\".\"}return\"\"}}var ge={};function tr(e){{var r=pe();if(!r){var n=typeof e==\"string\"?e:e.displayName||e.name;n&&(r=`\n\nCheck the top-level render call using <`+n+\">.\")}return r}}function ve(e,r){{if(!e._store||e._store.validated||e.key!=null)return;e._store.validated=!0;var n=tr(r);if(ge[n])return;ge[n]=!0;var a=\"\";e&&e._owner&&e._owner!==L.current&&(a=\" It was passed a child from \"+g(e._owner.type)+\".\"),x(e),m('Each child in a list should have a unique \"key\" prop.%s%s See https://reactjs.org/link/warning-keys for more information.',n,a),x(null)}}function _e(e,r){{if(typeof e!=\"object\")return;if(V(e))for(var n=0;n<e.length;n++){var a=e[n];B(a)&&ve(a,r)}else if(B(e))e._store&&(e._store.validated=!0);else if(e){var l=Oe(e);if(typeof l==\"function\"&&l!==e.entries)for(var u=l.call(e),o;!(o=u.next()).done;)B(o.value)&&ve(o.value,r)}}}function ar(e){{var r=e.type;if(r==null||typeof r==\"string\")return;var n;if(typeof r==\"function\")n=r.propTypes;else if(typeof r==\"object\"&&(r.$$typeof===T||r.$$typeof===C))n=r.propTypes;else return;if(n){var a=g(r);Ke(n,e.props,\"prop\",a,e)}else if(r.PropTypes!==void 0&&!K){K=!0;var l=g(r);m(\"Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?\",l||\"Unknown\")}typeof r.getDefaultProps==\"function\"&&!r.getDefaultProps.isReactClassApproved&&m(\"getDefaultProps is only used on classic React.createClass definitions. Use a static property named `defaultProps` instead.\")}}function ir(e){{for(var r=Object.keys(e.props),n=0;n<r.length;n++){var a=r[n];if(a!==\"children\"&&a!==\"key\"){x(e),m(\"Invalid prop `%s` supplied to `React.Fragment`. React.Fragment can only have `key` and `children` props.\",a),x(null);break}}e.ref!==null&&(x(e),m(\"Invalid attribute `ref` supplied to `React.Fragment`.\"),x(null))}}function or(e,r,n,a,l,u){{var o=We(e);if(!o){var i=\"\";(e===void 0||typeof e==\"object\"&&e!==null&&Object.keys(e).length===0)&&(i+=\" You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.\");var b=nr(l);b?i+=b:i+=pe();var f;e===null?f=\"null\":V(e)?f=\"array\":e!==void 0&&e.$$typeof===t?(f=\"<\"+(g(e.type)||\"Unknown\")+\" />\",i=\" Did you accidentally export a JSX literal instead of a component?\"):f=typeof e,m(\"React.jsx: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: %s.%s\",f,i)}var d=rr(e,r,n,l,u);if(d==null)return d;if(o){var h=r.children;if(h!==void 0)if(a)if(V(h)){for(var R=0;R<h.length;R++)_e(h[R],e);Object.freeze&&Object.freeze(h)}else m(\"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\");else _e(h,e)}return e===y?ir(d):ar(d),d}}var lr=or;z.Fragment=y,z.jsxDEV=lr})()});var ke=q((xr,Ne)=>{\"use strict\";Ne.exports=Re()});var _r={};mr(_r,{default:()=>vr,frontmatter:()=>pr});var s=br(ke()),pr={title:\"Cookie Usage | Kapreon\",description:\"Kapreon's cookie usage policy compliant with Canadian and Quebec laws.\",heroTitle:\"Cookie Usage\",heroSubtitle:\"Legal\",lastUpdated:new Date(1739232e6)};function Te(c){let t=Object.assign({p:\"p\",h2:\"h2\",ul:\"ul\",li:\"li\",strong:\"strong\",a:\"a\"},c.components);return(0,s.jsxDEV)(s.Fragment,{children:[(0,s.jsxDEV)(t.p,{children:`This policy aims to inform users of our website about the use of connection tokens, commonly called \"cookies\", in accordance with Canadian and Quebec laws, including Quebec's Bill 25. By accessing our site, the user agrees to the use of cookies as described below.`},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-3c85fe02-d1b8-4129-acba-dfe65f774308.mdx\",lineNumber:9,columnNumber:1},this),`\n`,(0,s.jsxDEV)(t.h2,{children:\"What is a cookie?\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-3c85fe02-d1b8-4129-acba-dfe65f774308.mdx\",lineNumber:11,columnNumber:1},this),`\n`,(0,s.jsxDEV)(t.p,{children:\"A cookie is a text file that the website sends to your browser and is then stored on a device. Cookies allow the site to recognize the user's device during subsequent visits, thus facilitating navigation and improving the user experience.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-3c85fe02-d1b8-4129-acba-dfe65f774308.mdx\",lineNumber:13,columnNumber:1},this),`\n`,(0,s.jsxDEV)(t.h2,{children:\"Types of cookies\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-3c85fe02-d1b8-4129-acba-dfe65f774308.mdx\",lineNumber:15,columnNumber:1},this),`\n`,(0,s.jsxDEV)(t.p,{children:\"Kapreon uses analytical cookies to collect anonymous information about how visitors use Kapreon's site. This data helps Kapreon improve the content and performance of the site.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-3c85fe02-d1b8-4129-acba-dfe65f774308.mdx\",lineNumber:17,columnNumber:1},this),`\n`,(0,s.jsxDEV)(t.h2,{children:\"Cookie retention period\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-3c85fe02-d1b8-4129-acba-dfe65f774308.mdx\",lineNumber:19,columnNumber:1},this),`\n`,(0,s.jsxDEV)(t.p,{children:\"Cookies are retained for a period proportional to their purpose.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-3c85fe02-d1b8-4129-acba-dfe65f774308.mdx\",lineNumber:21,columnNumber:1},this),`\n`,(0,s.jsxDEV)(t.p,{children:\"When first visiting Kapreon's site, a banner informs the user about the use of cookies and invites the user to accept or refuse them. The user can modify their cookie preferences at any time by adjusting their browser settings.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-3c85fe02-d1b8-4129-acba-dfe65f774308.mdx\",lineNumber:23,columnNumber:1},this),`\n`,(0,s.jsxDEV)(t.h2,{children:\"User rights\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-3c85fe02-d1b8-4129-acba-dfe65f774308.mdx\",lineNumber:25,columnNumber:1},this),`\n`,(0,s.jsxDEV)(t.p,{children:\"In accordance with Quebec's Bill 25 and PIPEDA, the user has the following rights:\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-3c85fe02-d1b8-4129-acba-dfe65f774308.mdx\",lineNumber:27,columnNumber:1},this),`\n`,(0,s.jsxDEV)(t.ul,{children:[`\n`,(0,s.jsxDEV)(t.li,{children:[(0,s.jsxDEV)(t.strong,{children:\"Right of access\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-3c85fe02-d1b8-4129-acba-dfe65f774308.mdx\",lineNumber:29,columnNumber:3},this),\": The user may request access to personal information we hold about the user.\"]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-3c85fe02-d1b8-4129-acba-dfe65f774308.mdx\",lineNumber:29,columnNumber:1},this),`\n`,(0,s.jsxDEV)(t.li,{children:[(0,s.jsxDEV)(t.strong,{children:\"Right of rectification\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-3c85fe02-d1b8-4129-acba-dfe65f774308.mdx\",lineNumber:30,columnNumber:3},this),\": The user may request correction of inaccurate or incomplete personal information.\"]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-3c85fe02-d1b8-4129-acba-dfe65f774308.mdx\",lineNumber:30,columnNumber:1},this),`\n`,(0,s.jsxDEV)(t.li,{children:[(0,s.jsxDEV)(t.strong,{children:\"Right to withdraw consent\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-3c85fe02-d1b8-4129-acba-dfe65f774308.mdx\",lineNumber:31,columnNumber:3},this),\": The user may withdraw their consent to the use of their personal information, including cookies, at any time.\"]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-3c85fe02-d1b8-4129-acba-dfe65f774308.mdx\",lineNumber:31,columnNumber:1},this),`\n`]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-3c85fe02-d1b8-4129-acba-dfe65f774308.mdx\",lineNumber:29,columnNumber:1},this),`\n`,(0,s.jsxDEV)(t.h2,{children:\"Policy modification\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-3c85fe02-d1b8-4129-acba-dfe65f774308.mdx\",lineNumber:33,columnNumber:1},this),`\n`,(0,s.jsxDEV)(t.p,{children:\"Kapreon reserves the right to modify this policy based on legislative or technical developments. Kapreon encourages users to regularly consult this page to stay informed of any changes.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-3c85fe02-d1b8-4129-acba-dfe65f774308.mdx\",lineNumber:35,columnNumber:1},this),`\n`,(0,s.jsxDEV)(t.p,{children:[\"For any questions or concerns regarding this policy or Kapreon's personal information protection practices, please contact the agency at \",(0,s.jsxDEV)(t.a,{href:\"mailto:<EMAIL>\",children:\"<EMAIL>\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-3c85fe02-d1b8-4129-acba-dfe65f774308.mdx\",lineNumber:37,columnNumber:138},this),\".\"]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-3c85fe02-d1b8-4129-acba-dfe65f774308.mdx\",lineNumber:37,columnNumber:1},this),`\n`,(0,s.jsxDEV)(t.p,{children:\"This policy is effective as of February 11, 2025.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-3c85fe02-d1b8-4129-acba-dfe65f774308.mdx\",lineNumber:39,columnNumber:1},this)]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-3c85fe02-d1b8-4129-acba-dfe65f774308.mdx\",lineNumber:1,columnNumber:1},this)}function gr(c={}){let{wrapper:t}=c.components||{};return t?(0,s.jsxDEV)(t,Object.assign({},c,{children:(0,s.jsxDEV)(Te,c,void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-3c85fe02-d1b8-4129-acba-dfe65f774308.mdx\"},this)}),void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-3c85fe02-d1b8-4129-acba-dfe65f774308.mdx\"},this):Te(c)}var vr=gr;return hr(_r);})();\n/*! Bundled license information:\n\nreact/cjs/react-jsx-dev-runtime.development.js:\n  (**\n   * @license React\n   * react-jsx-dev-runtime.development.js\n   *\n   * Copyright (c) Facebook, Inc. and its affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *)\n*/\n;return Component;"}, "_id": "legal/en/cookies.mdx", "_raw": {"sourceFilePath": "legal/en/cookies.mdx", "sourceFileName": "cookies.mdx", "sourceFileDir": "legal/en", "contentType": "mdx", "flattenedPath": "legal/en/cookies"}, "type": "Legal", "locale": "en", "slug": "cookies"}