{"title": "Termes et conditions | Kapreon", "description": "Termes et conditions d'utilisation du site web de Kapreon.", "heroTitle": "Te<PERSON><PERSON> et conditions", "heroSubtitle": "Légal", "lastUpdated": "2025-02-11T00:00:00.000Z", "body": {"raw": "\nKapreon est une entreprise immatriculée au Canada. Les informations contenues sur ce site web sont la propriété de Kapreon et sont protégées par les lois sur les droits d'auteur et les marques en vigueur au Canada et au Québec.\n\n## Responsable de la publication\n\n- Kapreon Inc.\n- Numéro d'entreprise du Québec (NEQ) : 1175313031\n- Siège social: 100-50 Rue St-Charles O., Longueuil, J4H 1C6, Québec, Canada\n- Numéro de téléphone : [+****************](tel:+14388033053)\n- <PERSON>ress<PERSON> cour<PERSON> : [<EMAIL>](mailto:<EMAIL>)\n\n## Responsabilité\n\nLes informations contenues sur ce site web sont fournies à titre indicatif et ne sont pas destinées à être exhaustives. Kapreon ne peut garantir l'exactitude, la complétude ou l'actualité des informations diffusées sur son site web.\n\nEn conséquence, Kapreon décline toute responsabilité :\n\n- pour toute imprécision, inexactitude ou omission portant sur des informations disponibles sur le site web;\n- pour tous dommages résultant d'une intrusion frauduleuse d'un tiers ayant entraîné une modification des informations mises à disposition sur le site web;\n- pour tous dommages, directs ou indirects, provoqués en raison de l'accès ou de l'utilisation du site.\n\nKapreon ne garantit pas un accès ininterrompu au site. Kapreon se réserve le droit de suspendre l'accès pour maintenance ou toute autre raison technique.\n\nLes liens hypertextes externes présents sur le site ne sauraient engager la responsabilité de Kapreon.\n\n## Propriété intellectuelle\n\nLe site peut contenir des marques, logos, images, vidéos, textes et autres contenus appartenant à Kapreon ou à des tiers. Kapreon détient les droits exclusifs sur certains contenus, tandis que d'autres sont la propriété de partenaires, clients ou tiers et sont utilisés avec leur autorisation ou dans le cadre de projets réalisés, fictifs ou non.\n\nToute reproduction, diffusion, modification ou réutilisation, en tout ou en partie, des éléments présents sur ce site est interdite sans l'autorisation expresse des titulaires des droits. L'usage des contenus du site, y compris les images et logos de tiers, ne confère aucun droit de propriété ni d'exploitation aux utilisateurs.\n\n## Protection des données personnelles\n\nLes données personnelles collectées sur le site sont traitées conformément à la Loi sur la protection des renseignements personnels et les documents électroniques (LPRPDE) du Canada et à la Loi sur la protection des renseignements personnels dans le secteur privé du Québec.\n\n### Finalité du traitement\n\nLes données collectées sont utilisées exclusivement pour répondre aux demandes d'information et gérer les inscriptions et services offerts sur le site.\n\n### Destinataire des données\n\nLes données sont destinées à un usage interne exclusivement et ne seront en aucun cas cédées, vendues ou louées à des tiers.\n\n### Sécurité des données\n\nKapreon met en œuvre des mesures de sécurité strictes pour protéger les informations personnelles contre tout accès non autorisé, perte ou modification. Les données sont stockées sur des serveurs sécurisés et chiffrées.\n\n### Durée de conservation\n\nLes données sont conservées aussi longtemps que nécessaire pour les finalités prévues, conformément à la législation en vigueur.\n\n### Droits des utilisateurs\n\nConformément à la LPRPDE et à la législation québécoise, vous bénéficiez d'un droit d'accès, de rectification et de suppression des données vous concernant. Vous pouvez exercer ce droit en nous contactant à [<EMAIL>](mailto:<EMAIL>).\n\n### Politique des cookies\n\nKapreon utilise des cookies pour améliorer l'expérience utilisateur et analyser l'audience du site. Vous pouvez consulter notre [Politique de cookies](/legal/cookies) pour plus d'informations sur leur utilisation et la gestion de vos préférences.\n\n### Droit applicable et juridiction compétente\n\nCe site est soumis aux lois canadiennes et québécoises. En cas de litige, la juridiction compétente est celle des tribunaux du Québec, district de Montréal.\n\nCes mentions légales peuvent être modifiées à tout moment. Nous vous invitons donc à les consulter régulièrement.\n\nDernière mise à jour : 11 février 2025\n", "code": "var Component=(()=>{var un=Object.create;var F=Object.defineProperty;var cn=Object.getOwnPropertyDescriptor;var dn=Object.getOwnPropertyNames;var fn=Object.getPrototypeOf,mn=Object.prototype.hasOwnProperty;var z=(c,t)=>()=>(t||c((t={exports:{}}).exports,t),t.exports),bn=(c,t)=>{for(var _ in t)F(c,_,{get:t[_],enumerable:!0})},xe=(c,t,_,N)=>{if(t&&typeof t==\"object\"||typeof t==\"function\")for(let v of dn(t))!mn.call(c,v)&&v!==_&&F(c,v,{get:()=>t[v],enumerable:!(N=cn(t,v))||N.enumerable});return c};var pn=(c,t,_)=>(_=c!=null?un(fn(c)):{},xe(t||!c||!c.__esModule?F(_,\"default\",{value:c,enumerable:!0}):_,c)),_n=c=>xe(F({},\"__esModule\",{value:!0}),c);var Ee=z((yn,ye)=>{ye.exports=React});var we=z(B=>{\"use strict\";(function(){\"use strict\";var c=Ee(),t=Symbol.for(\"react.element\"),_=Symbol.for(\"react.portal\"),N=Symbol.for(\"react.fragment\"),v=Symbol.for(\"react.strict_mode\"),G=Symbol.for(\"react.profiler\"),Q=Symbol.for(\"react.provider\"),X=Symbol.for(\"react.context\"),T=Symbol.for(\"react.forward_ref\"),A=Symbol.for(\"react.suspense\"),L=Symbol.for(\"react.suspense_list\"),C=Symbol.for(\"react.memo\"),I=Symbol.for(\"react.lazy\"),Ce=Symbol.for(\"react.offscreen\"),H=Symbol.iterator,Pe=\"@@iterator\";function Oe(e){if(e===null||typeof e!=\"object\")return null;var n=H&&e[H]||e[Pe];return typeof n==\"function\"?n:null}var y=c.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;function m(e){{for(var n=arguments.length,r=new Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];Se(\"error\",e,r)}}function Se(e,n,r){{var i=y.ReactDebugCurrentFrame,l=i.getStackAddendum();l!==\"\"&&(n+=\"%s\",r=r.concat([l]));var u=r.map(function(s){return String(s)});u.unshift(\"Warning: \"+n),Function.prototype.apply.call(console[e],console,u)}}var je=!1,De=!1,Fe=!1,Ae=!1,Le=!1,J;J=Symbol.for(\"react.module.reference\");function Ie(e){return!!(typeof e==\"string\"||typeof e==\"function\"||e===N||e===G||Le||e===v||e===A||e===L||Ae||e===Ce||je||De||Fe||typeof e==\"object\"&&e!==null&&(e.$$typeof===I||e.$$typeof===C||e.$$typeof===Q||e.$$typeof===X||e.$$typeof===T||e.$$typeof===J||e.getModuleId!==void 0))}function Ye(e,n,r){var i=e.displayName;if(i)return i;var l=n.displayName||n.name||\"\";return l!==\"\"?r+\"(\"+l+\")\":r}function Z(e){return e.displayName||\"Context\"}function h(e){if(e==null)return null;if(typeof e.tag==\"number\"&&m(\"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"),typeof e==\"function\")return e.displayName||e.name||null;if(typeof e==\"string\")return e;switch(e){case N:return\"Fragment\";case _:return\"Portal\";case G:return\"Profiler\";case v:return\"StrictMode\";case A:return\"Suspense\";case L:return\"SuspenseList\"}if(typeof e==\"object\")switch(e.$$typeof){case X:var n=e;return Z(n)+\".Consumer\";case Q:var r=e;return Z(r._context)+\".Provider\";case T:return Ye(e,e.render,\"ForwardRef\");case C:var i=e.displayName||null;return i!==null?i:h(e.type)||\"Memo\";case I:{var l=e,u=l._payload,s=l._init;try{return h(s(u))}catch{return null}}}return null}var x=Object.assign,k=0,ee,ne,re,te,ae,ie,oe;function se(){}se.__reactDisabledLog=!0;function $e(){{if(k===0){ee=console.log,ne=console.info,re=console.warn,te=console.error,ae=console.group,ie=console.groupCollapsed,oe=console.groupEnd;var e={configurable:!0,enumerable:!0,value:se,writable:!0};Object.defineProperties(console,{info:e,log:e,warn:e,error:e,group:e,groupCollapsed:e,groupEnd:e})}k++}}function Ve(){{if(k--,k===0){var e={configurable:!0,enumerable:!0,writable:!0};Object.defineProperties(console,{log:x({},e,{value:ee}),info:x({},e,{value:ne}),warn:x({},e,{value:re}),error:x({},e,{value:te}),group:x({},e,{value:ae}),groupCollapsed:x({},e,{value:ie}),groupEnd:x({},e,{value:oe})})}k<0&&m(\"disabledDepth fell below zero. This is a bug in React. Please file an issue.\")}}var Y=y.ReactCurrentDispatcher,$;function P(e,n,r){{if($===void 0)try{throw Error()}catch(l){var i=l.stack.trim().match(/\\n( *(at )?)/);$=i&&i[1]||\"\"}return`\n`+$+e}}var V=!1,O;{var We=typeof WeakMap==\"function\"?WeakMap:Map;O=new We}function le(e,n){if(!e||V)return\"\";{var r=O.get(e);if(r!==void 0)return r}var i;V=!0;var l=Error.prepareStackTrace;Error.prepareStackTrace=void 0;var u;u=Y.current,Y.current=null,$e();try{if(n){var s=function(){throw Error()};if(Object.defineProperty(s.prototype,\"props\",{set:function(){throw Error()}}),typeof Reflect==\"object\"&&Reflect.construct){try{Reflect.construct(s,[])}catch(g){i=g}Reflect.construct(e,[],s)}else{try{s.call()}catch(g){i=g}e.call(s.prototype)}}else{try{throw Error()}catch(g){i=g}e()}}catch(g){if(g&&i&&typeof g.stack==\"string\"){for(var o=g.stack.split(`\n`),b=i.stack.split(`\n`),d=o.length-1,f=b.length-1;d>=1&&f>=0&&o[d]!==b[f];)f--;for(;d>=1&&f>=0;d--,f--)if(o[d]!==b[f]){if(d!==1||f!==1)do if(d--,f--,f<0||o[d]!==b[f]){var p=`\n`+o[d].replace(\" at new \",\" at \");return e.displayName&&p.includes(\"<anonymous>\")&&(p=p.replace(\"<anonymous>\",e.displayName)),typeof e==\"function\"&&O.set(e,p),p}while(d>=1&&f>=0);break}}}finally{V=!1,Y.current=u,Ve(),Error.prepareStackTrace=l}var w=e?e.displayName||e.name:\"\",Ne=w?P(w):\"\";return typeof e==\"function\"&&O.set(e,Ne),Ne}function Me(e,n,r){return le(e,!1)}function Ke(e){var n=e.prototype;return!!(n&&n.isReactComponent)}function S(e,n,r){if(e==null)return\"\";if(typeof e==\"function\")return le(e,Ke(e));if(typeof e==\"string\")return P(e);switch(e){case A:return P(\"Suspense\");case L:return P(\"SuspenseList\")}if(typeof e==\"object\")switch(e.$$typeof){case T:return Me(e.render);case C:return S(e.type,n,r);case I:{var i=e,l=i._payload,u=i._init;try{return S(u(l),n,r)}catch{}}}return\"\"}var j=Object.prototype.hasOwnProperty,ue={},ce=y.ReactDebugCurrentFrame;function D(e){if(e){var n=e._owner,r=S(e.type,e._source,n?n.type:null);ce.setExtraStackFrame(r)}else ce.setExtraStackFrame(null)}function qe(e,n,r,i,l){{var u=Function.call.bind(j);for(var s in e)if(u(e,s)){var o=void 0;try{if(typeof e[s]!=\"function\"){var b=Error((i||\"React class\")+\": \"+r+\" type `\"+s+\"` is invalid; it must be a function, usually from the `prop-types` package, but received `\"+typeof e[s]+\"`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.\");throw b.name=\"Invariant Violation\",b}o=e[s](n,s,i,r,null,\"SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED\")}catch(d){o=d}o&&!(o instanceof Error)&&(D(l),m(\"%s: type specification of %s `%s` is invalid; the type checker function must return `null` or an `Error` but returned a %s. You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument).\",i||\"React class\",r,s,typeof o),D(null)),o instanceof Error&&!(o.message in ue)&&(ue[o.message]=!0,D(l),m(\"Failed %s type: %s\",r,o.message),D(null))}}}var Ue=Array.isArray;function W(e){return Ue(e)}function ze(e){{var n=typeof Symbol==\"function\"&&Symbol.toStringTag,r=n&&e[Symbol.toStringTag]||e.constructor.name||\"Object\";return r}}function Be(e){try{return de(e),!1}catch{return!0}}function de(e){return\"\"+e}function fe(e){if(Be(e))return m(\"The provided key is an unsupported type %s. This value must be coerced to a string before before using it here.\",ze(e)),de(e)}var R=y.ReactCurrentOwner,Ge={key:!0,ref:!0,__self:!0,__source:!0},me,be,M;M={};function Qe(e){if(j.call(e,\"ref\")){var n=Object.getOwnPropertyDescriptor(e,\"ref\").get;if(n&&n.isReactWarning)return!1}return e.ref!==void 0}function Xe(e){if(j.call(e,\"key\")){var n=Object.getOwnPropertyDescriptor(e,\"key\").get;if(n&&n.isReactWarning)return!1}return e.key!==void 0}function He(e,n){if(typeof e.ref==\"string\"&&R.current&&n&&R.current.stateNode!==n){var r=h(R.current.type);M[r]||(m('Component \"%s\" contains the string ref \"%s\". Support for string refs will be removed in a future major release. This case cannot be automatically converted to an arrow function. We ask you to manually fix this case by using useRef() or createRef() instead. Learn more about using refs safely here: https://reactjs.org/link/strict-mode-string-ref',h(R.current.type),e.ref),M[r]=!0)}}function Je(e,n){{var r=function(){me||(me=!0,m(\"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)\",n))};r.isReactWarning=!0,Object.defineProperty(e,\"key\",{get:r,configurable:!0})}}function Ze(e,n){{var r=function(){be||(be=!0,m(\"%s: `ref` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)\",n))};r.isReactWarning=!0,Object.defineProperty(e,\"ref\",{get:r,configurable:!0})}}var en=function(e,n,r,i,l,u,s){var o={$$typeof:t,type:e,key:n,ref:r,props:s,_owner:u};return o._store={},Object.defineProperty(o._store,\"validated\",{configurable:!1,enumerable:!1,writable:!0,value:!1}),Object.defineProperty(o,\"_self\",{configurable:!1,enumerable:!1,writable:!1,value:i}),Object.defineProperty(o,\"_source\",{configurable:!1,enumerable:!1,writable:!1,value:l}),Object.freeze&&(Object.freeze(o.props),Object.freeze(o)),o};function nn(e,n,r,i,l){{var u,s={},o=null,b=null;r!==void 0&&(fe(r),o=\"\"+r),Xe(n)&&(fe(n.key),o=\"\"+n.key),Qe(n)&&(b=n.ref,He(n,l));for(u in n)j.call(n,u)&&!Ge.hasOwnProperty(u)&&(s[u]=n[u]);if(e&&e.defaultProps){var d=e.defaultProps;for(u in d)s[u]===void 0&&(s[u]=d[u])}if(o||b){var f=typeof e==\"function\"?e.displayName||e.name||\"Unknown\":e;o&&Je(s,f),b&&Ze(s,f)}return en(e,o,b,l,i,R.current,s)}}var K=y.ReactCurrentOwner,pe=y.ReactDebugCurrentFrame;function E(e){if(e){var n=e._owner,r=S(e.type,e._source,n?n.type:null);pe.setExtraStackFrame(r)}else pe.setExtraStackFrame(null)}var q;q=!1;function U(e){return typeof e==\"object\"&&e!==null&&e.$$typeof===t}function _e(){{if(K.current){var e=h(K.current.type);if(e)return`\n\nCheck the render method of \\``+e+\"`.\"}return\"\"}}function rn(e){{if(e!==void 0){var n=e.fileName.replace(/^.*[\\\\\\/]/,\"\"),r=e.lineNumber;return`\n\nCheck your code at `+n+\":\"+r+\".\"}return\"\"}}var he={};function tn(e){{var n=_e();if(!n){var r=typeof e==\"string\"?e:e.displayName||e.name;r&&(n=`\n\nCheck the top-level render call using <`+r+\">.\")}return n}}function ge(e,n){{if(!e._store||e._store.validated||e.key!=null)return;e._store.validated=!0;var r=tn(n);if(he[r])return;he[r]=!0;var i=\"\";e&&e._owner&&e._owner!==K.current&&(i=\" It was passed a child from \"+h(e._owner.type)+\".\"),E(e),m('Each child in a list should have a unique \"key\" prop.%s%s See https://reactjs.org/link/warning-keys for more information.',r,i),E(null)}}function ve(e,n){{if(typeof e!=\"object\")return;if(W(e))for(var r=0;r<e.length;r++){var i=e[r];U(i)&&ge(i,n)}else if(U(e))e._store&&(e._store.validated=!0);else if(e){var l=Oe(e);if(typeof l==\"function\"&&l!==e.entries)for(var u=l.call(e),s;!(s=u.next()).done;)U(s.value)&&ge(s.value,n)}}}function an(e){{var n=e.type;if(n==null||typeof n==\"string\")return;var r;if(typeof n==\"function\")r=n.propTypes;else if(typeof n==\"object\"&&(n.$$typeof===T||n.$$typeof===C))r=n.propTypes;else return;if(r){var i=h(n);qe(r,e.props,\"prop\",i,e)}else if(n.PropTypes!==void 0&&!q){q=!0;var l=h(n);m(\"Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?\",l||\"Unknown\")}typeof n.getDefaultProps==\"function\"&&!n.getDefaultProps.isReactClassApproved&&m(\"getDefaultProps is only used on classic React.createClass definitions. Use a static property named `defaultProps` instead.\")}}function on(e){{for(var n=Object.keys(e.props),r=0;r<n.length;r++){var i=n[r];if(i!==\"children\"&&i!==\"key\"){E(e),m(\"Invalid prop `%s` supplied to `React.Fragment`. React.Fragment can only have `key` and `children` props.\",i),E(null);break}}e.ref!==null&&(E(e),m(\"Invalid attribute `ref` supplied to `React.Fragment`.\"),E(null))}}function sn(e,n,r,i,l,u){{var s=Ie(e);if(!s){var o=\"\";(e===void 0||typeof e==\"object\"&&e!==null&&Object.keys(e).length===0)&&(o+=\" You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.\");var b=rn(l);b?o+=b:o+=_e();var d;e===null?d=\"null\":W(e)?d=\"array\":e!==void 0&&e.$$typeof===t?(d=\"<\"+(h(e.type)||\"Unknown\")+\" />\",o=\" Did you accidentally export a JSX literal instead of a component?\"):d=typeof e,m(\"React.jsx: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: %s.%s\",d,o)}var f=nn(e,n,r,l,u);if(f==null)return f;if(s){var p=n.children;if(p!==void 0)if(i)if(W(p)){for(var w=0;w<p.length;w++)ve(p[w],e);Object.freeze&&Object.freeze(p)}else m(\"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\");else ve(p,e)}return e===N?on(f):an(f),f}}var ln=sn;B.Fragment=N,B.jsxDEV=ln})()});var Re=z((wn,ke)=>{\"use strict\";ke.exports=we()});var Nn={};bn(Nn,{default:()=>vn,frontmatter:()=>hn});var a=pn(Re()),hn={title:\"Termes et conditions | Kapreon\",description:\"Termes et conditions d'utilisation du site web de Kapreon.\",heroTitle:\"Termes et conditions\",heroSubtitle:\"L\\xE9gal\",lastUpdated:new Date(1739232e6)};function Te(c){let t=Object.assign({p:\"p\",h2:\"h2\",ul:\"ul\",li:\"li\",a:\"a\",h3:\"h3\"},c.components);return(0,a.jsxDEV)(a.Fragment,{children:[(0,a.jsxDEV)(t.p,{children:\"Kapreon est une entreprise immatricul\\xE9e au Canada. Les informations contenues sur ce site web sont la propri\\xE9t\\xE9 de Kapreon et sont prot\\xE9g\\xE9es par les lois sur les droits d'auteur et les marques en vigueur au Canada et au Qu\\xE9bec.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\",lineNumber:9,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.h2,{children:\"Responsable de la publication\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\",lineNumber:11,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.ul,{children:[`\n`,(0,a.jsxDEV)(t.li,{children:\"Kapreon Inc.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\",lineNumber:13,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.li,{children:\"Num\\xE9ro d'entreprise du Qu\\xE9bec (NEQ) : 1175313031\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\",lineNumber:14,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.li,{children:\"Si\\xE8ge social: 100-50 Rue St-Charles O., Longueuil, J4H 1C6, Qu\\xE9bec, Canada\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\",lineNumber:15,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.li,{children:[\"Num\\xE9ro de t\\xE9l\\xE9phone : \",(0,a.jsxDEV)(t.a,{href:\"tel:+14388033053\",children:\"+****************\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\",lineNumber:16,columnNumber:25},this)]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\",lineNumber:16,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.li,{children:[\"Adresse courriel : \",(0,a.jsxDEV)(t.a,{href:\"mailto:<EMAIL>\",children:\"<EMAIL>\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\",lineNumber:17,columnNumber:22},this)]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\",lineNumber:17,columnNumber:1},this),`\n`]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\",lineNumber:13,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.h2,{children:\"Responsabilit\\xE9\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\",lineNumber:19,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:\"Les informations contenues sur ce site web sont fournies \\xE0 titre indicatif et ne sont pas destin\\xE9es \\xE0 \\xEAtre exhaustives. Kapreon ne peut garantir l'exactitude, la compl\\xE9tude ou l'actualit\\xE9 des informations diffus\\xE9es sur son site web.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\",lineNumber:21,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:\"En cons\\xE9quence, Kapreon d\\xE9cline toute responsabilit\\xE9 :\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\",lineNumber:23,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.ul,{children:[`\n`,(0,a.jsxDEV)(t.li,{children:\"pour toute impr\\xE9cision, inexactitude ou omission portant sur des informations disponibles sur le site web;\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\",lineNumber:25,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.li,{children:\"pour tous dommages r\\xE9sultant d'une intrusion frauduleuse d'un tiers ayant entra\\xEEn\\xE9 une modification des informations mises \\xE0 disposition sur le site web;\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\",lineNumber:26,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.li,{children:\"pour tous dommages, directs ou indirects, provoqu\\xE9s en raison de l'acc\\xE8s ou de l'utilisation du site.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\",lineNumber:27,columnNumber:1},this),`\n`]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\",lineNumber:25,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:\"Kapreon ne garantit pas un acc\\xE8s ininterrompu au site. Kapreon se r\\xE9serve le droit de suspendre l'acc\\xE8s pour maintenance ou toute autre raison technique.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\",lineNumber:29,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:\"Les liens hypertextes externes pr\\xE9sents sur le site ne sauraient engager la responsabilit\\xE9 de Kapreon.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\",lineNumber:31,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.h2,{children:\"Propri\\xE9t\\xE9 intellectuelle\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\",lineNumber:33,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:\"Le site peut contenir des marques, logos, images, vid\\xE9os, textes et autres contenus appartenant \\xE0 Kapreon ou \\xE0 des tiers. Kapreon d\\xE9tient les droits exclusifs sur certains contenus, tandis que d'autres sont la propri\\xE9t\\xE9 de partenaires, clients ou tiers et sont utilis\\xE9s avec leur autorisation ou dans le cadre de projets r\\xE9alis\\xE9s, fictifs ou non.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\",lineNumber:35,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:\"Toute reproduction, diffusion, modification ou r\\xE9utilisation, en tout ou en partie, des \\xE9l\\xE9ments pr\\xE9sents sur ce site est interdite sans l'autorisation expresse des titulaires des droits. L'usage des contenus du site, y compris les images et logos de tiers, ne conf\\xE8re aucun droit de propri\\xE9t\\xE9 ni d'exploitation aux utilisateurs.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\",lineNumber:37,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.h2,{children:\"Protection des donn\\xE9es personnelles\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\",lineNumber:39,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:\"Les donn\\xE9es personnelles collect\\xE9es sur le site sont trait\\xE9es conform\\xE9ment \\xE0 la Loi sur la protection des renseignements personnels et les documents \\xE9lectroniques (LPRPDE) du Canada et \\xE0 la Loi sur la protection des renseignements personnels dans le secteur priv\\xE9 du Qu\\xE9bec.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\",lineNumber:41,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.h3,{children:\"Finalit\\xE9 du traitement\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\",lineNumber:43,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:\"Les donn\\xE9es collect\\xE9es sont utilis\\xE9es exclusivement pour r\\xE9pondre aux demandes d'information et g\\xE9rer les inscriptions et services offerts sur le site.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\",lineNumber:45,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.h3,{children:\"Destinataire des donn\\xE9es\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\",lineNumber:47,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:\"Les donn\\xE9es sont destin\\xE9es \\xE0 un usage interne exclusivement et ne seront en aucun cas c\\xE9d\\xE9es, vendues ou lou\\xE9es \\xE0 des tiers.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\",lineNumber:49,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.h3,{children:\"S\\xE9curit\\xE9 des donn\\xE9es\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\",lineNumber:51,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:\"Kapreon met en \\u0153uvre des mesures de s\\xE9curit\\xE9 strictes pour prot\\xE9ger les informations personnelles contre tout acc\\xE8s non autoris\\xE9, perte ou modification. Les donn\\xE9es sont stock\\xE9es sur des serveurs s\\xE9curis\\xE9s et chiffr\\xE9es.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\",lineNumber:53,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.h3,{children:\"Dur\\xE9e de conservation\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\",lineNumber:55,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:\"Les donn\\xE9es sont conserv\\xE9es aussi longtemps que n\\xE9cessaire pour les finalit\\xE9s pr\\xE9vues, conform\\xE9ment \\xE0 la l\\xE9gislation en vigueur.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\",lineNumber:57,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.h3,{children:\"Droits des utilisateurs\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\",lineNumber:59,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:[\"Conform\\xE9ment \\xE0 la LPRPDE et \\xE0 la l\\xE9gislation qu\\xE9b\\xE9coise, vous b\\xE9n\\xE9ficiez d'un droit d'acc\\xE8s, de rectification et de suppression des donn\\xE9es vous concernant. Vous pouvez exercer ce droit en nous contactant \\xE0 \",(0,a.jsxDEV)(t.a,{href:\"mailto:<EMAIL>\",children:\"<EMAIL>\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\",lineNumber:61,columnNumber:208},this),\".\"]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\",lineNumber:61,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.h3,{children:\"Politique des cookies\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\",lineNumber:63,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:[\"Kapreon utilise des cookies pour am\\xE9liorer l'exp\\xE9rience utilisateur et analyser l'audience du site. Vous pouvez consulter notre \",(0,a.jsxDEV)(t.a,{href:\"/legal/cookies\",children:\"Politique de cookies\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\",lineNumber:65,columnNumber:129},this),\" pour plus d'informations sur leur utilisation et la gestion de vos pr\\xE9f\\xE9rences.\"]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\",lineNumber:65,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.h3,{children:\"Droit applicable et juridiction comp\\xE9tente\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\",lineNumber:67,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:\"Ce site est soumis aux lois canadiennes et qu\\xE9b\\xE9coises. En cas de litige, la juridiction comp\\xE9tente est celle des tribunaux du Qu\\xE9bec, district de Montr\\xE9al.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\",lineNumber:69,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:\"Ces mentions l\\xE9gales peuvent \\xEAtre modifi\\xE9es \\xE0 tout moment. Nous vous invitons donc \\xE0 les consulter r\\xE9guli\\xE8rement.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\",lineNumber:71,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:\"Derni\\xE8re mise \\xE0 jour : 11 f\\xE9vrier 2025\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\",lineNumber:73,columnNumber:1},this)]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\",lineNumber:1,columnNumber:1},this)}function gn(c={}){let{wrapper:t}=c.components||{};return t?(0,a.jsxDEV)(t,Object.assign({},c,{children:(0,a.jsxDEV)(Te,c,void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\"},this)}),void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-7b88fb5d-edf8-4a2a-b5ca-4cda85d26303.mdx\"},this):Te(c)}var vn=gn;return _n(Nn);})();\n/*! Bundled license information:\n\nreact/cjs/react-jsx-dev-runtime.development.js:\n  (**\n   * @license React\n   * react-jsx-dev-runtime.development.js\n   *\n   * Copyright (c) Facebook, Inc. and its affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *)\n*/\n;return Component;"}, "_id": "legal/fr/terms.mdx", "_raw": {"sourceFilePath": "legal/fr/terms.mdx", "sourceFileName": "terms.mdx", "sourceFileDir": "legal/fr", "contentType": "mdx", "flattenedPath": "legal/fr/terms"}, "type": "Legal", "locale": "fr", "slug": "terms"}