// NOTE This file is auto-generated by Contentlayer

import legal__en__cookiesMdx from './legal__en__cookies.mdx.json' assert { type: 'json' }
import legal__en__termsMdx from './legal__en__terms.mdx.json' assert { type: 'json' }
import legal__fr__cookiesMdx from './legal__fr__cookies.mdx.json' assert { type: 'json' }
import legal__fr__termsMdx from './legal__fr__terms.mdx.json' assert { type: 'json' }

export const allLegals = [legal__en__cookiesMdx, legal__en__termsMdx, legal__fr__cookiesMdx, legal__fr__termsMdx]
