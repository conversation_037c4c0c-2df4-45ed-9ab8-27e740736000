{"title": "Terms and Conditions | Kapreon", "description": "Terms and conditions of use for Kapreon's website.", "heroTitle": "Terms and Conditions", "heroSubtitle": "Legal", "lastUpdated": "2025-02-11T00:00:00.000Z", "body": {"raw": "\nKapreon is a company incorporated in Canada. The information contained on this website is the property of <PERSON><PERSON><PERSON><PERSON> and is protected by copyright and trademark laws in force in Canada and Quebec.\n\n## Publication Manager\n\n- Kapreon Inc.\n- Quebec Enterprise Number (NEQ): 1175313031\n- Head office: 100-50 Rue St-Charles O., Longueuil, J4H 1C6, Quebec, Canada\n- Phone number: [+****************](tel:+14388033053)\n- Email address: [<EMAIL>](mailto:<EMAIL>)\n\n## Liability\n\nThe information contained on this website is provided for informational purposes and is not intended to be exhaustive. Kapreon cannot guarantee the accuracy, completeness or timeliness of information published on its website.\n\nConsequently, Kapreon disclaims all liability:\n\n- for any inaccuracy, inexactitude or omission relating to information available on the website;\n- for all damages resulting from fraudulent intrusion by a third party that led to modification of information made available on the website;\n- for all damages, direct or indirect, caused due to access to or use of the site.\n\nKapreon does not guarantee uninterrupted access to the site. Kapreon reserves the right to suspend access for maintenance or any other technical reason.\n\nExternal hyperlinks present on the site cannot engage <PERSON><PERSON><PERSON><PERSON>'s liability.\n\n## Intellectual Property\n\nThe site may contain trademarks, logos, images, videos, texts and other content belonging to <PERSON><PERSON><PERSON><PERSON> or third parties. Ka<PERSON>reon holds exclusive rights to certain content, while others are the property of partners, clients or third parties and are used with their authorization or as part of completed projects, fictional or not.\n\nAny reproduction, distribution, modification or reuse, in whole or in part, of elements present on this site is prohibited without express authorization from rights holders. Use of site content, including third-party images and logos, does not confer any property or exploitation rights to users.\n\n## Personal Data Protection\n\nPersonal data collected on the site is processed in accordance with Canada's Personal Information Protection and Electronic Documents Act (PIPEDA) and Quebec's Act respecting the protection of personal information in the private sector.\n\n### Processing Purpose\n\nData collected is used exclusively to respond to information requests and manage registrations and services offered on the site.\n\n### Data Recipients\n\nData is intended for internal use exclusively and will under no circumstances be transferred, sold or rented to third parties.\n\n### Data Security\n\nKapreon implements strict security measures to protect personal information against unauthorized access, loss or modification. Data is stored on secure and encrypted servers.\n\n### Retention Period\n\nData is retained as long as necessary for intended purposes, in accordance with applicable legislation.\n\n### User Rights\n\nIn accordance with PIPEDA and Quebec legislation, you have the right to access, rectify and delete data concerning you. You can exercise this right by contacting us at [<EMAIL>](mailto:<EMAIL>).\n\n### Cookie Policy\n\nKapreon uses cookies to improve user experience and analyze site audience. You can consult our [Cookie Policy](/legal/cookies) for more information on their use and managing your preferences.\n\n### Applicable Law and Competent Jurisdiction\n\nThis site is subject to Canadian and Quebec laws. In case of dispute, the competent jurisdiction is that of Quebec courts, Montreal district.\n\nThese legal notices may be modified at any time. We therefore invite you to consult them regularly.\n\nLast update: February 11, 2025\n", "code": "var Component=(()=>{var sn=Object.create;var A=Object.defineProperty;var un=Object.getOwnPropertyDescriptor;var fn=Object.getOwnPropertyNames;var dn=Object.getPrototypeOf,mn=Object.prototype.hasOwnProperty;var B=(u,t)=>()=>(t||u((t={exports:{}}).exports,t),t.exports),bn=(u,t)=>{for(var p in t)A(u,p,{get:t[p],enumerable:!0})},Ne=(u,t,p,y)=>{if(t&&typeof t==\"object\"||typeof t==\"function\")for(let v of fn(t))!mn.call(u,v)&&v!==p&&A(u,v,{get:()=>t[v],enumerable:!(y=un(t,v))||y.enumerable});return u};var hn=(u,t,p)=>(p=u!=null?sn(dn(u)):{},Ne(t||!u||!u.__esModule?A(p,\"default\",{value:u,enumerable:!0}):p,u)),pn=u=>Ne(A({},\"__esModule\",{value:!0}),u);var we=B((xn,xe)=>{xe.exports=React});var Ee=B(Q=>{\"use strict\";(function(){\"use strict\";var u=we(),t=Symbol.for(\"react.element\"),p=Symbol.for(\"react.portal\"),y=Symbol.for(\"react.fragment\"),v=Symbol.for(\"react.strict_mode\"),q=Symbol.for(\"react.profiler\"),G=Symbol.for(\"react.provider\"),H=Symbol.for(\"react.context\"),T=Symbol.for(\"react.forward_ref\"),F=Symbol.for(\"react.suspense\"),I=Symbol.for(\"react.suspense_list\"),C=Symbol.for(\"react.memo\"),Y=Symbol.for(\"react.lazy\"),Ce=Symbol.for(\"react.offscreen\"),X=Symbol.iterator,Pe=\"@@iterator\";function Oe(e){if(e===null||typeof e!=\"object\")return null;var n=X&&e[X]||e[Pe];return typeof n==\"function\"?n:null}var x=u.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;function m(e){{for(var n=arguments.length,r=new Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];Se(\"error\",e,r)}}function Se(e,n,r){{var i=x.ReactDebugCurrentFrame,c=i.getStackAddendum();c!==\"\"&&(n+=\"%s\",r=r.concat([c]));var s=r.map(function(l){return String(l)});s.unshift(\"Warning: \"+n),Function.prototype.apply.call(console[e],console,s)}}var je=!1,De=!1,Ae=!1,Fe=!1,Ie=!1,J;J=Symbol.for(\"react.module.reference\");function Ye(e){return!!(typeof e==\"string\"||typeof e==\"function\"||e===y||e===q||Ie||e===v||e===F||e===I||Fe||e===Ce||je||De||Ae||typeof e==\"object\"&&e!==null&&(e.$$typeof===Y||e.$$typeof===C||e.$$typeof===G||e.$$typeof===H||e.$$typeof===T||e.$$typeof===J||e.getModuleId!==void 0))}function We(e,n,r){var i=e.displayName;if(i)return i;var c=n.displayName||n.name||\"\";return c!==\"\"?r+\"(\"+c+\")\":r}function Z(e){return e.displayName||\"Context\"}function _(e){if(e==null)return null;if(typeof e.tag==\"number\"&&m(\"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"),typeof e==\"function\")return e.displayName||e.name||null;if(typeof e==\"string\")return e;switch(e){case y:return\"Fragment\";case p:return\"Portal\";case q:return\"Profiler\";case v:return\"StrictMode\";case F:return\"Suspense\";case I:return\"SuspenseList\"}if(typeof e==\"object\")switch(e.$$typeof){case H:var n=e;return Z(n)+\".Consumer\";case G:var r=e;return Z(r._context)+\".Provider\";case T:return We(e,e.render,\"ForwardRef\");case C:var i=e.displayName||null;return i!==null?i:_(e.type)||\"Memo\";case Y:{var c=e,s=c._payload,l=c._init;try{return _(l(s))}catch{return null}}}return null}var N=Object.assign,k=0,ee,ne,re,te,ae,ie,oe;function le(){}le.__reactDisabledLog=!0;function $e(){{if(k===0){ee=console.log,ne=console.info,re=console.warn,te=console.error,ae=console.group,ie=console.groupCollapsed,oe=console.groupEnd;var e={configurable:!0,enumerable:!0,value:le,writable:!0};Object.defineProperties(console,{info:e,log:e,warn:e,error:e,group:e,groupCollapsed:e,groupEnd:e})}k++}}function Le(){{if(k--,k===0){var e={configurable:!0,enumerable:!0,writable:!0};Object.defineProperties(console,{log:N({},e,{value:ee}),info:N({},e,{value:ne}),warn:N({},e,{value:re}),error:N({},e,{value:te}),group:N({},e,{value:ae}),groupCollapsed:N({},e,{value:ie}),groupEnd:N({},e,{value:oe})})}k<0&&m(\"disabledDepth fell below zero. This is a bug in React. Please file an issue.\")}}var W=x.ReactCurrentDispatcher,$;function P(e,n,r){{if($===void 0)try{throw Error()}catch(c){var i=c.stack.trim().match(/\\n( *(at )?)/);$=i&&i[1]||\"\"}return`\n`+$+e}}var L=!1,O;{var Me=typeof WeakMap==\"function\"?WeakMap:Map;O=new Me}function ce(e,n){if(!e||L)return\"\";{var r=O.get(e);if(r!==void 0)return r}var i;L=!0;var c=Error.prepareStackTrace;Error.prepareStackTrace=void 0;var s;s=W.current,W.current=null,$e();try{if(n){var l=function(){throw Error()};if(Object.defineProperty(l.prototype,\"props\",{set:function(){throw Error()}}),typeof Reflect==\"object\"&&Reflect.construct){try{Reflect.construct(l,[])}catch(g){i=g}Reflect.construct(e,[],l)}else{try{l.call()}catch(g){i=g}e.call(l.prototype)}}else{try{throw Error()}catch(g){i=g}e()}}catch(g){if(g&&i&&typeof g.stack==\"string\"){for(var o=g.stack.split(`\n`),b=i.stack.split(`\n`),f=o.length-1,d=b.length-1;f>=1&&d>=0&&o[f]!==b[d];)d--;for(;f>=1&&d>=0;f--,d--)if(o[f]!==b[d]){if(f!==1||d!==1)do if(f--,d--,d<0||o[f]!==b[d]){var h=`\n`+o[f].replace(\" at new \",\" at \");return e.displayName&&h.includes(\"<anonymous>\")&&(h=h.replace(\"<anonymous>\",e.displayName)),typeof e==\"function\"&&O.set(e,h),h}while(f>=1&&d>=0);break}}}finally{L=!1,W.current=s,Le(),Error.prepareStackTrace=c}var E=e?e.displayName||e.name:\"\",ye=E?P(E):\"\";return typeof e==\"function\"&&O.set(e,ye),ye}function Ve(e,n,r){return ce(e,!1)}function Ke(e){var n=e.prototype;return!!(n&&n.isReactComponent)}function S(e,n,r){if(e==null)return\"\";if(typeof e==\"function\")return ce(e,Ke(e));if(typeof e==\"string\")return P(e);switch(e){case F:return P(\"Suspense\");case I:return P(\"SuspenseList\")}if(typeof e==\"object\")switch(e.$$typeof){case T:return Ve(e.render);case C:return S(e.type,n,r);case Y:{var i=e,c=i._payload,s=i._init;try{return S(s(c),n,r)}catch{}}}return\"\"}var j=Object.prototype.hasOwnProperty,se={},ue=x.ReactDebugCurrentFrame;function D(e){if(e){var n=e._owner,r=S(e.type,e._source,n?n.type:null);ue.setExtraStackFrame(r)}else ue.setExtraStackFrame(null)}function Ue(e,n,r,i,c){{var s=Function.call.bind(j);for(var l in e)if(s(e,l)){var o=void 0;try{if(typeof e[l]!=\"function\"){var b=Error((i||\"React class\")+\": \"+r+\" type `\"+l+\"` is invalid; it must be a function, usually from the `prop-types` package, but received `\"+typeof e[l]+\"`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.\");throw b.name=\"Invariant Violation\",b}o=e[l](n,l,i,r,null,\"SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED\")}catch(f){o=f}o&&!(o instanceof Error)&&(D(c),m(\"%s: type specification of %s `%s` is invalid; the type checker function must return `null` or an `Error` but returned a %s. You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument).\",i||\"React class\",r,l,typeof o),D(null)),o instanceof Error&&!(o.message in se)&&(se[o.message]=!0,D(c),m(\"Failed %s type: %s\",r,o.message),D(null))}}}var ze=Array.isArray;function M(e){return ze(e)}function Be(e){{var n=typeof Symbol==\"function\"&&Symbol.toStringTag,r=n&&e[Symbol.toStringTag]||e.constructor.name||\"Object\";return r}}function Qe(e){try{return fe(e),!1}catch{return!0}}function fe(e){return\"\"+e}function de(e){if(Qe(e))return m(\"The provided key is an unsupported type %s. This value must be coerced to a string before before using it here.\",Be(e)),fe(e)}var R=x.ReactCurrentOwner,qe={key:!0,ref:!0,__self:!0,__source:!0},me,be,V;V={};function Ge(e){if(j.call(e,\"ref\")){var n=Object.getOwnPropertyDescriptor(e,\"ref\").get;if(n&&n.isReactWarning)return!1}return e.ref!==void 0}function He(e){if(j.call(e,\"key\")){var n=Object.getOwnPropertyDescriptor(e,\"key\").get;if(n&&n.isReactWarning)return!1}return e.key!==void 0}function Xe(e,n){if(typeof e.ref==\"string\"&&R.current&&n&&R.current.stateNode!==n){var r=_(R.current.type);V[r]||(m('Component \"%s\" contains the string ref \"%s\". Support for string refs will be removed in a future major release. This case cannot be automatically converted to an arrow function. We ask you to manually fix this case by using useRef() or createRef() instead. Learn more about using refs safely here: https://reactjs.org/link/strict-mode-string-ref',_(R.current.type),e.ref),V[r]=!0)}}function Je(e,n){{var r=function(){me||(me=!0,m(\"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)\",n))};r.isReactWarning=!0,Object.defineProperty(e,\"key\",{get:r,configurable:!0})}}function Ze(e,n){{var r=function(){be||(be=!0,m(\"%s: `ref` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)\",n))};r.isReactWarning=!0,Object.defineProperty(e,\"ref\",{get:r,configurable:!0})}}var en=function(e,n,r,i,c,s,l){var o={$$typeof:t,type:e,key:n,ref:r,props:l,_owner:s};return o._store={},Object.defineProperty(o._store,\"validated\",{configurable:!1,enumerable:!1,writable:!0,value:!1}),Object.defineProperty(o,\"_self\",{configurable:!1,enumerable:!1,writable:!1,value:i}),Object.defineProperty(o,\"_source\",{configurable:!1,enumerable:!1,writable:!1,value:c}),Object.freeze&&(Object.freeze(o.props),Object.freeze(o)),o};function nn(e,n,r,i,c){{var s,l={},o=null,b=null;r!==void 0&&(de(r),o=\"\"+r),He(n)&&(de(n.key),o=\"\"+n.key),Ge(n)&&(b=n.ref,Xe(n,c));for(s in n)j.call(n,s)&&!qe.hasOwnProperty(s)&&(l[s]=n[s]);if(e&&e.defaultProps){var f=e.defaultProps;for(s in f)l[s]===void 0&&(l[s]=f[s])}if(o||b){var d=typeof e==\"function\"?e.displayName||e.name||\"Unknown\":e;o&&Je(l,d),b&&Ze(l,d)}return en(e,o,b,c,i,R.current,l)}}var K=x.ReactCurrentOwner,he=x.ReactDebugCurrentFrame;function w(e){if(e){var n=e._owner,r=S(e.type,e._source,n?n.type:null);he.setExtraStackFrame(r)}else he.setExtraStackFrame(null)}var U;U=!1;function z(e){return typeof e==\"object\"&&e!==null&&e.$$typeof===t}function pe(){{if(K.current){var e=_(K.current.type);if(e)return`\n\nCheck the render method of \\``+e+\"`.\"}return\"\"}}function rn(e){{if(e!==void 0){var n=e.fileName.replace(/^.*[\\\\\\/]/,\"\"),r=e.lineNumber;return`\n\nCheck your code at `+n+\":\"+r+\".\"}return\"\"}}var _e={};function tn(e){{var n=pe();if(!n){var r=typeof e==\"string\"?e:e.displayName||e.name;r&&(n=`\n\nCheck the top-level render call using <`+r+\">.\")}return n}}function ge(e,n){{if(!e._store||e._store.validated||e.key!=null)return;e._store.validated=!0;var r=tn(n);if(_e[r])return;_e[r]=!0;var i=\"\";e&&e._owner&&e._owner!==K.current&&(i=\" It was passed a child from \"+_(e._owner.type)+\".\"),w(e),m('Each child in a list should have a unique \"key\" prop.%s%s See https://reactjs.org/link/warning-keys for more information.',r,i),w(null)}}function ve(e,n){{if(typeof e!=\"object\")return;if(M(e))for(var r=0;r<e.length;r++){var i=e[r];z(i)&&ge(i,n)}else if(z(e))e._store&&(e._store.validated=!0);else if(e){var c=Oe(e);if(typeof c==\"function\"&&c!==e.entries)for(var s=c.call(e),l;!(l=s.next()).done;)z(l.value)&&ge(l.value,n)}}}function an(e){{var n=e.type;if(n==null||typeof n==\"string\")return;var r;if(typeof n==\"function\")r=n.propTypes;else if(typeof n==\"object\"&&(n.$$typeof===T||n.$$typeof===C))r=n.propTypes;else return;if(r){var i=_(n);Ue(r,e.props,\"prop\",i,e)}else if(n.PropTypes!==void 0&&!U){U=!0;var c=_(n);m(\"Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?\",c||\"Unknown\")}typeof n.getDefaultProps==\"function\"&&!n.getDefaultProps.isReactClassApproved&&m(\"getDefaultProps is only used on classic React.createClass definitions. Use a static property named `defaultProps` instead.\")}}function on(e){{for(var n=Object.keys(e.props),r=0;r<n.length;r++){var i=n[r];if(i!==\"children\"&&i!==\"key\"){w(e),m(\"Invalid prop `%s` supplied to `React.Fragment`. React.Fragment can only have `key` and `children` props.\",i),w(null);break}}e.ref!==null&&(w(e),m(\"Invalid attribute `ref` supplied to `React.Fragment`.\"),w(null))}}function ln(e,n,r,i,c,s){{var l=Ye(e);if(!l){var o=\"\";(e===void 0||typeof e==\"object\"&&e!==null&&Object.keys(e).length===0)&&(o+=\" You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.\");var b=rn(c);b?o+=b:o+=pe();var f;e===null?f=\"null\":M(e)?f=\"array\":e!==void 0&&e.$$typeof===t?(f=\"<\"+(_(e.type)||\"Unknown\")+\" />\",o=\" Did you accidentally export a JSX literal instead of a component?\"):f=typeof e,m(\"React.jsx: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: %s.%s\",f,o)}var d=nn(e,n,r,c,s);if(d==null)return d;if(l){var h=n.children;if(h!==void 0)if(i)if(M(h)){for(var E=0;E<h.length;E++)ve(h[E],e);Object.freeze&&Object.freeze(h)}else m(\"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\");else ve(h,e)}return e===y?on(d):an(d),d}}var cn=ln;Q.Fragment=y,Q.jsxDEV=cn})()});var Re=B((En,ke)=>{\"use strict\";ke.exports=Ee()});var yn={};bn(yn,{default:()=>vn,frontmatter:()=>_n});var a=hn(Re()),_n={title:\"Terms and Conditions | Kapreon\",description:\"Terms and conditions of use for Kapreon's website.\",heroTitle:\"Terms and Conditions\",heroSubtitle:\"Legal\",lastUpdated:new Date(1739232e6)};function Te(u){let t=Object.assign({p:\"p\",h2:\"h2\",ul:\"ul\",li:\"li\",a:\"a\",h3:\"h3\"},u.components);return(0,a.jsxDEV)(a.Fragment,{children:[(0,a.jsxDEV)(t.p,{children:\"Kapreon is a company incorporated in Canada. The information contained on this website is the property of Kapreon and is protected by copyright and trademark laws in force in Canada and Quebec.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\",lineNumber:9,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.h2,{children:\"Publication Manager\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\",lineNumber:11,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.ul,{children:[`\n`,(0,a.jsxDEV)(t.li,{children:\"Kapreon Inc.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\",lineNumber:13,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.li,{children:\"Quebec Enterprise Number (NEQ): 1175313031\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\",lineNumber:14,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.li,{children:\"Head office: 100-50 Rue St-Charles O., Longueuil, J4H 1C6, Quebec, Canada\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\",lineNumber:15,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.li,{children:[\"Phone number: \",(0,a.jsxDEV)(t.a,{href:\"tel:+14388033053\",children:\"+****************\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\",lineNumber:16,columnNumber:17},this)]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\",lineNumber:16,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.li,{children:[\"Email address: \",(0,a.jsxDEV)(t.a,{href:\"mailto:<EMAIL>\",children:\"<EMAIL>\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\",lineNumber:17,columnNumber:18},this)]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\",lineNumber:17,columnNumber:1},this),`\n`]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\",lineNumber:13,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.h2,{children:\"Liability\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\",lineNumber:19,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:\"The information contained on this website is provided for informational purposes and is not intended to be exhaustive. Kapreon cannot guarantee the accuracy, completeness or timeliness of information published on its website.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\",lineNumber:21,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:\"Consequently, Kapreon disclaims all liability:\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\",lineNumber:23,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.ul,{children:[`\n`,(0,a.jsxDEV)(t.li,{children:\"for any inaccuracy, inexactitude or omission relating to information available on the website;\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\",lineNumber:25,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.li,{children:\"for all damages resulting from fraudulent intrusion by a third party that led to modification of information made available on the website;\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\",lineNumber:26,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.li,{children:\"for all damages, direct or indirect, caused due to access to or use of the site.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\",lineNumber:27,columnNumber:1},this),`\n`]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\",lineNumber:25,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:\"Kapreon does not guarantee uninterrupted access to the site. Kapreon reserves the right to suspend access for maintenance or any other technical reason.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\",lineNumber:29,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:\"External hyperlinks present on the site cannot engage Kapreon's liability.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\",lineNumber:31,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.h2,{children:\"Intellectual Property\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\",lineNumber:33,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:\"The site may contain trademarks, logos, images, videos, texts and other content belonging to Kapreon or third parties. Kapreon holds exclusive rights to certain content, while others are the property of partners, clients or third parties and are used with their authorization or as part of completed projects, fictional or not.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\",lineNumber:35,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:\"Any reproduction, distribution, modification or reuse, in whole or in part, of elements present on this site is prohibited without express authorization from rights holders. Use of site content, including third-party images and logos, does not confer any property or exploitation rights to users.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\",lineNumber:37,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.h2,{children:\"Personal Data Protection\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\",lineNumber:39,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:\"Personal data collected on the site is processed in accordance with Canada's Personal Information Protection and Electronic Documents Act (PIPEDA) and Quebec's Act respecting the protection of personal information in the private sector.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\",lineNumber:41,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.h3,{children:\"Processing Purpose\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\",lineNumber:43,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:\"Data collected is used exclusively to respond to information requests and manage registrations and services offered on the site.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\",lineNumber:45,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.h3,{children:\"Data Recipients\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\",lineNumber:47,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:\"Data is intended for internal use exclusively and will under no circumstances be transferred, sold or rented to third parties.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\",lineNumber:49,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.h3,{children:\"Data Security\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\",lineNumber:51,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:\"Kapreon implements strict security measures to protect personal information against unauthorized access, loss or modification. Data is stored on secure and encrypted servers.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\",lineNumber:53,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.h3,{children:\"Retention Period\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\",lineNumber:55,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:\"Data is retained as long as necessary for intended purposes, in accordance with applicable legislation.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\",lineNumber:57,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.h3,{children:\"User Rights\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\",lineNumber:59,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:[\"In accordance with PIPEDA and Quebec legislation, you have the right to access, rectify and delete data concerning you. You can exercise this right by contacting us at \",(0,a.jsxDEV)(t.a,{href:\"mailto:<EMAIL>\",children:\"<EMAIL>\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\",lineNumber:61,columnNumber:169},this),\".\"]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\",lineNumber:61,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.h3,{children:\"Cookie Policy\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\",lineNumber:63,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:[\"Kapreon uses cookies to improve user experience and analyze site audience. You can consult our \",(0,a.jsxDEV)(t.a,{href:\"/legal/cookies\",children:\"Cookie Policy\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\",lineNumber:65,columnNumber:96},this),\" for more information on their use and managing your preferences.\"]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\",lineNumber:65,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.h3,{children:\"Applicable Law and Competent Jurisdiction\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\",lineNumber:67,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:\"This site is subject to Canadian and Quebec laws. In case of dispute, the competent jurisdiction is that of Quebec courts, Montreal district.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\",lineNumber:69,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:\"These legal notices may be modified at any time. We therefore invite you to consult them regularly.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\",lineNumber:71,columnNumber:1},this),`\n`,(0,a.jsxDEV)(t.p,{children:\"Last update: February 11, 2025\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\",lineNumber:73,columnNumber:1},this)]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\",lineNumber:1,columnNumber:1},this)}function gn(u={}){let{wrapper:t}=u.components||{};return t?(0,a.jsxDEV)(t,Object.assign({},u,{children:(0,a.jsxDEV)(Te,u,void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\"},this)}),void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/en/_mdx_bundler_entry_point-a15b1efe-f0f0-47d5-9d1e-0c2c4b19f8e8.mdx\"},this):Te(u)}var vn=gn;return pn(yn);})();\n/*! Bundled license information:\n\nreact/cjs/react-jsx-dev-runtime.development.js:\n  (**\n   * @license React\n   * react-jsx-dev-runtime.development.js\n   *\n   * Copyright (c) Facebook, Inc. and its affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *)\n*/\n;return Component;"}, "_id": "legal/en/terms.mdx", "_raw": {"sourceFilePath": "legal/en/terms.mdx", "sourceFileName": "terms.mdx", "sourceFileDir": "legal/en", "contentType": "mdx", "flattenedPath": "legal/en/terms"}, "type": "Legal", "locale": "en", "slug": "terms"}