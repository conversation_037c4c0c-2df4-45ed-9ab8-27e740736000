{"title": "Utilisation des cookies | Kapreon", "description": "Politique d'utilisation des cookies de Kapreon conforme aux lois canadiennes et québécoises.", "heroTitle": "Utilisation des cookies", "heroSubtitle": "Légal", "lastUpdated": "2025-02-11T00:00:00.000Z", "body": {"raw": "\nLa présente politique vise à informer les utilisateurs de notre site web sur l'utilisation des témoins de connexion, communément appelés « cookies », conformément aux lois canadiennes et québécoises, notamment la Loi 25 du Québec. En accédant à notre site, l'utilisateur est d'accord à l'utilisation des cookies tels que décrits ci-dessous.\n\n## Qu'est-ce qu'un cookie ?\n\nUn cookie est un fichier texte que le site web envoie à votre navigateur et qui est ensuite stocké sur un appareil. Les cookies permettent au site de reconnaître l'appareil de l'utilisateur lors de visites ultérieures, facilitant ainsi la navigation et améliorant l'expérience utilisateur.\n\n## Type de cookies\n\nKapreon utilise des cookies analytiques pour collecter des informations anonymes sur la manière dont les visiteurs utilisent le site de Kapreon. Ces données aident Kapreon à améliorer le contenu et les performances du site.\n\n## Durée de conservation des cookies\n\nLes cookies sont conservés pour une durée proportionnelle à leur finalité.\n\nLors de la première visite sur le site de Kapreon, une bannière informe l'utilisateur de l'utilisation des cookies et invite l'utilisateur à les accepter ou à les refuser. L'utilisateur peut à tout moment modifier ses préférences en matière de cookies en ajustant les paramètres de son navigateur.\n\n## Droits des utilisateurs\n\nConformément à la Loi 25 du Québec et à la LPRPDE, l'utilisateur dispose des droits suivants :\n\n- **Droit d'accès** : L'utilisateur peut demander l'accès aux renseignements personnels que nous détenons au sujet de l'utilisateur.\n- **Droit de rectification** : L'utilisateur peut demander la correction de renseignements personnels inexacts ou incomplets.\n- **Droit de retrait du consentement** : L'utilisateur peut retirer son consentement à l'utilisation de ses renseignements personnels, y compris les cookies, à tout moment.\n\n## Modification de la politique\n\nKapreon se réserve le droit de modifier la présente politique en fonction des évolutions législatives ou techniques. Kapreon encourage à consulter régulièrement cette page pour tenir informé ses utilisateurs des éventuels changements.\n\nPour toute question ou préoccupation concernant cette politique ou les pratiques de Kapreon en matière de protection des renseignements personnels, veuillez contacter l'agence à [<EMAIL>](mailto:<EMAIL>).\n\nCette politique est en vigueur à compter du 11 février 2025.\n", "code": "var Component=(()=>{var ur=Object.create;var F=Object.defineProperty;var sr=Object.getOwnPropertyDescriptor;var cr=Object.getOwnPropertyNames;var fr=Object.getPrototypeOf,dr=Object.prototype.hasOwnProperty;var B=(c,t)=>()=>(t||c((t={exports:{}}).exports,t),t.exports),mr=(c,t)=>{for(var v in t)F(c,v,{get:t[v],enumerable:!0})},Ee=(c,t,v,y)=>{if(t&&typeof t==\"object\"||typeof t==\"function\")for(let _ of cr(t))!dr.call(c,_)&&_!==v&&F(c,_,{get:()=>t[_],enumerable:!(y=sr(t,_))||y.enumerable});return c};var pr=(c,t,v)=>(v=c!=null?ur(fr(c)):{},Ee(t||!c||!c.__esModule?F(v,\"default\",{value:c,enumerable:!0}):v,c)),br=c=>Ee(F({},\"__esModule\",{value:!0}),c);var Re=B((Er,xe)=>{xe.exports=React});var Ne=B(z=>{\"use strict\";(function(){\"use strict\";var c=Re(),t=Symbol.for(\"react.element\"),v=Symbol.for(\"react.portal\"),y=Symbol.for(\"react.fragment\"),_=Symbol.for(\"react.strict_mode\"),G=Symbol.for(\"react.profiler\"),X=Symbol.for(\"react.provider\"),H=Symbol.for(\"react.context\"),T=Symbol.for(\"react.forward_ref\"),A=Symbol.for(\"react.suspense\"),L=Symbol.for(\"react.suspense_list\"),C=Symbol.for(\"react.memo\"),I=Symbol.for(\"react.lazy\"),Ce=Symbol.for(\"react.offscreen\"),Q=Symbol.iterator,Pe=\"@@iterator\";function Oe(e){if(e===null||typeof e!=\"object\")return null;var r=Q&&e[Q]||e[Pe];return typeof r==\"function\"?r:null}var x=c.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;function m(e){{for(var r=arguments.length,n=new Array(r>1?r-1:0),a=1;a<r;a++)n[a-1]=arguments[a];Se(\"error\",e,n)}}function Se(e,r,n){{var a=x.ReactDebugCurrentFrame,l=a.getStackAddendum();l!==\"\"&&(r+=\"%s\",n=n.concat([l]));var s=n.map(function(o){return String(o)});s.unshift(\"Warning: \"+r),Function.prototype.apply.call(console[e],console,s)}}var je=!1,De=!1,Fe=!1,Ae=!1,Le=!1,J;J=Symbol.for(\"react.module.reference\");function Ie(e){return!!(typeof e==\"string\"||typeof e==\"function\"||e===y||e===G||Le||e===_||e===A||e===L||Ae||e===Ce||je||De||Fe||typeof e==\"object\"&&e!==null&&(e.$$typeof===I||e.$$typeof===C||e.$$typeof===X||e.$$typeof===H||e.$$typeof===T||e.$$typeof===J||e.getModuleId!==void 0))}function qe(e,r,n){var a=e.displayName;if(a)return a;var l=r.displayName||r.name||\"\";return l!==\"\"?n+\"(\"+l+\")\":n}function Z(e){return e.displayName||\"Context\"}function h(e){if(e==null)return null;if(typeof e.tag==\"number\"&&m(\"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"),typeof e==\"function\")return e.displayName||e.name||null;if(typeof e==\"string\")return e;switch(e){case y:return\"Fragment\";case v:return\"Portal\";case G:return\"Profiler\";case _:return\"StrictMode\";case A:return\"Suspense\";case L:return\"SuspenseList\"}if(typeof e==\"object\")switch(e.$$typeof){case H:var r=e;return Z(r)+\".Consumer\";case X:var n=e;return Z(n._context)+\".Provider\";case T:return qe(e,e.render,\"ForwardRef\");case C:var a=e.displayName||null;return a!==null?a:h(e.type)||\"Memo\";case I:{var l=e,s=l._payload,o=l._init;try{return h(o(s))}catch{return null}}}return null}var E=Object.assign,w=0,ee,re,ne,te,ae,ie,oe;function le(){}le.__reactDisabledLog=!0;function Ye(){{if(w===0){ee=console.log,re=console.info,ne=console.warn,te=console.error,ae=console.group,ie=console.groupCollapsed,oe=console.groupEnd;var e={configurable:!0,enumerable:!0,value:le,writable:!0};Object.defineProperties(console,{info:e,log:e,warn:e,error:e,group:e,groupCollapsed:e,groupEnd:e})}w++}}function $e(){{if(w--,w===0){var e={configurable:!0,enumerable:!0,writable:!0};Object.defineProperties(console,{log:E({},e,{value:ee}),info:E({},e,{value:re}),warn:E({},e,{value:ne}),error:E({},e,{value:te}),group:E({},e,{value:ae}),groupCollapsed:E({},e,{value:ie}),groupEnd:E({},e,{value:oe})})}w<0&&m(\"disabledDepth fell below zero. This is a bug in React. Please file an issue.\")}}var q=x.ReactCurrentDispatcher,Y;function P(e,r,n){{if(Y===void 0)try{throw Error()}catch(l){var a=l.stack.trim().match(/\\n( *(at )?)/);Y=a&&a[1]||\"\"}return`\n`+Y+e}}var $=!1,O;{var We=typeof WeakMap==\"function\"?WeakMap:Map;O=new We}function ue(e,r){if(!e||$)return\"\";{var n=O.get(e);if(n!==void 0)return n}var a;$=!0;var l=Error.prepareStackTrace;Error.prepareStackTrace=void 0;var s;s=q.current,q.current=null,Ye();try{if(r){var o=function(){throw Error()};if(Object.defineProperty(o.prototype,\"props\",{set:function(){throw Error()}}),typeof Reflect==\"object\"&&Reflect.construct){try{Reflect.construct(o,[])}catch(g){a=g}Reflect.construct(e,[],o)}else{try{o.call()}catch(g){a=g}e.call(o.prototype)}}else{try{throw Error()}catch(g){a=g}e()}}catch(g){if(g&&a&&typeof g.stack==\"string\"){for(var i=g.stack.split(`\n`),p=a.stack.split(`\n`),f=i.length-1,d=p.length-1;f>=1&&d>=0&&i[f]!==p[d];)d--;for(;f>=1&&d>=0;f--,d--)if(i[f]!==p[d]){if(f!==1||d!==1)do if(f--,d--,d<0||i[f]!==p[d]){var b=`\n`+i[f].replace(\" at new \",\" at \");return e.displayName&&b.includes(\"<anonymous>\")&&(b=b.replace(\"<anonymous>\",e.displayName)),typeof e==\"function\"&&O.set(e,b),b}while(f>=1&&d>=0);break}}}finally{$=!1,q.current=s,$e(),Error.prepareStackTrace=l}var N=e?e.displayName||e.name:\"\",ye=N?P(N):\"\";return typeof e==\"function\"&&O.set(e,ye),ye}function Me(e,r,n){return ue(e,!1)}function Ve(e){var r=e.prototype;return!!(r&&r.isReactComponent)}function S(e,r,n){if(e==null)return\"\";if(typeof e==\"function\")return ue(e,Ve(e));if(typeof e==\"string\")return P(e);switch(e){case A:return P(\"Suspense\");case L:return P(\"SuspenseList\")}if(typeof e==\"object\")switch(e.$$typeof){case T:return Me(e.render);case C:return S(e.type,r,n);case I:{var a=e,l=a._payload,s=a._init;try{return S(s(l),r,n)}catch{}}}return\"\"}var j=Object.prototype.hasOwnProperty,se={},ce=x.ReactDebugCurrentFrame;function D(e){if(e){var r=e._owner,n=S(e.type,e._source,r?r.type:null);ce.setExtraStackFrame(n)}else ce.setExtraStackFrame(null)}function Ue(e,r,n,a,l){{var s=Function.call.bind(j);for(var o in e)if(s(e,o)){var i=void 0;try{if(typeof e[o]!=\"function\"){var p=Error((a||\"React class\")+\": \"+n+\" type `\"+o+\"` is invalid; it must be a function, usually from the `prop-types` package, but received `\"+typeof e[o]+\"`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.\");throw p.name=\"Invariant Violation\",p}i=e[o](r,o,a,n,null,\"SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED\")}catch(f){i=f}i&&!(i instanceof Error)&&(D(l),m(\"%s: type specification of %s `%s` is invalid; the type checker function must return `null` or an `Error` but returned a %s. You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument).\",a||\"React class\",n,o,typeof i),D(null)),i instanceof Error&&!(i.message in se)&&(se[i.message]=!0,D(l),m(\"Failed %s type: %s\",n,i.message),D(null))}}}var Ke=Array.isArray;function W(e){return Ke(e)}function Be(e){{var r=typeof Symbol==\"function\"&&Symbol.toStringTag,n=r&&e[Symbol.toStringTag]||e.constructor.name||\"Object\";return n}}function ze(e){try{return fe(e),!1}catch{return!0}}function fe(e){return\"\"+e}function de(e){if(ze(e))return m(\"The provided key is an unsupported type %s. This value must be coerced to a string before before using it here.\",Be(e)),fe(e)}var k=x.ReactCurrentOwner,Ge={key:!0,ref:!0,__self:!0,__source:!0},me,pe,M;M={};function Xe(e){if(j.call(e,\"ref\")){var r=Object.getOwnPropertyDescriptor(e,\"ref\").get;if(r&&r.isReactWarning)return!1}return e.ref!==void 0}function He(e){if(j.call(e,\"key\")){var r=Object.getOwnPropertyDescriptor(e,\"key\").get;if(r&&r.isReactWarning)return!1}return e.key!==void 0}function Qe(e,r){if(typeof e.ref==\"string\"&&k.current&&r&&k.current.stateNode!==r){var n=h(k.current.type);M[n]||(m('Component \"%s\" contains the string ref \"%s\". Support for string refs will be removed in a future major release. This case cannot be automatically converted to an arrow function. We ask you to manually fix this case by using useRef() or createRef() instead. Learn more about using refs safely here: https://reactjs.org/link/strict-mode-string-ref',h(k.current.type),e.ref),M[n]=!0)}}function Je(e,r){{var n=function(){me||(me=!0,m(\"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)\",r))};n.isReactWarning=!0,Object.defineProperty(e,\"key\",{get:n,configurable:!0})}}function Ze(e,r){{var n=function(){pe||(pe=!0,m(\"%s: `ref` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)\",r))};n.isReactWarning=!0,Object.defineProperty(e,\"ref\",{get:n,configurable:!0})}}var er=function(e,r,n,a,l,s,o){var i={$$typeof:t,type:e,key:r,ref:n,props:o,_owner:s};return i._store={},Object.defineProperty(i._store,\"validated\",{configurable:!1,enumerable:!1,writable:!0,value:!1}),Object.defineProperty(i,\"_self\",{configurable:!1,enumerable:!1,writable:!1,value:a}),Object.defineProperty(i,\"_source\",{configurable:!1,enumerable:!1,writable:!1,value:l}),Object.freeze&&(Object.freeze(i.props),Object.freeze(i)),i};function rr(e,r,n,a,l){{var s,o={},i=null,p=null;n!==void 0&&(de(n),i=\"\"+n),He(r)&&(de(r.key),i=\"\"+r.key),Xe(r)&&(p=r.ref,Qe(r,l));for(s in r)j.call(r,s)&&!Ge.hasOwnProperty(s)&&(o[s]=r[s]);if(e&&e.defaultProps){var f=e.defaultProps;for(s in f)o[s]===void 0&&(o[s]=f[s])}if(i||p){var d=typeof e==\"function\"?e.displayName||e.name||\"Unknown\":e;i&&Je(o,d),p&&Ze(o,d)}return er(e,i,p,l,a,k.current,o)}}var V=x.ReactCurrentOwner,be=x.ReactDebugCurrentFrame;function R(e){if(e){var r=e._owner,n=S(e.type,e._source,r?r.type:null);be.setExtraStackFrame(n)}else be.setExtraStackFrame(null)}var U;U=!1;function K(e){return typeof e==\"object\"&&e!==null&&e.$$typeof===t}function ve(){{if(V.current){var e=h(V.current.type);if(e)return`\n\nCheck the render method of \\``+e+\"`.\"}return\"\"}}function nr(e){{if(e!==void 0){var r=e.fileName.replace(/^.*[\\\\\\/]/,\"\"),n=e.lineNumber;return`\n\nCheck your code at `+r+\":\"+n+\".\"}return\"\"}}var he={};function tr(e){{var r=ve();if(!r){var n=typeof e==\"string\"?e:e.displayName||e.name;n&&(r=`\n\nCheck the top-level render call using <`+n+\">.\")}return r}}function ge(e,r){{if(!e._store||e._store.validated||e.key!=null)return;e._store.validated=!0;var n=tr(r);if(he[n])return;he[n]=!0;var a=\"\";e&&e._owner&&e._owner!==V.current&&(a=\" It was passed a child from \"+h(e._owner.type)+\".\"),R(e),m('Each child in a list should have a unique \"key\" prop.%s%s See https://reactjs.org/link/warning-keys for more information.',n,a),R(null)}}function _e(e,r){{if(typeof e!=\"object\")return;if(W(e))for(var n=0;n<e.length;n++){var a=e[n];K(a)&&ge(a,r)}else if(K(e))e._store&&(e._store.validated=!0);else if(e){var l=Oe(e);if(typeof l==\"function\"&&l!==e.entries)for(var s=l.call(e),o;!(o=s.next()).done;)K(o.value)&&ge(o.value,r)}}}function ar(e){{var r=e.type;if(r==null||typeof r==\"string\")return;var n;if(typeof r==\"function\")n=r.propTypes;else if(typeof r==\"object\"&&(r.$$typeof===T||r.$$typeof===C))n=r.propTypes;else return;if(n){var a=h(r);Ue(n,e.props,\"prop\",a,e)}else if(r.PropTypes!==void 0&&!U){U=!0;var l=h(r);m(\"Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?\",l||\"Unknown\")}typeof r.getDefaultProps==\"function\"&&!r.getDefaultProps.isReactClassApproved&&m(\"getDefaultProps is only used on classic React.createClass definitions. Use a static property named `defaultProps` instead.\")}}function ir(e){{for(var r=Object.keys(e.props),n=0;n<r.length;n++){var a=r[n];if(a!==\"children\"&&a!==\"key\"){R(e),m(\"Invalid prop `%s` supplied to `React.Fragment`. React.Fragment can only have `key` and `children` props.\",a),R(null);break}}e.ref!==null&&(R(e),m(\"Invalid attribute `ref` supplied to `React.Fragment`.\"),R(null))}}function or(e,r,n,a,l,s){{var o=Ie(e);if(!o){var i=\"\";(e===void 0||typeof e==\"object\"&&e!==null&&Object.keys(e).length===0)&&(i+=\" You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.\");var p=nr(l);p?i+=p:i+=ve();var f;e===null?f=\"null\":W(e)?f=\"array\":e!==void 0&&e.$$typeof===t?(f=\"<\"+(h(e.type)||\"Unknown\")+\" />\",i=\" Did you accidentally export a JSX literal instead of a component?\"):f=typeof e,m(\"React.jsx: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: %s.%s\",f,i)}var d=rr(e,r,n,l,s);if(d==null)return d;if(o){var b=r.children;if(b!==void 0)if(a)if(W(b)){for(var N=0;N<b.length;N++)_e(b[N],e);Object.freeze&&Object.freeze(b)}else m(\"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\");else _e(b,e)}return e===y?ir(d):ar(d),d}}var lr=or;z.Fragment=y,z.jsxDEV=lr})()});var ke=B((Rr,we)=>{\"use strict\";we.exports=Ne()});var _r={};mr(_r,{default:()=>gr,frontmatter:()=>vr});var u=pr(ke()),vr={title:\"Utilisation des cookies | Kapreon\",description:\"Politique d'utilisation des cookies de Kapreon conforme aux lois canadiennes et qu\\xE9b\\xE9coises.\",heroTitle:\"Utilisation des cookies\",heroSubtitle:\"L\\xE9gal\",lastUpdated:new Date(1739232e6)};function Te(c){let t=Object.assign({p:\"p\",h2:\"h2\",ul:\"ul\",li:\"li\",strong:\"strong\",a:\"a\"},c.components);return(0,u.jsxDEV)(u.Fragment,{children:[(0,u.jsxDEV)(t.p,{children:\"La pr\\xE9sente politique vise \\xE0 informer les utilisateurs de notre site web sur l'utilisation des t\\xE9moins de connexion, commun\\xE9ment appel\\xE9s \\xAB cookies \\xBB, conform\\xE9ment aux lois canadiennes et qu\\xE9b\\xE9coises, notamment la Loi 25 du Qu\\xE9bec. En acc\\xE9dant \\xE0 notre site, l'utilisateur est d'accord \\xE0 l'utilisation des cookies tels que d\\xE9crits ci-dessous.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-ae02f340-9368-4299-8885-53020007715a.mdx\",lineNumber:9,columnNumber:1},this),`\n`,(0,u.jsxDEV)(t.h2,{children:\"Qu'est-ce qu'un cookie ?\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-ae02f340-9368-4299-8885-53020007715a.mdx\",lineNumber:11,columnNumber:1},this),`\n`,(0,u.jsxDEV)(t.p,{children:\"Un cookie est un fichier texte que le site web envoie \\xE0 votre navigateur et qui est ensuite stock\\xE9 sur un appareil. Les cookies permettent au site de reconna\\xEEtre l'appareil de l'utilisateur lors de visites ult\\xE9rieures, facilitant ainsi la navigation et am\\xE9liorant l'exp\\xE9rience utilisateur.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-ae02f340-9368-4299-8885-53020007715a.mdx\",lineNumber:13,columnNumber:1},this),`\n`,(0,u.jsxDEV)(t.h2,{children:\"Type de cookies\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-ae02f340-9368-4299-8885-53020007715a.mdx\",lineNumber:15,columnNumber:1},this),`\n`,(0,u.jsxDEV)(t.p,{children:\"Kapreon utilise des cookies analytiques pour collecter des informations anonymes sur la mani\\xE8re dont les visiteurs utilisent le site de Kapreon. Ces donn\\xE9es aident Kapreon \\xE0 am\\xE9liorer le contenu et les performances du site.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-ae02f340-9368-4299-8885-53020007715a.mdx\",lineNumber:17,columnNumber:1},this),`\n`,(0,u.jsxDEV)(t.h2,{children:\"Dur\\xE9e de conservation des cookies\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-ae02f340-9368-4299-8885-53020007715a.mdx\",lineNumber:19,columnNumber:1},this),`\n`,(0,u.jsxDEV)(t.p,{children:\"Les cookies sont conserv\\xE9s pour une dur\\xE9e proportionnelle \\xE0 leur finalit\\xE9.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-ae02f340-9368-4299-8885-53020007715a.mdx\",lineNumber:21,columnNumber:1},this),`\n`,(0,u.jsxDEV)(t.p,{children:\"Lors de la premi\\xE8re visite sur le site de Kapreon, une banni\\xE8re informe l'utilisateur de l'utilisation des cookies et invite l'utilisateur \\xE0 les accepter ou \\xE0 les refuser. L'utilisateur peut \\xE0 tout moment modifier ses pr\\xE9f\\xE9rences en mati\\xE8re de cookies en ajustant les param\\xE8tres de son navigateur.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-ae02f340-9368-4299-8885-53020007715a.mdx\",lineNumber:23,columnNumber:1},this),`\n`,(0,u.jsxDEV)(t.h2,{children:\"Droits des utilisateurs\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-ae02f340-9368-4299-8885-53020007715a.mdx\",lineNumber:25,columnNumber:1},this),`\n`,(0,u.jsxDEV)(t.p,{children:\"Conform\\xE9ment \\xE0 la Loi 25 du Qu\\xE9bec et \\xE0 la LPRPDE, l'utilisateur dispose des droits suivants :\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-ae02f340-9368-4299-8885-53020007715a.mdx\",lineNumber:27,columnNumber:1},this),`\n`,(0,u.jsxDEV)(t.ul,{children:[`\n`,(0,u.jsxDEV)(t.li,{children:[(0,u.jsxDEV)(t.strong,{children:\"Droit d'acc\\xE8s\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-ae02f340-9368-4299-8885-53020007715a.mdx\",lineNumber:29,columnNumber:3},this),\" : L'utilisateur peut demander l'acc\\xE8s aux renseignements personnels que nous d\\xE9tenons au sujet de l'utilisateur.\"]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-ae02f340-9368-4299-8885-53020007715a.mdx\",lineNumber:29,columnNumber:1},this),`\n`,(0,u.jsxDEV)(t.li,{children:[(0,u.jsxDEV)(t.strong,{children:\"Droit de rectification\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-ae02f340-9368-4299-8885-53020007715a.mdx\",lineNumber:30,columnNumber:3},this),\" : L'utilisateur peut demander la correction de renseignements personnels inexacts ou incomplets.\"]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-ae02f340-9368-4299-8885-53020007715a.mdx\",lineNumber:30,columnNumber:1},this),`\n`,(0,u.jsxDEV)(t.li,{children:[(0,u.jsxDEV)(t.strong,{children:\"Droit de retrait du consentement\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-ae02f340-9368-4299-8885-53020007715a.mdx\",lineNumber:31,columnNumber:3},this),\" : L'utilisateur peut retirer son consentement \\xE0 l'utilisation de ses renseignements personnels, y compris les cookies, \\xE0 tout moment.\"]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-ae02f340-9368-4299-8885-53020007715a.mdx\",lineNumber:31,columnNumber:1},this),`\n`]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-ae02f340-9368-4299-8885-53020007715a.mdx\",lineNumber:29,columnNumber:1},this),`\n`,(0,u.jsxDEV)(t.h2,{children:\"Modification de la politique\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-ae02f340-9368-4299-8885-53020007715a.mdx\",lineNumber:33,columnNumber:1},this),`\n`,(0,u.jsxDEV)(t.p,{children:\"Kapreon se r\\xE9serve le droit de modifier la pr\\xE9sente politique en fonction des \\xE9volutions l\\xE9gislatives ou techniques. Kapreon encourage \\xE0 consulter r\\xE9guli\\xE8rement cette page pour tenir inform\\xE9 ses utilisateurs des \\xE9ventuels changements.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-ae02f340-9368-4299-8885-53020007715a.mdx\",lineNumber:35,columnNumber:1},this),`\n`,(0,u.jsxDEV)(t.p,{children:[\"Pour toute question ou pr\\xE9occupation concernant cette politique ou les pratiques de Kapreon en mati\\xE8re de protection des renseignements personnels, veuillez contacter l'agence \\xE0 \",(0,u.jsxDEV)(t.a,{href:\"mailto:<EMAIL>\",children:\"<EMAIL>\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-ae02f340-9368-4299-8885-53020007715a.mdx\",lineNumber:37,columnNumber:179},this),\".\"]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-ae02f340-9368-4299-8885-53020007715a.mdx\",lineNumber:37,columnNumber:1},this),`\n`,(0,u.jsxDEV)(t.p,{children:\"Cette politique est en vigueur \\xE0 compter du 11 f\\xE9vrier 2025.\"},void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-ae02f340-9368-4299-8885-53020007715a.mdx\",lineNumber:39,columnNumber:1},this)]},void 0,!0,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-ae02f340-9368-4299-8885-53020007715a.mdx\",lineNumber:1,columnNumber:1},this)}function hr(c={}){let{wrapper:t}=c.components||{};return t?(0,u.jsxDEV)(t,Object.assign({},c,{children:(0,u.jsxDEV)(Te,c,void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-ae02f340-9368-4299-8885-53020007715a.mdx\"},this)}),void 0,!1,{fileName:\"/home/<USER>/git/kapreon-website-1/src/content/legal/fr/_mdx_bundler_entry_point-ae02f340-9368-4299-8885-53020007715a.mdx\"},this):Te(c)}var gr=hr;return br(_r);})();\n/*! Bundled license information:\n\nreact/cjs/react-jsx-dev-runtime.development.js:\n  (**\n   * @license React\n   * react-jsx-dev-runtime.development.js\n   *\n   * Copyright (c) Facebook, Inc. and its affiliates.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   *)\n*/\n;return Component;"}, "_id": "legal/fr/cookies.mdx", "_raw": {"sourceFilePath": "legal/fr/cookies.mdx", "sourceFileName": "cookies.mdx", "sourceFileDir": "legal/fr", "contentType": "mdx", "flattenedPath": "legal/fr/cookies"}, "type": "Legal", "locale": "fr", "slug": "cookies"}