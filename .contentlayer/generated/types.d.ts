// NOTE This file is auto-generated by Contentlayer

import type { Markdown, <PERSON><PERSON>, <PERSON><PERSON>ieldD<PERSON>, IsoDateTimeString } from 'contentlayer/core'
import * as Local from 'contentlayer/source-files'

export { isType } from 'contentlayer/client'

export type { Markdown, MDX, ImageFieldData, IsoDateTimeString }

/** Document types */
export type Legal = {
  /** File path relative to `contentDirPath` */
  _id: string
  _raw: Local.RawDocumentData
  type: 'Legal'
  title: string
  description: string
  heroTitle: string
  heroSubtitle: string
  lastUpdated: IsoDateTimeString
  /** MDX file body */
  body: MDX
  locale: string
  slug: string
}

export type Post = {
  /** File path relative to `contentDirPath` */
  _id: string
  _raw: Local.RawDocumentData
  type: 'Post'
  title: string
  excerpt: string
  date: IsoDateTimeString
  updated?: IsoDateTimeString | undefined
  category?: string | undefined
  tags?: string[] | undefined
  cover?: string | undefined
  published: boolean
  /** MDX file body */
  body: MDX
  locale: string
  slug: string
}  

/** Nested types */
  

/** Helper types */

export type AllTypes = DocumentTypes | NestedTypes
export type AllTypeNames = DocumentTypeNames | NestedTypeNames

export type DocumentTypes = Legal | Post
export type DocumentTypeNames = 'Legal' | 'Post'

export type NestedTypes = never
export type NestedTypeNames = never

export type DataExports = {
  allDocuments: DocumentTypes[]
  allPosts: Post[]
  allLegals: Legal[]
}


export interface ContentlayerGenTypes {
  documentTypes: DocumentTypes
  documentTypeMap: DocumentTypeMap
  documentTypeNames: DocumentTypeNames
  nestedTypes: NestedTypes
  nestedTypeMap: NestedTypeMap
  nestedTypeNames: NestedTypeNames
  allTypeNames: AllTypeNames
  dataExports: DataExports
}

declare global {
  interface ContentlayerGen extends ContentlayerGenTypes {}
}

export type DocumentTypeMap = {
  Legal: Legal
  Post: Post
}

export type NestedTypeMap = {

}

 