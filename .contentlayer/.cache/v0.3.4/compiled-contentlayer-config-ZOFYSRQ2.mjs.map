{"version": 3, "sources": ["../../../contentlayer.config.js"], "sourcesContent": ["// contentlayer.config.js\nimport { defineDocumentType, makeSource } from 'contentlayer/source-files';\nimport rehypePrism from 'rehype-prism-plus';\nimport remarkGfm from 'remark-gfm';\n\nconst Post = defineDocumentType(() => ({\n  name: 'Post',\n  filePathPattern: 'blog/**/*.mdx',\n  contentType: 'mdx',\n  fields: {\n    title:   { type: 'string', required: true },\n    excerpt: { type: 'string', required: true },\n    date:    { type: 'date',   required: true },   // date de publication\n    updated: { type: 'date' },                     // nouvelle: dernière MAJ\n    category:{ type: 'string' },                   // nouvelle: 1 seule catégorie\n    tags:    { type: 'list', of: { type: 'string' } },\n    cover: { type: 'string' },\n    published: { type: 'boolean', default: true },\n  },\n  computedFields: {\n    locale: {\n      type: 'string',\n      resolve: (post) => {\n        // Extraire la langue du chemin : blog/fr/article.mdx -> fr\n        const pathParts = post._raw.flattenedPath.split('/');\n        return pathParts[1] || 'fr'; // défaut français\n      },\n    },\n    slug: {\n      type: 'string',\n      resolve: (post) => {\n        // Extraire le slug : blog/fr/article.mdx -> article\n        return post._raw.flattenedPath.split('/').pop();\n      },\n    },\n  },\n}));\n\nconst Legal = defineDocumentType(() => ({\n  name: 'Legal',\n  filePathPattern: 'legal/**/*.mdx',\n  contentType: 'mdx',\n  fields: {\n    title: { type: 'string', required: true },\n    description: { type: 'string', required: true },\n    heroTitle: { type: 'string', required: true },\n    heroSubtitle: { type: 'string', required: true },\n    lastUpdated: { type: 'date', required: true },\n  },\n  computedFields: {\n    locale: {\n      type: 'string',\n      resolve: (doc) => {\n        // Extraire la langue du chemin : legal/fr/cookies.md -> fr\n        const pathParts = doc._raw.flattenedPath.split('/');\n        return pathParts[1] || 'fr';\n      },\n    },\n    slug: {\n      type: 'string',\n      resolve: (doc) => {\n        // Extraire le slug : legal/fr/cookies.md -> cookies\n        return doc._raw.flattenedPath.split('/').pop();\n      },\n    },\n  },\n}));\n\nexport default makeSource({\n  contentDirPath: 'src/content',\n  documentTypes: [Post, Legal],\n  mdx: { remarkPlugins: [remarkGfm], rehypePlugins: [rehypePrism] },\n});\n"], "mappings": ";AACA,SAAS,oBAAoB,kBAAkB;AAC/C,OAAO,iBAAiB;AACxB,OAAO,eAAe;AAEtB,IAAM,OAAO,mBAAmB,OAAO;AAAA,EACrC,MAAM;AAAA,EACN,iBAAiB;AAAA,EACjB,aAAa;AAAA,EACb,QAAQ;AAAA,IACN,OAAS,EAAE,MAAM,UAAU,UAAU,KAAK;AAAA,IAC1C,SAAS,EAAE,MAAM,UAAU,UAAU,KAAK;AAAA,IAC1C,MAAS,EAAE,MAAM,QAAU,UAAU,KAAK;AAAA;AAAA,IAC1C,SAAS,EAAE,MAAM,OAAO;AAAA;AAAA,IACxB,UAAS,EAAE,MAAM,SAAS;AAAA;AAAA,IAC1B,MAAS,EAAE,MAAM,QAAQ,IAAI,EAAE,MAAM,SAAS,EAAE;AAAA,IAChD,OAAO,EAAE,MAAM,SAAS;AAAA,IACxB,WAAW,EAAE,MAAM,WAAW,SAAS,KAAK;AAAA,EAC9C;AAAA,EACA,gBAAgB;AAAA,IACd,QAAQ;AAAA,MACN,MAAM;AAAA,MACN,SAAS,CAAC,SAAS;AAEjB,cAAM,YAAY,KAAK,KAAK,cAAc,MAAM,GAAG;AACnD,eAAO,UAAU,CAAC,KAAK;AAAA,MACzB;AAAA,IACF;AAAA,IACA,MAAM;AAAA,MACJ,MAAM;AAAA,MACN,SAAS,CAAC,SAAS;AAEjB,eAAO,KAAK,KAAK,cAAc,MAAM,GAAG,EAAE,IAAI;AAAA,MAChD;AAAA,IACF;AAAA,EACF;AACF,EAAE;AAEF,IAAM,QAAQ,mBAAmB,OAAO;AAAA,EACtC,MAAM;AAAA,EACN,iBAAiB;AAAA,EACjB,aAAa;AAAA,EACb,QAAQ;AAAA,IACN,OAAO,EAAE,MAAM,UAAU,UAAU,KAAK;AAAA,IACxC,aAAa,EAAE,MAAM,UAAU,UAAU,KAAK;AAAA,IAC9C,WAAW,EAAE,MAAM,UAAU,UAAU,KAAK;AAAA,IAC5C,cAAc,EAAE,MAAM,UAAU,UAAU,KAAK;AAAA,IAC/C,aAAa,EAAE,MAAM,QAAQ,UAAU,KAAK;AAAA,EAC9C;AAAA,EACA,gBAAgB;AAAA,IACd,QAAQ;AAAA,MACN,MAAM;AAAA,MACN,SAAS,CAAC,QAAQ;AAEhB,cAAM,YAAY,IAAI,KAAK,cAAc,MAAM,GAAG;AAClD,eAAO,UAAU,CAAC,KAAK;AAAA,MACzB;AAAA,IACF;AAAA,IACA,MAAM;AAAA,MACJ,MAAM;AAAA,MACN,SAAS,CAAC,QAAQ;AAEhB,eAAO,IAAI,KAAK,cAAc,MAAM,GAAG,EAAE,IAAI;AAAA,MAC/C;AAAA,IACF;AAAA,EACF;AACF,EAAE;AAEF,IAAO,8BAAQ,WAAW;AAAA,EACxB,gBAAgB;AAAA,EAChB,eAAe,CAAC,MAAM,KAAK;AAAA,EAC3B,KAAK,EAAE,eAAe,CAAC,SAAS,GAAG,eAAe,CAAC,WAAW,EAAE;AAClE,CAAC;", "names": []}