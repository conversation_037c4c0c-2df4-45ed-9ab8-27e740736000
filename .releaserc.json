{"branches": ["main"], "plugins": ["@semantic-release/commit-analyzer", "@semantic-release/release-notes-generator", "@semantic-release/npm", ["@semantic-release/changelog", {"changelogFile": "CHANGELOG.md"}], ["@semantic-release/git", {"assets": ["package.json", "CHANGELOG.md"], "message": "chore(release): ${nextRelease.version} [skip ci]\n\n${nextRelease.notes}"}], ["@semantic-release/github", {"successComment": false, "failTitle": false}]], "preset": "angular"}