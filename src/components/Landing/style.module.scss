.landing{
    position: relative;
    display: flex;
    height: 100vh;
    overflow: hidden;

    img{
      object-fit: cover;
    }
    
    .sliderContainer{
      position: absolute;
      top: calc(100vh - 350px);
    }
    
    .slider{
      position: relative;
      white-space: nowrap;
    }
    
    .slider p{
      position: relative;
      margin: 0px;
      color: white;
      font-size: 230px;
      font-weight: 500;
      padding-right: 50px;
    }
    
    .slider p:nth-of-type(2){
      position: absolute;
      left: 100%;
      top: 0;
    }
  
    .description{
      position: absolute;
      top: 35%;
      left: 65%;
      color: white;
      font-size: 24px;
      font-weight: 300;
      p{
        margin: 0px;
        margin-bottom: 10px;
      }
      svg{
        transform: scale(2);
        margin-bottom: 100px;
      }
    }
  }
  
