import React, { useState, useRef } from "react";
import styles from "./style.module.scss";

/**
 * Composant Accordion - Conteneur principal qui gère l'état des éléments
 * @param {React.ReactNode} children - Les éléments AccordionItem
 */
export function Accordion({ children }) {
  const [activeIndex, setActiveIndex] = useState(null);
  const [hoveredIndex, setHoveredIndex] = useState(null);

  const handleItemClick = (index) => {
    setActiveIndex((prev) => (prev === index ? null : index));
  };

  const handleItemHover = (index) => {
    setHoveredIndex(index);
  };

  const handleItemLeave = () => {
    setHoveredIndex(null);
  };

  return (
    <div className={styles.accordion}>
      {React.Children.map(children, (child, index) => {
        if (!React.isValidElement(child)) return null;

        return React.cloneElement(child, {
          isOpen: activeIndex === index,
          isHovered: hoveredIndex === index,
          isOtherHovered: hoveredIndex !== null && hoveredIndex !== index,
          isOtherActive: activeIndex !== null && activeIndex !== index,
          onClick: () => handleItemClick(index),
          onMouseEnter: () => handleItemHover(index),
          onMouseLeave: handleItemLeave
        });
      })}
    </div>
  );
}





/**
 * Composant AccordionItem - Élément individuel d'accordéon
 * @param {string} title - Titre de l'élément
 * @param {React.ReactNode} children - Contenu de l'élément
 * @param {boolean} isOpen - État ouvert/fermé
 * @param {boolean} isHovered - État de survol
 * @param {boolean} isOtherHovered - Un autre élément est survolé
 * @param {boolean} isOtherActive - Un autre élément est actif
 * @param {Function} onClick - Gestionnaire de clic
 * @param {Function} onMouseEnter - Gestionnaire d'entrée de souris
 * @param {Function} onMouseLeave - Gestionnaire de sortie de souris
 * @param {Array} items - Liste d'éléments à afficher
 * @param {boolean} hideIcon - Cacher l'icône "+"
 */
export function AccordionItem({
  title,
  children,
  isOpen = false,
  isHovered = false,
  isOtherHovered = false,
  isOtherActive = false,
  onClick,
  onMouseEnter,
  onMouseLeave,
  items = [],
  hideIcon = false,
  ...rest
}) {
  const contentRef = useRef(null);

  // Calcul de l'opacité pour l'effet de focus
  const itemOpacity = (isOtherHovered || isOtherActive) && !isOpen ? 0.3 : 1;

  return (
    <div className={styles.wrapper}>
      <button
        {...rest}
        onClick={onClick}
        onMouseEnter={onMouseEnter}
        onMouseLeave={onMouseLeave}
        className={`${styles.questionContainer} ${
          isOpen ? styles.questionContainerActive : ""
        }`}
        style={{
          opacity: itemOpacity,
          transition: "all 0.3s ease"
        }}
        aria-expanded={isOpen}
        aria-controls={`accordion-content-${title}`}
      >
        <div className={styles.headerContent}>
          <h3 className={styles.questionContent}>{title}</h3>

          {/* Icône par défaut - conditionnelle */}
          {!hideIcon && (
            <span
              className="material-symbols-outlined"
              style={{
                transform: isOpen ? "rotate(45deg)" : "rotate(0deg)",
                transition: "transform 0.3s ease"
              }}
              aria-hidden="true"
            >
              add
            </span>
          )}
        </div>
      </button>

      <div
        className={styles.answerContainer}
        ref={contentRef}
        id={`accordion-content-${title}`}
        style={{
          height: isOpen && contentRef.current ? contentRef.current.scrollHeight : 0,
          opacity: isOpen ? 1 : 0,
          transition: "height 0.3s ease, opacity 0.3s ease"
        }}
        aria-hidden={!isOpen}
      >
        <div className={styles.answerContent}>
          {/* Affichage conditionnel : items ou contenu children */}
          {items.length > 0 ? (
            items.map((item, index) => (
              <p key={index} className="text-big">
                {item}
              </p>
            ))
          ) : (
            children
          )}
        </div>
      </div>
    </div>
  );
}


