"use client";

import React, { useEffect, useState } from "react";
import AnimatedLink from "../AnimatedLink";
import styles from "./style.module.scss";
import { useTranslation } from "@/hooks/useTranslation";

export default function CookieConsent({ locale = 'fr' }) {
  const { t } = useTranslation('common');
  const [visible, setVisible] = useState(false);

  useEffect(() => {
    if (typeof window !== "undefined") {
      const accepted = localStorage.getItem("cookieConsentAccepted");
      if (!accepted) {
        setVisible(true);
      }
    }
  }, []);

  const handleAccept = () => {
    localStorage.setItem("cookieConsentAccepted", "true");
    setVisible(false);
  };

  if (!visible) return null;

  return (
    <div className={styles.cookieConsentWrapper}>
      <div className={styles.cookieConsentContainer}>
        <div className={styles.cookieConsent}>
          <span className={styles.cookieConsentText}>
            {t('cookies.message')}
          </span>
          <div className={styles.cookieConsentLinks}>
            <AnimatedLink href={`/${locale}/legal/cookies`}>
              {t('cookies.learn_more')}
            </AnimatedLink>
            <AnimatedLink
              href="#"
              onClick={(e) => {
                e.preventDefault();
                handleAccept();
              }}
            >
              {t('cookies.accept')}
            </AnimatedLink>
          </div>
        </div>
      </div>
    </div>
  );
}
