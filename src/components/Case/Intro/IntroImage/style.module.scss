.image {
    width: 100%;
    height: auto;
    display: block;
    object-fit: cover;
    /* Version mobile : la partie supérieure jusqu'à 40% reste intacte */
    clip-path: polygon(18% 0%, 100% 0%, 100% 92%, 92% 100%, 0% 100%, 0% 23%);
  }
  
  @media (min-width: 768px) {
    // .image {
    //   /* Sur tablette, on décale la zone intacte jusqu'à 45% */
    //   clip-path: polygon(
    //     0% 0%,
    //     100% 0%,
    //     100% 45%,
    //     98% 100%,
    //     2% 100%,
    //     0% 45%
    //   );
    // }

    .image {
      clip-path: polygon(18% 0%, 100% 0%, 100% 92%, 92% 100%, 0% 100%, 0% 23%);
    }
  }
  
  @media (min-width: 1024px) {
    .image {
      /* Sur desktop, on conserve la même logique (tu peux ajuster si besoin) */
    //   clip-path: polygon(0% 0%, 100% 0%, 100% 88%, 95% 100%, 5% 109%, 0% 88%);
    clip-path: polygon(5% 0%, 100% 0%, 100% 88%, 95% 100%, 0% 100%, 0% 12%);
    }
  }
  
  
  
  /* Exemple de style pour le bouton, inchangé */
  .button {
    top: 80%;
    left: calc(100% - 200px);
    width: 180px;
    height: 180px;
    background-color: #1C1D20;
    color: white;
    border-radius: 50%;
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    
    p {
      margin: 0;
      font-size: 16px;
      font-weight: 300;
      position: relative;
      z-index: 1;
    }
  }

  // .buttonContainer {
  //   z-index: 3;
  // }

  // .buttonLink {
  //   text-decoration: none;
  //   color: inherit;
  //   cursor: default;
  //   text-align: center;
  //   display: flex;
  // }
  
  .buttonLink {
    text-decoration: none;
    color: inherit;
    cursor: default;
    text-align: center;
    display: flex;
    align-items: center; /* aligne verticalement le texte et l'icône */
    gap: 0.25rem;       /* espace entre le label et l'icône */
    position: relative; /* permet à z-index d'agir */
  }
  
  .material-symbols-outlined {
    /* applique les réglages de l'icône si besoin */
    font-variation-settings: 
      'FILL' 0, 
      'wght' 400, 
      'GRAD' 0, 
      'opsz' 24;
    /* Si tu souhaites utiliser z-index sur l'icône, positionne-la aussi en relative */
    position: relative;
    z-index: 1;
  }

  .container {
    position: relative;
  }

.buttonContainer {
  position: relative;
  z-index: 2;
}