import React from 'react';
import Hero from '../../Heros/Hero';
import Details from './Details';
import Image from 'next/image';
import IntroImage from './IntroImage';



const Intro = ({ project, locale = 'fr' }) => {
    if (!project) {
        return <p>Projet introuvable</p>;
    }
        
  return (
      <div>
          <Hero title={project.title} subtitle={project.client} />
          <Details project={project} locale={locale} />
          <IntroImage project={project} />
                {/* {project.src && (
                  <Image
                    src={`/images/${project.src}`}
                    width={800}
                    height={500}
                    alt={project.title}
                  />
                )} */}
    </div>
  );
};

export default Intro;
