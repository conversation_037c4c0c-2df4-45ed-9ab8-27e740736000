import React from 'react';
import Link from 'next/link';
import styles from './style.module.scss';
import { getTranslation } from '@/hooks/useTranslation';

const Details = ({ project, locale = 'fr' }) => {
  const t = getTranslation(locale, 'projects');

    if (!project) {
        return <p>Projet introuvable</p>;
    }
    
  return (
    <section className="container">
          <div className={styles.contentWrapper}>
            <div className={styles.expertises}>
                <small className="small">{t('expertises')}</small>
                <div className={styles.divider}></div>
                <ul className={styles.list}>
                    {project.categories?.map((category, index) => (
                    <li key={index} className={styles.listItem}>
                        {category}
                    </li>
                    ))}
                </ul>
            </div>

              
            {/* Bloc Technologies */}
            {project.technologies && project.technologies.length > 0 && (
              <div className={styles.expertises}>
                <small className="small">{t('technologies')}</small>
                <div className={styles.divider}></div>
                <ul className={styles.list}>
                    {project.technologies.map((technology, index) => (
                    <li key={index} className={styles.listItem}>
                        {technology}
                    </li>
                    ))}
                </ul>
              </div>
            )}
              
              <div className={styles.expertises}>
                <small className="small">{t('details')}</small>
                <div className={styles.divider}></div>
                    <ul className={styles.list}>
                      <li className={styles.listItem}>{t('year_label')} : {project.year}</li>
                      <li className={styles.listItem}>{t('location_label')} : {project.location}</li>
                      <li className={styles.listItem}>{t('industry_label')} : {project.industry}</li>
                    </ul>
                </div>
        </div>
    </section>
  );
};

export default Details;
