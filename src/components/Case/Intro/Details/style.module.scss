// Container principal
.container {
    transform: matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, -1.2, 0, 1);
    opacity: 1;
    pointer-events: all;
    padding-top: 106.92px;
    margin-top: -1px;
    // padding-bottom: 149.688px;
    display: block;
    box-sizing: border-box;
    position: relative;
    padding-bottom: 100px;
  }
  
  // Wrapper pour organiser le contenu
  .contentWrapper {
    margin: 0 auto;
    max-width: 1600px;
    box-sizing: border-box;
    padding-bottom: calc(var(--section-padding) * 0.66);
  }

@media (min-width: 1200px) { 
    .contentWrapper {
        display: flex;
    }
}
  
  // Sections spécifiques
  .expertises {
    width: calc(33.333% - (clamp(1.5em, 4vw, 2.5em) / 1));
    margin-right: 60px;
    display: block;
    box-sizing: border-box;
    position: relative;
    margin-bottom: calc(var(--section-padding) / 3);
  
    @media (max-width: 768px) {
      width: 100%;
      margin-right: 0;
    }
  }
  
  // Titres des sections
  .sectionTitle {
    font-size: 9.6px;
    text-transform: uppercase;
    letter-spacing: 0.48px;
    opacity: 0.5;
    font-family: '<PERSON> Sans', sans-serif;
    margin-bottom: 9.6px;
    font-weight: 450;
    line-height: 10.224px;
  }
  
  // Ligne de séparation
  .divider {
    margin: 28px 0 24px;
    width: 100%;
    height: 1px;
    background-color: rgba(28, 29, 32, 0.176);
    display: block;
  }
  
  // Liste et éléments de liste
  .list {
    padding: 0;
    margin: 0;
    list-style: none;
  }

  
  // Lien de la section Étude de cas
  .link {
    text-decoration: none;
    color: rgb(193, 2, 6);
    background-color: transparent;
  }
  