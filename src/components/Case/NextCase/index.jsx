"use client";
import React, { useEffect, useState } from "react";
import styles from "./style.module.scss";
import Link from "next/link";
import Image from "next/image";
import AnimatedLink from "@/components/AnimatedLink";
import Mask from "@/components/Mask";
import { useParams } from "next/navigation";
import allProjects from "../../../data/projectsData";
import { getTranslation } from "@/hooks/useTranslation";

function shuffle(array) {
  const arr = array.slice();
  for (let i = arr.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [arr[i], arr[j]] = [arr[j], arr[i]];
  }
  return arr;
}

const NextCase = ({ project: propProject }) => {
  const [relatedProjects, setRelatedProjects] = useState([]);
  const params = useParams();
  const currentSlug = propProject ? propProject.slug : params?.slug;
  const locale = params?.locale || 'fr';
  const t = getTranslation(locale, 'projects');
  
  // On utilise un "trigger" basé sur le timestamp pour forcer un recalcul
  const [trigger, setTrigger] = useState(Date.now());
  
  useEffect(() => {
    // On met à jour le trigger à chaque navigation (si le slug change)
    setTrigger(Date.now());
  }, [currentSlug]);

  useEffect(() => {
    if (!currentSlug) return;
    const currentProject = allProjects.find((p) => p.slug === currentSlug);
    if (!currentProject) return;
    
    let candidates = allProjects.filter(
      (p) =>
        p.expertise === currentProject.expertise &&
        p.slug !== currentSlug &&
        p.visible === true &&
        (p.locale === locale || (!p.locale && locale === 'fr'))
    );
    
    // Si aucun projet ne correspond à l'expertise, on prend le fallback
    if (candidates.length === 0) {
      candidates = allProjects.filter((p) =>
        p.slug !== currentSlug &&
        (p.locale === locale || (!p.locale && locale === 'fr'))
      );
    }
    
    // Mélanger les candidats à chaque fois
    const shuffled = shuffle(candidates);
    const selection = shuffled.slice(0, 2);
    setRelatedProjects(selection);
  }, [currentSlug, trigger, propProject]);

  if (!relatedProjects || relatedProjects.length === 0) return null;

  return (
    <div className={`container ${styles.nextCase}`}>
      <div className={styles.nextCaseHeading}>
        <h2>{t('related_projects')}</h2>
        <AnimatedLink href={`/${locale}/`}>{t('all_projects')}</AnimatedLink>
      </div>
      <div className={styles.nextCaseRow}>
        {relatedProjects.map((project) => {
          const { title, client, src, year, slug } = project;
          return (
            <Link href={`/${locale}/projets/${slug}`} className={styles.project} key={slug}>
              <div className={styles.gridLayout}>
                <div className={styles.imageWrapper}>
                  {src && (
                    <Mask wrap hoverAnim color="transparent">
                      <Image
                        src={`/images/${src}`}
                        alt={title || "Image du projet"}
                        width={600}
                        height={400}
                        style={{ width: "100%", height: "auto" }}
                        sizes="(max-width: 768px) 100vw, 600px"
                      />
                    </Mask>
                  )}
                </div>
                <h3 className={styles.projectTitle}>{title}</h3>
                <div className={styles.separator} />
                <div className={styles.metaRow}>
                  <div className={styles.services}>{client}</div>
                  <div className={styles.year}>{year || "—"}</div>
                </div>
              </div>
            </Link>
          );
        })}
      </div>
    </div>
  );
};

export default NextCase;
