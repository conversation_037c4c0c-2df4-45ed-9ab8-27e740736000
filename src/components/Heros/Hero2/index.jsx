'use client';

import TextReveal from '@/components/TextReveal';
import Image from 'next/image';
import styles from './style.module.scss';
import { motion } from 'framer-motion'


export default function Hero2({
  title,
  subtitle,
  subtitleRight,
  excerpt,
  imgSrc,
  imgAlt = '',
}) {
  return (
    <section className={`${styles.root} container`}>
      {/* ─── Titres ─────────────────────────────────────────────── */}
      <header className={styles.header}>
        <TextReveal as="h1" className={styles.title}>
          {title}
        </TextReveal>

        {(subtitle || subtitleRight) && (
          <div className={styles.subtitles}>
            {subtitle && (
              <TextReveal
                as="p"
                className={styles.subtitle}
                stagger={0.15}
                delayStart={0.4}
              >
                {subtitle}
              </TextReveal>
            )}

{subtitleRight && (
              <div className={styles.subtitleRight}>
                        <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.6, ease: 'easeInOut', delay: 0.2 }}
      >
    <svg
      width="14"
      height="16"
      viewBox="0 0 14 16"
      aria-hidden="true"
      className={styles.subtitleIcon}
    >
      <path d="M0 16V0H14L0 16Z" fill="#08142A" />
                  </svg>
                  </motion.div>

    {/* on anime seulement le texte */}
    <TextReveal as="span" stagger={0.15} delayStart={0.4}>
      {subtitleRight}
    </TextReveal>
  </div>
)}

          </div>
        )}
      </header>

      {/* ─── Texte + Image ─────────────────────────────────────── */}
      <div className={styles.content}>
        <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.6, ease: 'easeInOut', delay: 0.3 }}
        className={styles.textBlock}
      >
          <p className={`${styles.text} ${styles.subtitle}`}>{excerpt}</p>
          </motion.div>
        {imgSrc && (
          <div className={styles.imageContainer}>
                    <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.6, ease: 'easeInOut', delay: 0.5 }}
      >
            <Image
              src={imgSrc}
              alt={imgAlt}
              fill
              sizes="(max-width: 1024px) 100vw, 60vw"
              className={styles.image}
              />
              </motion.div>
          </div>
        )}
      </div>
    </section>
  );
}
