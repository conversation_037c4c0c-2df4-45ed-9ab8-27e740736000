// export const expand = {
//     initial: {
//         top: 0
//     },
//     enter: (i) => ({    
//         top: "100vh",
//         transition: {
//             duration: 0.9,
//             delay: 0.05 * i,
//             ease: [0.215, 0.61, 0.355, 1],
//         },
//         transitionEnd: { height: "0", top: "0" }
//     }),
//     exit: (i) => ({
//         height: "100vh",
//         transition: {
//             duration: 0.4,
//             delay: 0.05 * i,
//             ease: [0.215, 0.61, 0.355, 1]
//         }
//     })
// }
// export const opacity = {
//     initial: {
//         opacity: 0.5
//     },
//     enter: {
//         opacity: 0
//     },
//     exit: {
//         opacity: 0.5,
        
//     }
// }
// src/components/Stairs/anim.js
export const expand = {
    initial: { top: 0 },
    enter: (i) => ({
      top: "100vh",
      transition: {
        duration: 0.9,
        delay: 0.05 * i,
        ease: [0.215, 0.61, 0.355, 1],
      },
      transitionEnd: { height: "0", top: "0" },
    }),
    exit: (i) => ({
      height: "100vh",
      transition: {
        duration: 0.4,
        delay: 0.05 * i,
        ease: [0.215, 0.61, 0.355, 1],
      },
    }),
  };
  
  export const opacity = {
    initial: { opacity: 0.5 },
    enter: { opacity: 0 },
    exit: { opacity: 0.5 },
  };
  
  // Pour l'animation réversible des "stairs" (couvrir/découvrir)
  export const stairsVariants = {
    initial: { y: "-100vh" },
    cover: (i) => ({
      y: 0,
      transition: {
        duration: 0.9,
        delay: 0.05 * i,
        ease: [0.215, 0.61, 0.355, 1],
      },
    }),
    uncover: (i) => ({
      y: "-100vh",
      transition: {
        duration: 0.9,
        delay: 0.05 * i,
        ease: [0.215, 0.61, 0.355, 1],
      },
    }),
  };
  