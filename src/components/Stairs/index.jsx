'use client'

import React from 'react'
import { AnimatePresence, motion } from 'framer-motion'
import { usePathname } from 'next/navigation'
import { opacity, expand } from './anim'

// tes variants (opacity, expand) sont inchangés
// Tu les importes depuis un fichier `anim.js` ou autre

export default function StairsLayout({ children, backgroundColor }) {
  const pathname = usePathname()
  const nbOfColumns = 3

  // fonction utilitaire pour passer initial/animate/exit + custom
  const anim = (variants, custom = null) => ({
    initial: 'initial',
    animate: 'enter',
    exit: 'exit',
    custom,
    variants
  })

  // AnimatePresence entourant la div « page stairs » ;
  // key = pathname pour que l’animation exit se lance à chaque changement de route
  return (
    <AnimatePresence mode='wait'>
      <motion.div
        key={pathname}
        className='page stairs'
        style={{ backgroundColor }}
      >
        <motion.div {...anim(opacity)} className='transition-background' />
        <div className='transition-container'>
          {[...Array(nbOfColumns)].map((_, i) => (
            <motion.div
              key={i}
              {...anim(expand, nbOfColumns - i)}
            />
          ))}
        </div>

        {/* ton contenu de page */}
        {children}
      </motion.div>
    </AnimatePresence>
  )
}
