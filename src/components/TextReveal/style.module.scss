// Force l'héritage des styles typographiques pour tous les éléments TextReveal
.textRevealContainer {
  // Le conteneur hérite des styles du tag parent

  span {
    display: inline-block;
    white-space: pre-wrap;

    // Force l'héritage complet des propriétés typographiques
    font-size: inherit !important;
    font-weight: inherit !important;
    font-family: inherit !important;
    letter-spacing: inherit !important;
    word-spacing: inherit !important;
    line-height: inherit !important;
    color: inherit !important;
    text-transform: inherit !important;
    font-style: inherit !important;
  }
}
