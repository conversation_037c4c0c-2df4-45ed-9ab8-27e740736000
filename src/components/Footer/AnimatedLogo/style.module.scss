.logo {
    overflow: hidden;
    display: flex;
    // align-items: center;
    // justify-content: center;
    margin-top: calc(var(--section-padding) / 2);
    margin-bottom: calc(var(--section-padding) / 2);
  }
  
  .letter {
    font-size: calc(3.2rem + 4vw);
    font-weight: 500;
    /* On retire letter-spacing et word-spacing */
    /* letter-spacing: calc(-1px - 1.2vw);
    word-spacing: calc(5px + 0.5vw); */
  }
  
  .letter:not(:last-child) {
    margin-right: calc(-1px - 1.2vw);
  }
  
  @media screen and (min-width: 768px) {
    .letter {
      font-size: calc(6.2rem + 5vw) !important;
    }
  }
  
  @media screen and (min-width: 992px) {
    .letter {
      font-size: calc(7.2rem + 6vw) !important;
    }
    .letter:not(:last-child) {
      margin-right: calc(-1px - 1.4vw) !important;
    }
  }
  