// components/AnimatedLogo.jsx
import { motion, useTransform, useMotionValue } from 'framer-motion';
import { useEffect } from 'react';
import styles from './style.module.scss';

export default function AnimatedKapreon({ scrollTarget }) {
  const letters = 'Kapreon'.split('');
  // Motion value pour la progression (0 à 1)
  const progress = useMotionValue(0);

  useEffect(() => {
    function onScroll() {
      if (scrollTarget && scrollTarget.current) {
        const rect = scrollTarget.current.getBoundingClientRect();
        let p = 0;
        if (window.innerWidth < 768) {
          // Sur mobile, on utilise 50 % de la hauteur du footer pour l'animation
          // p = (window.innerHeight - rect.top) / (rect.height * 0.1);
          p = (window.innerHeight - (rect.top + rect.height / 1.7)) / (rect.height / 1.7);
        } else {
          // Sur desktop, on utilise la moitié de la hauteur du footer
          p = (window.innerHeight - (rect.top + rect.height / 2)) / (rect.height / 2);
        }
        if (p < 0) p = 0;
        if (p > 1) p = 1;
        progress.set(p);
      }
    }
    window.addEventListener('scroll', onScroll);
    onScroll();
    return () => window.removeEventListener('scroll', onScroll);
  }, [scrollTarget, progress]);

  return (
    <div className={styles.logo}>
      {letters.map((letter, index) => (
        <AnimatedLetter 
          key={index} 
          letter={letter} 
          index={index} 
          progress={progress} 
        />
      ))}
    </div>
  );
}

function AnimatedLetter({ letter, index, progress }) {
  const start = index * 0.05;
  const end = start + 0.3;
  const y = useTransform(progress, [start, end], [50, 0]);
  const opacity = useTransform(progress, [start, end], [0, 1]);
  return (
    <motion.span style={{ opacity, y }} className={styles.letter}>
      {letter}
    </motion.span>
  );
}
