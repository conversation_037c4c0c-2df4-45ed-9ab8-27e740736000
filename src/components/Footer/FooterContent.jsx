// Temporary component to fix encoding issues
import React from 'react';
import { motion } from 'framer-motion';
import AnimatedLink from '@/components/AnimatedLink';

export default function FooterContent({ locale, t, itemVariants }) {
  return (
    <div className="footerBlocks">
      <ul className="footerBlock">
        <li>
          <motion.div variants={itemVariants}>
            <AnimatedLink href={`/${locale}`} standalone>
              {t('case_studies')}
            </AnimatedLink>
          </motion.div>
        </li>
        <li>
          <motion.div variants={itemVariants}>
            <AnimatedLink href={`/${locale}/questions-frequentes`} standalone>
              {t('faq')}
            </AnimatedLink>
          </motion.div>
        </li>
        <li>
          <motion.div variants={itemVariants}>
            <AnimatedLink href={`/${locale}/contact`} standalone>
              {t('contact_agency')}
            </AnimatedLink>
          </motion.div>
        </li>
        <li>
          <motion.div variants={itemVariants}>
            <AnimatedLink href={`/${locale}/blog`} standalone>
              {t('blog')}
            </AnimatedLink>
          </motion.div>
        </li>
      </ul>
    </div>
  );
}
