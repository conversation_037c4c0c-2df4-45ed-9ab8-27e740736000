.customLink {
  appearance: none;
  background-color: transparent;
  border: 0;
  border-radius: 0;
  color: currentColor;
  cursor: pointer;
  display: inline;
  line-height: 1.4;
  outline: revert;
  overflow: visible;
  padding: 0;
  position: relative;
  text-decoration: none;
  user-select: none;
  font-weight: 500;
}

@media (min-width: 768px) {
  .customLink::after {
    content: "";
    position: absolute;
    left: 0;
    bottom: -3px;
    height: 1px;
    width: 100%;
    z-index: 1;
    background-image: linear-gradient(
      90deg,
      currentColor 0%,
      currentColor 33.33%,
      transparent 33.33%,
      transparent 66.66%,
      currentColor 66.66%,
      currentColor 100%
    );
    background-repeat: no-repeat;
    background-size: 300% 1px;
    transition: background-position 1s ease;
  }

  /* liens normaux : underline caché au départ (100%) */
  .customLink:not(.customLinkStandalone)::after {
    background-position: 100%;
  }

  /* liens standalone : underline partiellement visible au départ (50%) */
  .customLink.customLinkStandalone::after {
    background-position: 50%;
  }

  /* au survol, pour tous les liens, l'underline s'anime vers 0 */
  .customLink:hover::after {
    background-position: 0;
  }
}