import Link from 'next/link';
import styles from './style.module.scss';

export default function AnimatedLink({ href, children, className = '', standalone = false, external = false, ...props }) {
  // Si c'est un lien externe, on utilise un <a> au lieu de Next.js Link
  if (external) {
    return (
      <a
        href={href}
        className={`${styles.customLink} ${className} ${standalone ? styles.customLinkStandalone : ''}`}
        target="_blank"
        rel="noopener noreferrer"
        {...props}
      >
        {children}
      </a>
    );
  }

  return (
    <Link
      href={href}
      className={`${styles.customLink} ${className} ${standalone ? styles.customLinkStandalone : ''}`}
      {...props}
    >
      {children}
    </Link>
  );
}