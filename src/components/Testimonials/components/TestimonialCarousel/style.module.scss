.carousel {
  position: relative;
  width: 100%;
}

.embla {
  overflow: hidden;
}

.emblaContainer {
  display: flex;
  gap: var(--gap-padding);
  padding: var(--gap-padding) 0;
}

.emblaSlide {
  flex: 0 0 auto;
  width: 600px; // Plus large pour voir moins de témoignages
  min-width: 0;
}

.testimonialCard {
  background: #f5f5f5;
  padding: calc(var(--gap-padding) * 1.5);
  height: 200px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  transition: transform 0.2s ease;
  cursor: pointer;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;

  &:hover {
    transform: translateY(-2px);
  }
}

.author {
  margin: 0;
  color: rgba(28, 29, 32, 0.667);
  
  &::before {
    content: '— ';
    font-weight: 400;
  }
}

.progressIndicator {
  width: 100%;
  height: 2px;
  background: rgba(28, 29, 32, 0.1);
  overflow: hidden;

  .progressBar {
    height: 100%;
    background: #1c1d20;
    transition: width 0.1s ease;
  }
}

@media (max-width: 480px) {
  .emblaSlide {
    width: 320px;
  }
}
