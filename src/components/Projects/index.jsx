"use client";
import { useState, useEffect, useRef, useMemo } from "react";
import { motion } from "framer-motion";
import gsap from "gsap";
import Image from "next/image";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import styles from "./style.module.scss";
import projects from "../../data/projectsData";

// plugins
gsap.registerPlugin(ScrollTrigger);

// composants
import Filters from "./components/filters";
import Project from "./components/project";
import { getTranslation } from "@/hooks/useTranslation";
import CursorFollower, { useCursorFollower } from '@/components/CursorFollower';

const scaleAnimation = {
  initial: { scale: 0, x: "-50%", y: "-50%" },
  enter: { scale: 1, x: "-50%", y: "-50%", transition: { duration: 0.4, ease: [0.76, 0, 0.24, 1] } },
  closed: { scale: 0, x: "-50%", y: "-50%", transition: { duration: 0.4, ease: [0.32, 0, 0.67, 0] } }
};

// en haut du composant
const headerVariants = {
  hidden: {},
  visible: {
    transition: {
      delayChildren: 0.6,    // délai avant la 1ʳᵉ colonne
      staggerChildren: 0.15, // 150 ms entre chaque
    }
  }
};
const headerItemVariants = {
  hidden: { y: 10, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: { duration: 0.4, ease: "easeOut" }
  }
};

export default function Home({ isLoading, locale = 'fr' }) {
  const [modal, setModal] = useState({ active: false, index: 0 });
  const { active, index } = modal;
  const t = getTranslation(locale, 'projects');
  const [filter, setFilter] = useState('all'); // Utiliser l'ID au lieu du label
  const [viewMode, setViewMode] = useState("list"); // état pour le mode d’affichage

  // Hook pour gérer la bulle qui suit la souris
  const { isVisible: showCursor, showCursor: showCursorFollower, hideCursor, updatePosition, cursorRef } = useCursorFollower();

  const modalContainer = useRef(null);
  let xMoveContainer = useRef(null);
  let yMoveContainer = useRef(null);

  // useEffect(() => {
  //   if (!isLoading) {
  //     gsap.utils.toArray(".fadeMe").forEach((elem) => {
  //       gsap.fromTo(
  //         elem,
  //         { y: 50, opacity: 0 },
  //         {
  //           y: 0,
  //           opacity: 1,
  //           duration: 1,
  //           scrollTrigger: {
  //             trigger: elem,
  //             start: "top 85%",
  //             toggleActions: "play none none none"
  //           }
  //         }
  //       );
  //     });
  //   }
  // }, [isLoading]);
  
  useEffect(() => {
    xMoveContainer.current = gsap.quickTo(modalContainer.current, "left", { duration: 0.8, ease: "power3" });
    yMoveContainer.current = gsap.quickTo(modalContainer.current, "top", { duration: 0.8, ease: "power3" });
  }, []);

  const moveItems = (x, y) => {
    xMoveContainer.current?.(x);
    yMoveContainer.current?.(y);
    updatePosition(x, y);
  };

  const manageModal = (bool, projectIndex, x, y) => {
    moveItems(x, y);

    // Si projectIndex est négatif, on affiche seulement la bulle (pas l'image modale)
    if (projectIndex < 0) {
      setModal({ active: false, index: 0 }); // Image modale désactivée
      if (bool) {
        showCursorFollower(x, y); // Afficher la bulle "Explorer"
      } else {
        hideCursor(); // Cacher la bulle
      }
    } else {
      setModal({ active: bool, index: projectIndex }); // Comportement normal
      if (bool) {
        showCursorFollower(x, y); // Afficher la bulle "Explorer"
      } else {
        hideCursor(); // Cacher la bulle
      }
    }
  };

  const handleFilterChange = (filterId) => setFilter(filterId);

  // D'abord filtrer par langue
  const projectsByLanguage = projects.filter((p) => {
    const matchesLocale = p.locale === locale || (!p.locale && locale === 'fr');
    return matchesLocale;
  });

  // Puis filtrer par expertise en utilisant les IDs
  const filteredProjects = projectsByLanguage.filter((p) => {
    if (filter === 'all') return true;
    return p.expertiseId === filter;
  });



  // dès qu’on change de filtre, on ferme la modale
useEffect(() => {
  setModal({ active: false, index: 0 });
}, [filter]);

// Mettre à jour le filtre seulement quand la langue change (pas à chaque rendu)
useEffect(() => {
  setFilter('all');
}, [locale]);


  return (
    <>
      <div className={`${styles.filtersWrapper} fadeMe`}>
        <Filters
          projects={projectsByLanguage}
          onFilterChange={handleFilterChange}
          activeViewMode={viewMode}
          onViewModeChange={(mode) => setViewMode(mode)}
          locale={locale}
          activeFilter={filter}
        />
      </div>

      <main
  onMouseMove={(e) => moveItems(e.clientX, e.clientY)}
  className={`${styles.projects} container`}
>
  {viewMode === "list" && (
        <motion.div
        className={styles.headerRow}
        variants={headerVariants}
        initial="hidden"
        animate="visible"
        // ou pour déclencher au scroll :
        // initial="hidden"
        // whileInView="visible"
        // viewport={{ once: true, amount: 0.2 }}
      >
        <motion.div className={styles.colClient}  variants={headerItemVariants}>
          <h5>{locale === 'en' ? 'Project' : 'Projet'}</h5>
        </motion.div>
        <motion.div className={styles.colLocation} variants={headerItemVariants}>
          <h5>{locale === 'en' ? 'Location' : 'Localisation'}</h5>
        </motion.div>
        <motion.div className={styles.colServices} variants={headerItemVariants}>
          <h5>{locale === 'en' ? 'Expertise' : 'Expertise'}</h5>
        </motion.div>
      </motion.div>
  )}

  <div className={`${styles.body} ${viewMode === "list" ? styles.listView : styles.gridView}`}>
    {filteredProjects.map((project, index) => (
      <div className={`${styles.singleProject}`} key={project.slug}>
        <Project
          index={index}
          title={project.title}
          client={project.client}
          expertise={project.expertise}
          year={project.year}
          location={project.location}
          slug={project.slug}
          manageModal={manageModal}
          src={project.src}
          locale={locale}
          viewMode={viewMode}
        />
      </div>
    ))}
  </div>

  {/* Modal et autres éléments */}






        {/* Modal et animations */}
        <>
          <motion.div
            ref={modalContainer}
            variants={scaleAnimation}
            initial="initial"
            animate={active ? "enter" : "closed"}
            className={styles.modalContainer}
          >
      <div style={{ top: index * -100 + "%" }} className={styles.modalSlider}>
        {filteredProjects.map((p, i) => (
                <div className={styles.modal} style={{ backgroundColor: p.color }} key={`modal_${i}`}>
                  {/* <Image src={`/images/${p.src}`} width={300} height={0} alt="image" /> */}
                  <Image
        src={`/images/${p.src}`}
        width={300}
        height={200}         // Au lieu de 0
        alt="image"
        style={{ width: "75%", height: "auto" }} 
      />
                </div>
              ))}
            </div>
          </motion.div>

          {/* Bulle qui suit la souris */}
          <CursorFollower
            ref={cursorRef}
            text={t('explore')}
            backgroundColor="#FF413D"
            textColor="white"
            size={80}
            fontSize={14}
            fontWeight={300}
            isVisible={showCursor}
          />
        </>
      </main>
    </>
  );
}
