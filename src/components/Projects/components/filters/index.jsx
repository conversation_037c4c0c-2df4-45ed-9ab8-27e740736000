'use client';
import { useMemo } from "react";
import { motion } from "framer-motion";
import styles from "./style.module.scss";
import Rounded from "../../../../common/RoundedButton";
import { getTranslation } from "@/hooks/useTranslation";
import { getAllExpertises } from "@/data/taxonomies";

export default function Filters({ projects, onFilterChange, onViewModeChange, activeViewMode, locale = 'fr', activeFilter }) {
  const t = getTranslation(locale, 'projects');

  // Obtenir toutes les expertises disponibles
  const availableExpertises = getAllExpertises(locale);

  // Comptage dynamique basé sur les expertises réellement présentes dans les projets
  const expertiseCounts = useMemo(() => {
    const counts = {};

    // Compter tous les projets
    counts.all = projects.length;

    // Compter par expertise
    availableExpertises.forEach(expertise => {
      counts[expertise.id] = projects.filter(project =>
        project.expertiseId === expertise.id
      ).length;
    });

    return counts;
  }, [projects, availableExpertises]);

  // Construction dynamique des filtres
  const filters = useMemo(() => {
    const baseFilters = [
      {
        id: 'all',
        name: t('filter_all'),
        count: expertiseCounts.all
      }
    ];

    // Ajouter les filtres d'expertise qui ont au moins un projet
    const expertiseFilters = availableExpertises
      .filter(expertise => expertiseCounts[expertise.id] > 0)
      .map(expertise => ({
        id: expertise.id,
        name: expertise.label,
        count: expertiseCounts[expertise.id]
      }));

    return [...baseFilters, ...expertiseFilters];
  }, [availableExpertises, expertiseCounts, t]);

  const handleFilterClick = (filterId) => {
    onFilterChange(filterId);
  };

  const handleViewModeClick = (mode) => onViewModeChange(mode);

  // activeFilter contient déjà l'ID correct, pas besoin de conversion
  const activeFilterId = activeFilter;

  return (
    <section className={`${styles.filterRow} container`}>
      <div className={styles.toggleRow}>
        {filters.map(({ id, name, count }, i) => (
          <motion.div
            key={id}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: i * 0.1, duration: 0.4, ease: "easeOut" }}
          >
            <Rounded
              onClick={() => handleFilterClick(id)}
              count={count}
              isActive={activeFilterId === id}
              exclusive
            >
              <p className={activeFilterId === id ? styles.activeFilter : ""}>
                {name}
              </p>
            </Rounded>
          </motion.div>
        ))}
      </div>

      <div className={styles.viewModeRow}>
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5, duration: 0.4, ease: "easeOut" }}
        >
          <Rounded
            onClick={() => handleViewModeClick("list")}
            isActive={activeViewMode === "list"}
            className={styles.viewModeButton}
            style={{
              width: '60px',
              height: '60px',
              borderRadius: '50%',
              padding: '0',
              minWidth: '50px',
              minHeight: '50px'
            }}
          >
            <p>
              <svg viewBox="0 0 20 19" fill="currentColor" className={styles.svgFilter}>
                <path d="M0 6h20v1H0zM0 0h20v1H0zM0 12h20v1H0zM0 18h20v1H0z" />
              </svg>
            </p>
          </Rounded>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6, duration: 0.4, ease: "easeOut" }}
        >
          <Rounded
            onClick={() => handleViewModeClick("grid")}
            isActive={activeViewMode === "grid"}
            className={styles.viewModeButton}
            style={{
              width: '60px',
              height: '60px',
              borderRadius: '50%',
              padding: '0',
              minWidth: '50px',
              minHeight: '50px'
            }}
          >
            <p>
              <svg viewBox="0 0 20 20" fill="currentColor" className={styles.svgFilter}>
                <path d="M8 0H0v8h8V0zM7 1v6H1V1h6zM8 12H0v8h8v-8zm-1 1v6H1v-6h6zM20 0h-8v8h8V0zm-1 1v6h-6V1h6zM20 12h-8v8h8v-8zm-1 1v6h-6v-6h6z" />
              </svg>
            </p>
          </Rounded>
        </motion.div>
      </div>
    </section>
  );
}
