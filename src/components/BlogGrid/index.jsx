'use client';

import Masonry from 'react-masonry-css';
import BlogCard from '@/components/BlogCard';

export default function BlogGrid({ posts }) {
  /* breakpoints : 1 col <640 px, 2 cols <1024 px, 3 cols au‑dessus  */
  const cols = { default: 3, 1024: 2, 640: 1 };

  return (
    <Masonry breakpointCols={cols} className="masonry" columnClassName="masonry-col">
      {posts.map((p, i) => (
        <BlogCard key={p._id} post={p} big={i % 2 === 0} />
      ))}
    </Masonry>
  );
}
