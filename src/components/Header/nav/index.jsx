import React, { useState } from 'react'
import styles from './style.module.scss';
import { motion } from 'framer-motion';
import { usePathname } from 'next/navigation';
import { menuSlide } from '../animation';
import Link from './Link';
import Curve from './Curve';
import Footer from './Footer';
import { useTranslation } from '@/hooks/useTranslation';

export default function Index({ locale = 'fr' }) {
  const { t } = useTranslation('navigation');
  const pathname = usePathname();
  const [selectedIndicator, setSelectedIndicator] = useState(pathname);

  const navItems = [
    {
      title: t('home'),
      href: `/${locale}`,
    },
    {
      title: t('projects'),
      href: `/${locale}/projets`,
    },
    {
      title: t('blog'),
      href: `/${locale}/blog`,
    },
    {
      title: t('contact'),
      href: `/${locale}/contact`,
    },
  ];

  return (
    <motion.div 
      variants={menuSlide} 
      initial="initial" 
      animate="enter" 
      exit="exit" 
      className={styles.menu}
      >
       <div className={styles.body}>
            <div onMouseLeave={() => {setSelectedIndicator(pathname)}} className={styles.nav}>
                    <div className={styles.header}>
                        <p>{t('navigation')}</p>
                    </div>
                    {
                      navItems.map( (data, index) => {
                        return <Link 
                        key={index} 
                        data={{...data, index}} 
                        isActive={selectedIndicator == data.href} 
                        setSelectedIndicator={setSelectedIndicator}>
                        </Link>
                      })
                    }
            </div>
            <Footer />
        </div>
        <Curve />
    </motion.div>
  )
}