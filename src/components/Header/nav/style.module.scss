.menu{
    height: 100vh;
    background-color: rgb(41, 41, 41);
    position: fixed;
    right: 0;
    top: 0;
    color: white;
    z-index: 3;
    .body{
        box-sizing: border-box;
        height: 100%;
        padding: 100px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .nav{
            display: flex;
            flex-direction: column;
            font-size: 56px;
            gap: 12px;
            margin-top: 80px;

            .header{
                color: rgb(153, 153, 153);
                border-bottom: 1px solid rgb(153, 153, 153);
                text-transform: uppercase;
                font-size: 11px;
                margin-bottom: 40px;
            }

            a{
                text-decoration: none;
                color: white;
                font-weight: 300;
            }
            
        }
    }
}