/* components/Tweet.tsx */
'use client';

import { useEffect, useRef } from 'react';
import Script from 'next/script';

type Props = { id: string };

export default function Tweet({ id }: Props) {
  const ref = useRef<HTMLDivElement>(null);

  // charge le script Twitter une seule fois
  useEffect(() => {
    // @ts-ignore – global twttr injecté par le script
    const load = () => window.twttr?.widgets?.createTweet(id, ref.current!);

    if (window.twttr) load();
    else window.addEventListener('twitterLoaded', load);

    return () => window.removeEventListener('twitterLoaded', load);
  }, [id]);

  return (
    <>
      {/* script partagé par toutes les occurrences */}
      <Script
        id="twitter-widgets"
        src="https://platform.twitter.com/widgets.js"
        strategy="afterInteractive"
        onLoad={() => window.dispatchEvent(new Event('twitterLoaded'))}
      />
      <div ref={ref} />
    </>
  );
}
