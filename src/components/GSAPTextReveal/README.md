# GSAPTextReveal

Composant d'animation de texte utilisant GSAP SplitText pour des animations performantes et fluides.

## Fonctionnalités

- ✅ Animation par caractères, mots ou lignes
- ✅ Presets prédéfinis pour une utilisation rapide
- ✅ ScrollTrigger intégré
- ✅ API flexible avec callbacks
- ✅ Nettoyage automatique des animations
- ✅ Support des refs pour contrôle manuel
- ✅ Optimisé pour les performances

## Installation

Le composant utilise GSAP SplitText qui est maintenant gratuit avec GSAP 3.12+.

```bash
npm install gsap
```

## Utilisation de base

```jsx
import GSAPTextReveal from '@/components/GSAPTextReveal';

// Animation simple par caractères
<GSAPTextReveal as="h1">
  Mon titre animé
</GSAPTextReveal>

// Avec preset
import { getPreset } from '@/components/GSAPTextReveal/presets';

<GSAPTextReveal 
  as="h1" 
  {...getPreset('hero')}
>
  Titre principal
</GSAPTextReveal>
```

## Props

| Prop | Type | Défaut | Description |
|------|------|--------|-------------|
| `as` | string | 'div' | Tag HTML à utiliser |
| `children` | ReactNode | - | Contenu texte à animer |
| `className` | string | '' | Classes CSS additionnelles |
| `splitBy` | 'chars'\|'words'\|'lines' | 'chars' | Type de division du texte |
| `stagger` | number | 0.02 | Délai entre chaque élément |
| `duration` | number | 0.6 | Durée de l'animation |
| `delay` | number | 0 | Délai avant le début |
| `ease` | string | 'power2.out' | Type d'easing GSAP |
| `from` | object | `{y: 100, opacity: 0, rotationX: 90}` | État initial |
| `to` | object | `{y: 0, opacity: 1, rotationX: 0}` | État final |
| `triggerOnScroll` | boolean | true | Déclencher au scroll |
| `triggerStart` | string | 'top 80%' | Position de déclenchement |
| `triggerOnce` | boolean | true | Animation une seule fois |
| `onComplete` | function | - | Callback fin d'animation |
| `onStart` | function | - | Callback début d'animation |

## Presets disponibles

- `default` - Animation douce par caractères
- `hero` - Pour les titres principaux
- `subtitle` - Pour les sous-titres
- `energetic` - Animation rapide et énergique
- `elegant` - Animation élégante avec rotation
- `words` - Animation par mots
- `lines` - Animation par lignes
- `typewriter` - Effet machine à écrire
- `glitch` - Effet cyberpunk/glitch

## Exemples avancés

```jsx
// Animation personnalisée
<GSAPTextReveal
  as="h2"
  splitBy="chars"
  stagger={0.03}
  duration={0.8}
  from={{ y: 50, opacity: 0, scale: 0.8 }}
  to={{ y: 0, opacity: 1, scale: 1 }}
  ease="back.out(1.7)"
  onComplete={() => console.log('Animation terminée')}
>
  Titre avec animation personnalisée
</GSAPTextReveal>

// Contrôle manuel avec ref
const textRef = useRef();

<GSAPTextReveal
  ref={textRef}
  triggerOnScroll={false}
  {...getPreset('elegant')}
>
  Texte contrôlé manuellement
</GSAPTextReveal>

// Déclencher manuellement
textRef.current?.play();
textRef.current?.reverse();
textRef.current?.restart();
```

## Performance

- Utilise `will-change` et `backface-visibility` pour l'optimisation GPU
- Nettoyage automatique des animations et ScrollTriggers
- Gestion efficace de la mémoire avec `revert()` de SplitText

## Migration depuis TextReveal

Remplacez simplement :
```jsx
// Ancien
<TextReveal as="h1" stagger={0.12} delayStart={0.5}>
  Mon titre
</TextReveal>

// Nouveau
<GSAPTextReveal as="h1" {...getPreset('hero', { delay: 0.5 })}>
  Mon titre
</GSAPTextReveal>
```
