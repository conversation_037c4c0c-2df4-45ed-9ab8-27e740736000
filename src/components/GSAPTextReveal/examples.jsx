'use client';

import GSAPTextReveal from './index';
import { getPreset } from './presets';
import styles from './examples.module.scss';

/**
 * Composant d'exemples pour tester les différents presets
 * Utile pour le développement et les tests
 */
export default function GSAPTextRevealExamples() {
  return (
    <div className={styles.examples}>
      <div className="container">
        <h1>GSAPTextReveal - Exemples</h1>
        
        <section className={styles.section}>
          <h2>Preset: Hero</h2>
          <GSAPTextReveal 
            as="h1" 
            className={styles.heroTitle}
            {...getPreset('hero')}
          >
            Titre Principal Animé
          </GSAPTextReveal>
        </section>

        <section className={styles.section}>
          <h2>Preset: Subtitle</h2>
          <GSAPTextReveal 
            as="p" 
            className={styles.subtitle}
            {...getPreset('subtitle')}
          >
            Sous-titre avec animation douce par mots
          </GSAPTextReveal>
        </section>

        <section className={styles.section}>
          <h2>Preset: Energetic</h2>
          <GSAPTextReveal 
            as="h3" 
            className={styles.energeticTitle}
            {...getPreset('energetic')}
          >
            Animation Énergique !
          </GSAPTextReveal>
        </section>

        <section className={styles.section}>
          <h2>Preset: Elegant</h2>
          <GSAPTextReveal 
            as="h3" 
            className={styles.elegantTitle}
            {...getPreset('elegant')}
          >
            Animation Élégante avec Rotation
          </GSAPTextReveal>
        </section>

        <section className={styles.section}>
          <h2>Preset: Typewriter</h2>
          <GSAPTextReveal 
            as="p" 
            className={styles.typewriter}
            {...getPreset('typewriter')}
          >
            Effet machine à écrire caractère par caractère...
          </GSAPTextReveal>
        </section>

        <section className={styles.section}>
          <h2>Preset: Glitch</h2>
          <GSAPTextReveal 
            as="h3" 
            className={styles.glitch}
            {...getPreset('glitch')}
          >
            EFFET CYBERPUNK
          </GSAPTextReveal>
        </section>

        <section className={styles.section}>
          <h2>Animation par lignes</h2>
          <GSAPTextReveal 
            as="p" 
            className={styles.paragraph}
            {...getPreset('lines')}
          >
            Ceci est un paragraphe avec plusieurs lignes qui s'animent une par une.
            Chaque ligne apparaît avec un délai pour créer un effet de révélation progressive.
            C'est parfait pour les textes plus longs et les descriptions.
          </GSAPTextReveal>
        </section>

        <section className={styles.section}>
          <h2>Animation personnalisée</h2>
          <GSAPTextReveal 
            as="h3" 
            className={styles.custom}
            splitBy="chars"
            stagger={0.05}
            duration={1}
            ease="elastic.out(1, 0.3)"
            from={{ y: 100, opacity: 0, rotation: 180, scale: 0 }}
            to={{ y: 0, opacity: 1, rotation: 0, scale: 1 }}
          >
            Animation Complètement Personnalisée
          </GSAPTextReveal>
        </section>
      </div>
    </div>
  );
}
