'use client';

import { Children, isValidElement, cloneElement, useRef, useEffect, useState } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { getPreset } from './presets';

/**
 * Composant spécialisé pour animer des textes avec plusieurs paragraphes
 * Préserve l'espacement entre les paragraphes tout en appliquant l'animation GSAP
 * 
 * @param {Object} props - Les propriétés du composant
 * @param {React.ReactNode} props.children - Le contenu JSX avec <br/><br/> ou paragraphes
 * @param {string} props.className - Classes CSS additionnelles
 * @param {string} props.preset - Preset d'animation à utiliser (par défaut 'lines')
 * @param {Object} props.presetOverrides - Surcharges pour le preset
 * @param {number} props.paragraphSpacing - Espacement entre paragraphes en rem (par défaut 1.5)
 */
export default function GSAPParagraphs({
  children,
  className = '',
  preset = 'lines',
  presetOverrides = {},
  paragraphSpacing = 1.5,
  ...props
}) {
  const containerRef = useRef(null);
  const [SplitText, setSplitText] = useState(null);

  // Charger SplitText dynamiquement
  useEffect(() => {
    if (typeof window !== 'undefined' && !SplitText) {
      import('gsap/SplitText').then((module) => {
        setSplitText(() => module.SplitText);
        gsap.registerPlugin(module.SplitText, ScrollTrigger);
      });
    }
  }, [SplitText]);
  // Fonction pour traiter le contenu et séparer les paragraphes
  const processParagraphs = (content) => {
    if (typeof content === 'string') {
      // Si c'est une string simple, pas de traitement spécial nécessaire
      return [content];
    }

    // Si c'est du JSX, on doit le traiter différemment
    if (isValidElement(content) && content.type === 'React.Fragment' || Array.isArray(content.props?.children)) {
      const children = Array.isArray(content.props?.children) ? content.props.children : [content.props.children];

      // Séparer le contenu par les <br/><br/>
      const paragraphs = [];
      let currentParagraph = [];
      let skipNext = false;

      children.forEach((child, index) => {
        if (skipNext) {
          skipNext = false;
          return;
        }

        if (typeof child === 'string') {
          currentParagraph.push(child);
        } else if (isValidElement(child) && child.type === 'br') {
          // Vérifier si le prochain élément est aussi un <br/>
          const nextChild = children[index + 1];
          if (isValidElement(nextChild) && nextChild.type === 'br') {
            // Double <br/> = nouveau paragraphe
            if (currentParagraph.length > 0) {
              paragraphs.push(currentParagraph.join('').trim());
              currentParagraph = [];
            }
            // Marquer pour ignorer le prochain <br/>
            skipNext = true;
          } else {
            // Simple <br/> = saut de ligne dans le même paragraphe
            currentParagraph.push('\n');
          }
        } else if (child !== null) {
          currentParagraph.push(child);
        }
      });

      // Ajouter le dernier paragraphe s'il existe
      if (currentParagraph.length > 0) {
        paragraphs.push(currentParagraph.join('').trim());
      }

      return paragraphs.filter(p => p.length > 0);
    }

    // Fallback : traiter comme un seul paragraphe
    return [content];
  };

  const paragraphs = processParagraphs(children);

  // Animation GSAP personnalisée pour les paragraphes séquentiels
  useEffect(() => {
    if (!containerRef.current || !SplitText) return;

    const container = containerRef.current;
    const paragraphElements = container.querySelectorAll('p');

    if (paragraphElements.length === 0) return;

    // Obtenir la configuration du preset
    const config = getPreset(preset, presetOverrides);

    // Créer une timeline maître
    const masterTimeline = gsap.timeline({ paused: true });

    // Animer chaque paragraphe séquentiellement
    paragraphElements.forEach((paragraph, index) => {
      // Créer SplitText pour ce paragraphe
      const split = new SplitText(paragraph, {
        type: 'lines',
        linesClass: 'gsap-line'
      });

      // Wrapper chaque ligne pour l'overflow hidden
      split.lines.forEach((line) => {
        const wrapper = document.createElement('div');
        wrapper.className = 'gsap-line-wrapper';
        wrapper.style.overflow = 'hidden';
        wrapper.style.display = 'block';
        line.parentNode.insertBefore(wrapper, line);
        wrapper.appendChild(line);
      });

      // Définir l'état initial
      gsap.set(split.lines, config.from);

      // Ajouter l'animation à la timeline avec délai séquentiel
      const startTime = index * .5; // 1.5 secondes entre chaque paragraphe

      masterTimeline.to(split.lines, {
        ...config.to,
        duration: config.duration,
        ease: config.ease,
        stagger: config.stagger
      }, startTime);
    });

    // Configurer ScrollTrigger
    ScrollTrigger.create({
      trigger: container,
      start: 'top 80%',
      once: true,
      onEnter: () => masterTimeline.play()
    });

    // Cleanup
    return () => {
      masterTimeline.kill();
      ScrollTrigger.getAll().forEach(trigger => {
        if (trigger.trigger === container) {
          trigger.kill();
        }
      });
    };
  }, [children, SplitText, preset, presetOverrides]);

  return (
    <div ref={containerRef} className={className} {...props}>
      {paragraphs.map((paragraph, index) => (
        <div key={index}>
          <p className="text-big">
            {paragraph}
          </p>
          {index < paragraphs.length - 1 && (
            <div style={{ height: `${paragraphSpacing}rem` }} />
          )}
        </div>
      ))}
    </div>
  );
}
