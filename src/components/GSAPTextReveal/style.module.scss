// Styles pour le composant GSAPTextReveal
.gsapTextReveal {
  // Le conteneur hérite des styles du tag parent

  // Wrapper pour les lignes - masque le débordement vertical
  :global(.gsap-line-wrapper) {
    overflow: hidden;
    display: block;
  }

  // Styles pour les lignes splitText générées par GSAP
  :global(.gsap-line) {
    display: block;
    white-space: nowrap; // Empêche les retours à la ligne non désirés
  }

  :global(.gsap-char) {
    display: inline-block;
    transform-origin: center bottom;

    // Force l'héritage complet des propriétés typographiques
    font-size: inherit !important;
    font-weight: inherit !important;
    font-family: inherit !important;
    letter-spacing: inherit !important;
    word-spacing: inherit !important;
    line-height: inherit !important;
    color: inherit !important;
    text-transform: inherit !important;
    font-style: inherit !important;
  }

  // Styles pour améliorer les performances d'animation
  :global(.gsap-line),
  :global(.gsap-char),
  :global(.gsap-line-wrapper) {
    will-change: transform;
    backface-visibility: hidden;
  }
}

// Classe utilitaire pour désactiver temporairement les animations
.gsapTextReveal.no-animation {
  :global(.gsap-char) {
    transform: none !important;
    opacity: 1 !important;
  }
}
