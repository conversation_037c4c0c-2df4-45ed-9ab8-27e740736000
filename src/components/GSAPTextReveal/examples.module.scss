.examples {
  padding: 4rem 0;
  min-height: 100vh;

  h1 {
    text-align: center;
    margin-bottom: 4rem;
    font-size: 3rem;
  }
}

.section {
  margin-bottom: 6rem;
  padding: 2rem 0;

  h2 {
    font-size: 1.5rem;
    margin-bottom: 2rem;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 0.1em;
  }
}

.heroTitle {
  font-size: clamp(2rem, 5vw, 4rem);
  font-weight: 700;
  line-height: 1.1;
  margin-bottom: 2rem;
}

.subtitle {
  font-size: clamp(1.2rem, 3vw, 2rem);
  line-height: 1.4;
  color: #555;
  margin-bottom: 2rem;
}

.energeticTitle {
  font-size: clamp(1.5rem, 4vw, 3rem);
  font-weight: 600;
  color: #ff6b35;
  margin-bottom: 2rem;
}

.elegantTitle {
  font-size: clamp(1.5rem, 4vw, 3rem);
  font-weight: 300;
  font-style: italic;
  color: #2c3e50;
  margin-bottom: 2rem;
}

.typewriter {
  font-family: 'Courier New', monospace;
  font-size: 1.2rem;
  background: #000;
  color: #00ff00;
  padding: 1rem;
  border-radius: 4px;
  margin-bottom: 2rem;
}

.glitch {
  font-size: clamp(1.5rem, 4vw, 3rem);
  font-weight: 900;
  color: #ff0080;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  margin-bottom: 2rem;
  text-shadow: 
    2px 0 #00ffff,
    -2px 0 #ff0080;
}

.paragraph {
  font-size: 1.1rem;
  line-height: 1.6;
  max-width: 600px;
  color: #333;
  margin-bottom: 2rem;
}

.custom {
  font-size: clamp(1.5rem, 4vw, 3rem);
  font-weight: 600;
  background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 2rem;
}
