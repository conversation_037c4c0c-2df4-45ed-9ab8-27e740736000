'use client';

import { useRef, useEffect, forwardRef, useState } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import styles from './style.module.scss';

// Enregistrer ScrollTrigger
if (typeof window !== 'undefined') {
  gsap.registerPlugin(ScrollTrigger);
}

/**
 * Composant d'animation de texte utilisant GSAP SplitText
 * 
 * @param {Object} props - Les propriétés du composant
 * @param {string} props.as - Le tag HTML à utiliser (par défaut 'div')
 * @param {React.ReactNode} props.children - Le contenu texte à animer
 * @param {string} props.className - Classes CSS additionnelles
 * @param {'chars'|'words'|'lines'} props.splitBy - Type de division du texte (par défaut 'chars')
 * @param {number} props.stagger - <PERSON><PERSON>lai entre chaque élément (par défaut 0.02)
 * @param {number} props.duration - Durée de l'animation (par défaut 0.6)
 * @param {number} props.delay - <PERSON><PERSON><PERSON> avant le début de l'animation (par défaut 0)
 * @param {string} props.ease - Type d'easing (par défaut 'power2.out')
 * @param {Object} props.from - État initial de l'animation
 * @param {Object} props.to - État final de l'animation
 * @param {boolean} props.triggerOnScroll - Déclencher l'animation au scroll (par défaut true)
 * @param {string} props.triggerStart - Position de déclenchement ScrollTrigger (par défaut 'top 80%')
 * @param {boolean} props.triggerOnce - Animation une seule fois (par défaut true)
 * @param {Function} props.onComplete - Callback à la fin de l'animation
 * @param {Function} props.onStart - Callback au début de l'animation
 */
const GSAPTextReveal = forwardRef(({
  as: Tag = 'div',
  children,
  className = '',
  splitBy = 'chars',
  stagger = 0.02,
  duration = 0.6,
  delay = 0,
  ease = 'power2.out',
  from = {
    y: '100%'
  },
  to = {
    y: '0%'
  },
  triggerOnScroll = true,
  triggerStart = 'top 80%',
  triggerOnce = true,
  onComplete,
  onStart,
  ...props
}, ref) => {
  const elementRef = useRef(null);
  const splitTextRef = useRef(null);
  const timelineRef = useRef(null);
  const [SplitText, setSplitText] = useState(null);

  // Utiliser la ref externe si fournie, sinon utiliser la ref interne
  const targetRef = ref || elementRef;

  // Charger SplitText dynamiquement
  useEffect(() => {
    if (typeof window !== 'undefined' && !SplitText) {
      import('gsap/SplitText').then((module) => {
        setSplitText(() => module.SplitText);
        gsap.registerPlugin(module.SplitText);
      });
    }
  }, [SplitText]);

  useEffect(() => {
    if (!targetRef.current || !SplitText) return;

    const element = targetRef.current;

    // Nettoyer les animations précédentes
    if (timelineRef.current) {
      timelineRef.current.kill();
    }
    if (splitTextRef.current) {
      splitTextRef.current.revert();
    }

    // Créer le SplitText selon le type demandé
    let splitType;
    if (splitBy === 'chars') {
      splitType = 'lines,chars';
    } else if (splitBy === 'lines') {
      splitType = 'lines';
    } else if (splitBy === 'words') {
      splitType = 'words';
    }

    splitTextRef.current = new SplitText(element, {
      type: splitType,
      linesClass: 'gsap-line',
      wordsClass: 'gsap-word',
      charsClass: 'gsap-char'
    });

    // Wrapper les lignes avec overflow hidden seulement si on anime par caractères
    if (splitBy === 'chars') {
      const lines = splitTextRef.current.lines;
      lines.forEach((line) => {
        const wrapper = document.createElement('div');
        wrapper.className = 'gsap-line-wrapper';
        wrapper.style.overflow = 'hidden';
        wrapper.style.display = 'block';

        line.parentNode.insertBefore(wrapper, line);
        wrapper.appendChild(line);
      });
    } else if (splitBy === 'lines') {
      // Pour les lignes, wrapper chaque ligne individuellement
      const lines = splitTextRef.current.lines;
      lines.forEach((line) => {
        const wrapper = document.createElement('div');
        wrapper.className = 'gsap-line-wrapper';
        wrapper.style.overflow = 'hidden';
        wrapper.style.display = 'block';

        line.parentNode.insertBefore(wrapper, line);
        wrapper.appendChild(line);
      });
    }

    // Obtenir les cibles selon le type d'animation demandé
    let targets;
    if (splitBy === 'chars') {
      targets = splitTextRef.current.chars;
    } else if (splitBy === 'lines') {
      targets = splitTextRef.current.lines;
    } else if (splitBy === 'words') {
      targets = splitTextRef.current.words;
    }

    if (!targets || targets.length === 0) return;

    // Créer la timeline - toujours en pause au début
    timelineRef.current = gsap.timeline({
      paused: true,
      onComplete,
      onStart
    });

    // Définir l'état initial
    gsap.set(targets, from);

    // Créer l'animation
    timelineRef.current.to(targets, {
      ...to,
      duration,
      ease,
      stagger,
      delay
    });

    // Configurer ScrollTrigger si nécessaire
    if (triggerOnScroll) {
      ScrollTrigger.create({
        trigger: element,
        start: triggerStart,
        once: triggerOnce,
        onEnter: () => timelineRef.current.play(),
        onLeave: () => {
          if (!triggerOnce) timelineRef.current.reverse();
        },
        onEnterBack: () => {
          if (!triggerOnce) timelineRef.current.play();
        }
      });
    } else {
      // Si pas de ScrollTrigger, jouer immédiatement
      timelineRef.current.play();
    }

    // Cleanup function
    return () => {
      if (timelineRef.current) {
        timelineRef.current.kill();
      }
      if (splitTextRef.current) {
        splitTextRef.current.revert();
      }
      ScrollTrigger.getAll().forEach(trigger => {
        if (trigger.trigger === element) {
          trigger.kill();
        }
      });
    };
  }, [children, splitBy, stagger, duration, delay, ease, triggerOnScroll, triggerStart, triggerOnce, SplitText]);

  // Fonction pour déclencher manuellement l'animation
  const play = () => {
    if (timelineRef.current) {
      timelineRef.current.play();
    }
  };

  const reverse = () => {
    if (timelineRef.current) {
      timelineRef.current.reverse();
    }
  };

  const restart = () => {
    if (timelineRef.current) {
      timelineRef.current.restart();
    }
  };

  // Exposer les méthodes via la ref
  useEffect(() => {
    if (ref && typeof ref === 'object') {
      ref.current = {
        ...targetRef.current,
        play,
        reverse,
        restart,
        timeline: timelineRef.current,
        splitText: splitTextRef.current
      };
    }
  });

  return (
    <Tag
      ref={targetRef}
      className={`${styles.gsapTextReveal} ${className}`}
      {...props}
    >
      {children}
    </Tag>
  );
});

GSAPTextReveal.displayName = 'GSAPTextReveal';

export default GSAPTextReveal;
