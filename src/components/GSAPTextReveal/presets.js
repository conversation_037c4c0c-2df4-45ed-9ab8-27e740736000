/**
 * Presets d'animation pour GSAPTextReveal
 * Facilite l'utilisation avec des configurations prédéfinies
 */

export const ANIMATION_PRESETS = {
  // Animation par défaut - révélation douce par caractères (style Awwwards)
  default: {
    splitBy: 'chars',
    stagger: 0.03,
    duration: 0.8,
    ease: 'power3.out',
    from: { y: '100%' },
    to: { y: '0%' }
  },

  // Animation rapide et énergique avec reveal
  energetic: {
    splitBy: 'chars',
    stagger: 0.02,
    duration: 0.6,
    ease: 'back.out(1.7)',
    from: { y: '120%' },
    to: { y: '0%' }
  },

  // Animation élégante avec rotation et reveal
  elegant: {
    splitBy: 'chars',
    stagger: 0.04,
    duration: 1,
    ease: 'power4.out',
    from: { y: '100%', rotationX: 45 },
    to: { y: '0%', rotationX: 0 }
  },

  // Animation par mots avec reveal
  words: {
    splitBy: 'words',
    stagger: 0.1,
    duration: 0.8,
    ease: 'power3.out',
    from: { y: '100%' },
    to: { y: '0%' }
  },

  // Animation par lignes avec reveal
  lines: {
    splitBy: 'lines',
    stagger: 0.15,
    duration: 1,
    ease: 'power3.out',
    from: { y: '100%' },
    to: { y: '0%' }
  },

  // Animation de machine à écrire avec reveal
  typewriter: {
    splitBy: 'chars',
    stagger: 0.08,
    duration: 0.3,
    ease: 'power2.out',
    from: { clipPath: 'inset(0 0 100% 0)' },
    to: { clipPath: 'inset(0 0 0% 0)' }
  },

  // Animation de glitch/cyberpunk avec reveal
  glitch: {
    splitBy: 'chars',
    stagger: 0.01,
    duration: 0.4,
    ease: 'power2.inOut',
    from: {
      clipPath: 'inset(0 0 100% 0)',
      y: 20,
      skewX: 15,
      scaleY: 1.2
    },
    to: {
      clipPath: 'inset(0 0 0% 0)',
      y: 0,
      skewX: 0,
      scaleY: 1
    }
  },

  // Animation douce pour les sous-titres avec reveal
  subtitle: {
    splitBy: 'words',
    stagger: 0.08,
    duration: 0.9,
    ease: 'power3.out',
    delay: 0.3,
    from: { y: '100%' },
    to: { y: '0%' }
  },

  // Animation pour les titres principaux - style Awwwards premium
  hero: {
    splitBy: 'chars',
    stagger: 0.025,
    duration: 1.2,
    ease: 'power4.out',
    from: { y: '100%' },
    to: { y: '0%' }
  },

  // Animation ultra smooth pour les sites haut de gamme
  luxury: {
    splitBy: 'chars',
    stagger: 0.035,
    duration: 1.4,
    ease: 'power4.out',
    from: { clipPath: 'inset(0 0 100% 0)', y: 40, rotationX: 30 },
    to: { clipPath: 'inset(0 0 0% 0)', y: 0, rotationX: 0 }
  },

  // Animation rapide et moderne
  modern: {
    splitBy: 'chars',
    stagger: 0.015,
    duration: 0.7,
    ease: 'power3.out',
    from: { clipPath: 'inset(0 0 100% 0)', y: 30 },
    to: { clipPath: 'inset(0 0 0% 0)', y: 0 }
  }
};

/**
 * Fonction utilitaire pour obtenir un preset avec des overrides
 * @param {string} presetName - Nom du preset
 * @param {Object} overrides - Propriétés à surcharger
 * @returns {Object} Configuration finale
 */
export const getPreset = (presetName, overrides = {}) => {
  const preset = ANIMATION_PRESETS[presetName];
  if (!preset) {
    console.warn(`Preset "${presetName}" not found. Using default preset.`);
    return { ...ANIMATION_PRESETS.default, ...overrides };
  }
  
  return {
    ...preset,
    ...overrides,
    from: { ...preset.from, ...overrides.from },
    to: { ...preset.to, ...overrides.to }
  };
};
