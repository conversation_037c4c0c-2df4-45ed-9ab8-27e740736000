import Masonry from "react-masonry-css";
import Image from "next/image";
import styles from "./style.module.scss";

export default function MyMasonry({ images, columns }) {
  const breakpointColumnsObj = {
    default: columns,
    1100: columns,
    700: 1,
  };

  return (
    <Masonry
      breakpointCols={breakpointColumnsObj}
      className={`${styles.myMasonryGrid} masonryGridGlobal`}
      columnClassName={`${styles.myMasonryGridColumn} masonryGridColumnGlobal`}
    >
      {images.map((image, i) => (
        <div key={i}>
          <Image
            src={image}
            alt=""
            width={800}
            height={600}
          />
        </div>
      ))}
    </Masonry>
  );
}
