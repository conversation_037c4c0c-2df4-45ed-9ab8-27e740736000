.myMasonryGrid {
  display: -webkit-box; /* fixe l'affichage sur safari/old browsers */
  display: -ms-flexbox;
  display: flex;
  gap: var(--gap-padding);
  width: auto;
  padding-top: var(--gap-padding);
  padding-bottom: var(--gap-padding);
  align-items: center;
}

.myMasonryGridColumn {
  background-clip: padding-box;
  gap: var(--gap-padding);
  display: flex;
  flex-direction: column;
}

.myMasonryGridColumn > div {
  /* margin-bottom: 1rem; */
}

/* Cette règle force l'image à occuper 100% de la largeur de son conteneur
   tout en conservant son ratio (hauteur auto) */
.myMasonryGridColumn > div :global(img) {
  width: 100%;
  height: auto;
}
