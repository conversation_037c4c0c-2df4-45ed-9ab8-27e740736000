import styles from '../AnimatedLink/style.module.scss';

export default function MDXLink({ href, children, className = '', ...props }) {
  // Vérifier si c'est un lien externe
  const isExternal = href?.startsWith('http');

  // Toujours utiliser un <a> normal pour éviter les problèmes d'hydratation
  return (
    <a
      href={href}
      className={`${styles.customLink} ${className}`}
      target={isExternal ? '_blank' : undefined}
      rel={isExternal ? 'noopener noreferrer' : undefined}
      {...props}
    >
      {children}
    </a>
  );
}
