"use client";
import React from "react";
import { motion } from "framer-motion";
import styles from "./style.module.scss";

export default function AnimatedText({
  text = "",
  className = "",
  amount = 0.6 // pourcentage visible pour déclencher
}) {
  const lines = text.split("\n");

  return (
    <div className={className}>
      {lines.map((line, index) => (
        <div key={index} className={styles.lineMask}>
          <motion.span
            initial={{ y: "100%" }}
            whileInView={{ y: "0%" }}
            // amount: portion de l’élément qui doit être dans la fenêtre
            viewport={{ once: true, amount }} 
            transition={{
              duration: 0.75,
              ease: [0.33, 1, 0.68, 1],
              delay: index * 0.1
            }}
          >
            {line}
          </motion.span>
        </div>
      ))}
    </div>
  );
}
