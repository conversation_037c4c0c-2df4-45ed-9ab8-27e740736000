'use client';
import React from 'react';
import { useRouter, usePathname } from 'next/navigation';
import styles from './style.module.scss';

export default function LanguageSwitcher({ locale = 'fr' }) {
  const router = useRouter();
  const pathname = usePathname();

  // Déterminer la langue opposée et son libellé
  const targetLocale = locale === 'fr' ? 'en' : 'fr';
  const currentLanguageLabel = locale === 'fr' ? 'Français' : 'English';

  const handleLanguageSwitch = () => {
    // Construire la nouvelle URL avec la langue opposée
    const newPath = pathname.replace(`/${locale}`, `/${targetLocale}`);
    router.push(newPath);
  };

  return (
    <div className={styles.languageSwitcher}>
      <button className={styles.languageButton} onClick={handleLanguageSwitch}>
        <span className={`material-symbols-outlined ${styles.planetIcon}`}>
          language
        </span>
        <span className={styles.languageText}>{currentLanguageLabel}</span>
      </button>
    </div>
  );
}
