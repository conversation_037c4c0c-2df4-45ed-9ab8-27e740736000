@import "../../styles/variables.scss";

.content {
  display: flex;
  flex-direction: column;
  gap: calc(var(--gap-padding) * 1.5);
}

.textSection {
  display: flex;
  flex-direction: column;
  gap: calc(var(--gap-padding) / 2);
}

.description {
  // Pas de max-width sur mobile, on laisse le texte prendre toute la largeur

  // Tablet et plus - on limite la largeur du texte
  @media (min-width: $breakpoint-sm) {
    max-width: 600px;
  }
}

.imagesSection {
  display: flex;
  flex-direction: column;
  gap: var(--gap-padding);
}

.imageWrapper {
  position: relative;
  width: 100%;
  overflow: hidden;
  max-width: 100%;
}

.image {
  width: 100%;
  height: auto;
  object-fit: cover;
  display: block;
  max-width: 100%;
}

// Tablet - 768px+
@media (min-width: $breakpoint-sm) {
  .imagesSection {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--gap-padding);
  }
}

// Desktop - 1200px+
@media (min-width: $breakpoint-lg) {
  .content {
    gap: calc(var(--gap-padding) * 2);
  }
}
