import React from 'react';
import Image from 'next/image';
import styles from './style.module.scss';
import { motion } from 'framer-motion';
import GSAPTextReveal from '@/components/GSAPTextReveal';
import { getPreset } from '@/components/GSAPTextReveal/presets';
import AnimatedLink from '@/components/AnimatedLink';

export default function TitleTextImages({
  title = 'Titre par défaut',
  description = 'Description par défaut du composant TitleTextImages.',
  descriptionHedhofisLinkText = 'Hedhofis',
  image1Src = '/images/lucas-joliveau-bibliotheque-macbook.png',
  image2Src = '/images/lucas-joliveau-google-meet.png',
  image1Alt = 'Image 1',
  image2Alt = 'Image 2'
}) {

  // Construire la description avec AnimatedLink pour Hedhofis
  const renderDescription = () => {
    if (description.includes('{{hedhofis_link}}')) {
      const parts = description.split('{{hedhofis_link}}');
      return (
        <>
          {parts[0]}
          <AnimatedLink href="https://www.hedhofis.com/" external>
            {descriptionHedhofisLinkText}
          </AnimatedLink>
          {parts[1]}
        </>
      );
    }
    return description;
  };

  return (
    <section className={`${styles.titleTextImages} section`}>
      <div className="container">
        <div className={styles.content}>
          <div className={styles.textSection}>
                  <GSAPTextReveal
                  as="h2"
                  className={styles.title}
                  {...getPreset('hero')}
                >
                  {title}
                </GSAPTextReveal>

            <GSAPTextReveal
              as="p"
              className={styles.description}
              {...getPreset('lines', { delay: 0.8, stagger: 0.2 })}
            >
              {renderDescription()}
            </GSAPTextReveal>
          </div>

          <div className={styles.imagesSection}>
            <motion.div
              className={styles.imageWrapper}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.6, ease: 'easeInOut', delay: 0.4 }}
            >
              <Image
                src={image1Src}
                alt={image1Alt}
                width={600}
                height={400}
                className={styles.image}
              />
            </motion.div>

            <motion.div
              className={styles.imageWrapper}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.6, ease: 'easeInOut', delay: 0.5 }}
            >
              <Image
                src={image2Src}
                alt={image2Alt}
                width={600}
                height={400}
                className={styles.image}
              />
            </motion.div>
          </div>
        </div>
      </div>
    </section>
  );
}
