// components/BlogCard.jsx
import Link from 'next/link';
import Image from 'next/image';
import styles from './style.module.scss';
import { useParams } from 'next/navigation';

export default function BlogCard({ post, big }) {
  const params = useParams();
  const locale = params?.locale || 'fr';
  const slug = post._raw.flattenedPath.split('/').pop();

  return (
    <article className={`${styles.card} ${big ? styles.big : styles.small}`}>
      <Link href={`/${locale}/blog/${slug}`}>
        <Image
          src={post.cover ?? '/images/placeholder.jpg'}
          alt={post.title}
          fill
          sizes={big ? '(max-width: 1024px) 100vw, 66vw' : '(max-width: 1024px) 100vw, 33vw'}
          className={styles.img}
        />
        <div className={styles.meta}>
          <h3>{post.title}</h3>
          {post.category && <p className={styles.cat}>{post.category}</p>}
        </div>
      </Link>
    </article>
  );
}
