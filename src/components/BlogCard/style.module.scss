.card {
  position: relative;
  border-radius: .5rem;
  overflow: hidden;
  a { text-decoration: none; color: inherit; }
}

.img {
  object-fit: cover;
}

.meta {
  position: absolute;
  bottom: 0;
  width: 100%;
  padding: .75rem 1rem;
  background: linear-gradient(0deg, #0008, transparent);
  h3   { margin: 0; font-size: 1.1rem; color: #fff; }
  .cat { margin: .25rem 0 0; font-size: .85rem; color: #ffcd2d; }
}

.big   { aspect-ratio: 3 / 2;  } /* 2/3 largeur → ratio 3:2 */
.small { aspect-ratio: 3 / 4;  } /* 1/3 largeur → ratio 3:4 */
