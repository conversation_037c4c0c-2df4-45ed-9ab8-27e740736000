"use client";

import Description from '@/components/Description';
import Separator from '@/components/Separator';
import { useTranslation } from "@/hooks/useTranslation";

export default function Values({
  namespace = 'agency',
  value1Title = 'values1.title',
  value1Text = 'values1.text',
  value2Title = 'values2.title',
  value2Text = 'values2.text',
  value3Title = 'values3.title',
  value3Text = 'values3.text'
}) {
  const { t } = useTranslation(namespace);

  return (
    <>
      <div className="container">
        <Separator
          animated={true}
          color="#000"
          thickness={1}
          animationDelay={0.1}
        />
      </div>

      <Description
        descriptionTitle={t(value1Title)}
        descriptionText={t(value1Text)}
        showButton={false}
        titleTag="h3"
      />

      <div className="container">
        <Separator
          animated={true}
          color="#000"
          thickness={1}
          animationDelay={0.2}
        />
      </div>

      <Description
        descriptionTitle={t(value2Title)}
        descriptionText={t(value2Text)}
        showButton={false}
        titleTag="h3"
      />

      <div className="container">
        <Separator
          animated={true}
          color="#000"
          thickness={1}
          animationDelay={0.3}
        />
      </div>

      <Description
        descriptionTitle={t(value3Title)}
        descriptionText={t(value3Text)}
        showButton={false}
        titleTag="h3"
      />
    </>
  );
}