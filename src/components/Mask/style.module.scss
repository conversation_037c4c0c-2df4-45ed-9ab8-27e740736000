// components/mask.module.scss
.mask {
    position: absolute;
    z-index: 2;
    top: -89px;
    left: 0;
    width: 100%;
    height: 90px;
    clip-path: polygon(0% 100%, 4% 0%, 99% 0%, 100% 20%, 100% 100%);
    
    @media (min-width: 768px) {
      top: -119px;
      height: 120px;
      clip-path: polygon(0% 100%, 5% 0%, 98% 0%, 100% 25%, 100% 100%);
    }
    
    @media (min-width: 1024px) {
      top: -139px;
      height: 140px;
      clip-path: polygon(0% 100%, 4% 0%, 99% 0%, 100% 20%, 100% 100%);
    }
  }
  
  .overlay {
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    clip-path: none !important;
  }
  
  /* Style pour le mode wrap (découpe sur l'enfant) */
// components/mask.module.scss
.maskWrap {
    display: block;       // au lieu d'inline-block, pour qu'il prenne toute la largeur disponible
    width: 100%;
     height: 100%; 
    position: relative;
    overflow: hidden;
    /* État par défaut (non-hover) */
    clip-path: polygon(0% 0%, 100% 0%, 100% 92%, 100% 100%, 0% 100%, 0% 23%);
    transition: clip-path 0.6s ease;
    
    @media (min-width: 768px) {
      /* Ajustement si nécessaire pour tablette */
    }
    
    @media (min-width: 1024px) {
      /* Ajustement si nécessaire pour desktop */
    }
  }

  
  /* Animation sur hover pour le mode wrap */
  .hoverAnim {
    transition: clip-path 0.6s ease;
    
    &:hover {
      /* Nouvelle forme au survol (ici pour mobile) */
      clip-path: polygon(18% 0%, 100% 0%, 100% 92%, 92% 100%, 0% 100%, 0% 23%);
    }
  }
  
  .invert {
    transform: scale(-1, -1);
    transform-origin: top left;
  }
  

  /* Sur les écrans tactiles (pas de hover), on applique
   directement la forme « hover » */
@media (hover: none) {
  .maskWrap.hoverAnim {
    /* même clip-path que dans :hover */
    clip-path: polygon(18% 0%, 100% 0%, 100% 92%, 92% 100%, 0% 100%, 0% 23%);
  }
}

.noMobileMask {
  @media (hover: none), (pointer: coarse) {
    clip-path: none !important;
  }
}