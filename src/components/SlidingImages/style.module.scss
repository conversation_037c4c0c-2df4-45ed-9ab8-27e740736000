.slidingImages{
    display: flex;
    flex-direction: column;
    gap: 3vw;
    position: relative;
    padding-top: var(--section-padding);
    padding-bottom: var(--section-padding);
    background-color: #FCFCF8;
    overflow: hidden;
    z-index: 1;
    .slider{
        display: flex;
        position: relative;
        gap: 3vw;
        width: 120vw;
        left: -10vw;
        .project{
            width: 25%;
            height: 20vw;
            display: flex;
            align-items: center;
            justify-content: center;
            .imageContainer{
                position: relative;
                width: 80%;
                height: 80%;
                img{
                    object-fit: cover;
                }
            }
        }
    }
    .circleContainer{
        background-color: red;
        position: relative;
        margin-top: 100px;
        .circle{
            height: 1550%;
            width: 120%;
            left: -10%;
            border-radius: 0 0 50% 50%;
            background-color: white;
            z-index: 1;
            position: absolute;
            box-shadow: 0px 60px 50px rgba(0, 0, 0, 0.748);
        }
    }
}

@media (max-width: 1024px) { // Ajuste la valeur selon tes besoins
    .slidingImages {
        display: none;
    }
}
