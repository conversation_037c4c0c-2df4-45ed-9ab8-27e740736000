.root {
  font-size: 0.875rem;
  ol {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    padding: 0;

    li {
      display: inline-flex;
      align-items: center;

      a {
        color: var(--link, #999);
        text-decoration: none;
        &:hover { text-decoration: underline; }
      }

      &::after {
        content: "›";
        margin: 0 0.5rem;
        color: #999;
      }

      &:last-child::after { content: none; }
    }
  }
}
