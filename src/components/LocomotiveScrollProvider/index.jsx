// components/LenisScrollProvider.js
'use client';
import { useEffect, useRef, useState, createContext } from 'react';

export const LenisScrollContext = createContext(null);

export default function LenisScrollProvider({ children, options }) {
  const containerRef = useRef(null);
  const [lenisScroll, setLenisScroll] = useState(null);

  useEffect(() => {
    let scrollInstance;
    import('lenis').then((module) => {
      const Lenis = module.default;

      // Configuration Lenis pour reproduire le comportement de Locomotive Scroll
      scrollInstance = new Lenis({
        duration: 1.2, // Durée du smooth scroll
        easing: (t) => Math.min(1, 1.001 - Math.pow(2, -10 * t)), // Easing similaire à Locomotive
        direction: 'vertical',
        gestureDirection: 'vertical',
        smooth: true,
        smoothTouch: options?.smartphone?.smooth || false,
        touchMultiplier: 2,
        infinite: false,
        ...options,
      });

      setLenisScroll(scrollInstance);

      // Fonction d'animation pour Lenis
      function raf(time) {
        scrollInstance.raf(time);
        requestAnimationFrame(raf);
      }
      requestAnimationFrame(raf);

      setTimeout(() => {
        document.body.style.cursor = 'default';
      }, 100);
    });

    return () => {
      if (scrollInstance) {
        scrollInstance.destroy();
      }
    };
  }, [options]);

  return (
    <LenisScrollContext.Provider value={lenisScroll}>
      <div data-scroll-container ref={containerRef}>
        {children}
      </div>
    </LenisScrollContext.Provider>
  );
}
