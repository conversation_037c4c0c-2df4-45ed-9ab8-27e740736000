'use client';
import { useEffect, useRef } from 'react';

/**
 * Hook pour créer des effets de parallax avec Lenis
 * Compatible avec les attributs data-scroll-speed de Locomotive Scroll
 */
export function useLenisParallax(speed = 0.1) {
  const elementRef = useRef(null);

  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    let animationId;
    
    function updateParallax() {
      if (!element) return;
      
      const rect = element.getBoundingClientRect();
      const windowHeight = window.innerHeight;
      
      // Calculer la position relative de l'élément dans la viewport
      const elementTop = rect.top;
      const elementHeight = rect.height;
      
      // Calculer le pourcentage de scroll (de -1 à 1)
      const scrollProgress = (windowHeight - elementTop) / (windowHeight + elementHeight);
      
      // Appliquer l'effet de parallax
      const translateY = (scrollProgress - 0.5) * speed * 100;
      
      element.style.transform = `translateY(${translateY}px)`;
      
      animationId = requestAnimationFrame(updateParallax);
    }

    // Démarrer l'animation
    updateParallax();
    
    // Écouter les événements de scroll
    window.addEventListener('scroll', updateParallax, { passive: true });
    window.addEventListener('resize', updateParallax, { passive: true });

    return () => {
      window.removeEventListener('scroll', updateParallax);
      window.removeEventListener('resize', updateParallax);
      if (animationId) {
        cancelAnimationFrame(animationId);
      }
    };
  }, [speed]);

  return elementRef;
}
