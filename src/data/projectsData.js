// Structure professionnelle avec taxonomies centralisées
import {
  EXPERTISES,
  CATEGORIES,
  INDUSTRIES,
  TECHNOLOGIES,
  getExpertiseLabel,
  getCategoryLabel,
  getIndustryLabel,
  getTechnologyLabel
} from './taxonomies.js';
import AnimatedLink from '@/components/AnimatedLink';

const projectsBase = [
  {
    // Données communes (une seule fois)
    id: "cap-financimmo",
    client: "CAP Financimmo",
    year: 2023,
    src: "projects/cap-financimmo/main-cap-financimmo.webp",
    color: "#D6D6D4",
    clientSite: "https://www.capfinancimmo.com",
    visible: true,
    showDescriptionButton: false,
    showButton: true,

    // Taxonomies centralisées
    expertiseId: EXPERTISES.BRANDING.id,
    categoryIds: [CATEGORIES.LOGO_DESIGN.id, CATEGORIES.SOCIAL_MEDIA.id, CATEGORIES.PRINT_MATERIALS.id],
    industryId: INDUSTRIES.FINANCE.id,
    technologyIds: [TECHNOLOGIES.FIGMA.id, TECHNOLOGIES.HTML_CSS_JS.id, TECHNOLOGIES.ODOO.id, TECHNOLOGIES.PHOTOSHOP.id],

    // Traductions dans le même objet
    fr: {
      slug: "logo-cap-financimmo",
      metaDescriptionText: "Découvrez le logo repensé de CAP Financimmo : un design en éventail aux lignes de branches symbolisant l'équilibre entre expertise et variété.",
      title: "Donner vie à l'immobilier, un financement à la fois.",
      location: "Bordeaux, France",
      buttonLabel: "Voir le site web",
      descriptionTitle: "Un logo à la croisée des chemins du financement",
      descriptionText: "Conçu comme un point d'ancrage, le logo de Cap Financimmo incarne l'équilibre entre expertise, diversité et structure. Ses lignes évoquent des branches qui s'étendent, symbolisant les multiples solutions de l'entreprise. Une refonte pensée pour inspirer confiance, en ligne comme sur le terrain.",
      buttonText: "test",
      longDescription: {
        title: "Un design qui parle à tous les projets.",
        leftText: "Dès les premières étapes de l'analyse, nous avons identifié que l'ancien logotype souffrait de complexité : des détails superflus comme les plumes et les deux paons au centre altéraient sa lisibilité, particulièrement en petite taille. En réponse, nous avons simplifié la structure graphique en recentrant l'attention sur le fondamental : des branches évasées partant d'un tronc commun.",
        rightText: "Les choix de couleurs ont également été revisités. En renforçant la palette avec des couleurs plus sélectives, nous avons intensifié l'impact visuel tout en confirmant une meilleure accessibilité numérique. Le jaune incarne la vision qu'apporte l'entreprise à ses clients, contrastant avec le bleu, plus profond."
      }
    },
    en: {
      slug: "cap-financimmo-logo",
      metaDescriptionText: "Discover the redesigned CAP Financimmo logo: a fan design with branch lines symbolizing the balance between expertise and variety.",
      title: "Bringing real estate to life, one financing at a time.",
      location: "Montreal, Canada",
      buttonLabel: "",
      descriptionTitle: "A logo at the crossroads of financing",
      descriptionText: "Designed as an anchor point, the Cap Financimmo logo embodies the balance between expertise, diversity and structure. Its lines evoke branches that extend, symbolizing the company's multiple solutions. A redesign designed to inspire confidence, online and in the field.",
      buttonText: "test",
      longDescription: {
        title: "A design that speaks to all projects.",
        leftText: "From the first stages of analysis, we identified that the old logotype suffered from complexity: superfluous details like feathers and the two peacocks in the center altered its readability, particularly in small sizes. In response, we simplified the graphic structure by refocusing attention on the fundamental: flared branches starting from a common trunk.",
        rightText: "Color choices were also revisited. By strengthening the palette with more selective colors, we intensified the visual impact while confirming better digital accessibility. Yellow embodies the vision that the company brings to its clients, contrasting with the deeper blue."
      }
    },

    // Images communes (une seule fois)
    projectImages: {
      masonryBlocks: [
        {
          columns: 2,
          inContainer: true,
          images: [
            "/images/projects/cap-financimmo/logo-cap-financimmo.gif",
            "/images/projects/cap-financimmo/analyse-logo-cap-financimmo.webp"
          ]
        },
        {
          columns: 2,
          inContainer: true,
          images: [
            "/images/projects/cap-financimmo/instagram-cap-financimmo.webp",
            "/images/projects/cap-financimmo/stylo-cap-financimmo.webp"
          ]
        },
        {
          columns: 1,
          inContainer: false,
          images: [
            "/images/projects/cap-financimmo/analyse-logo-cap-financimmo.webp"
          ]
        },
        {
          columns: 2,
          inContainer: true,
          images: [
            "/images/projects/cap-financimmo/tshirt-cap-financimmo.webp",
            "/images/projects/cap-financimmo/tshirt-zoom-cap-financimmo.webp"
          ]
        },
        {
          columns: 1,
          inContainer: true,
          images: [
            "/images/projects/cap-financimmo/affiche-cap-financimmo.webp"
          ]
        },
        {
          columns: 2,
          inContainer: true,
          images: [
            "/images/projects/cap-financimmo/cartes-affaires-cap-financimmo.webp",
            "/images/projects/cap-financimmo/support-plastique-cap-financimmo.webp",
            "/images/projects/cap-financimmo/cahier-notes-cap-financimmo.webp",
            "/images/projects/cap-financimmo/stylo-cap-financimmo.webp"
          ]
        }
      ]
    }
  },

  {
    // Données communes
    id: "numigi-website",
    client: "Numigi",
    year: 2023,
    src: "projects/numigi/website/main-numigi.webp",
    color: "#F9BB5A",
    clientSite: "https://www.numigi.com",
    visible: true,
    showDescriptionButton: false,
    showButton: true,

    // Taxonomies centralisées
    expertiseId: EXPERTISES.WEB_DEVELOPMENT.id,
    categoryIds: [CATEGORIES.WEB_DEVELOPMENT.id, CATEGORIES.ODOO_DEVELOPMENT.id, CATEGORIES.PROTOTYPING.id],
    industryId: INDUSTRIES.INFORMATION_TECHNOLOGY.id,
    technologyIds: [TECHNOLOGIES.FIGMA.id, TECHNOLOGIES.HTML_CSS_JS.id, TECHNOLOGIES.ODOO.id],

    // Traductions
    fr: {
      slug: "site-web-numigi",
      metaDescriptionText: "Refonte du site de Numigi basée sur Odoo. L'objectif : accompagner les entreprises du Québec vers une gestion d'entreprise intégrée.",
      title: "Se démarquer dans l'intégration ERP au Québec.",
      location: "Longueuil, Canada",
      buttonLabel: "Voir le site web",
      descriptionTitle: "Cinq ans et un virage numérique",
      descriptionText: "À l'aube du cinquième anniversaire de la marque, Numigi souhaitait professionnaliser son site web pour mieux refléter son approche Open Source, basée sur les systèmes de gestion Konvergo et Odoo. Cette refonte s'attarde d'abord à simplifier la navigation, tout en incorporant la nouvelle identité visuelle.",
      buttonText: "test",
      longDescription: {
        title: "Une expérience centrée sur l'utilisateur",
        leftText: "La navigation a été repensée afin de proposer un parcours limpide et de mettre de l'avant ce que Numigi offre : des solutions adaptées aux secteurs des services, de la distribution, de la construction et de l'industrie manufacturière. Pour mieux répondre aux besoins de chaque entreprise, le site présente clairement ses forfaits clé-en-main, ses ateliers personnalisés et son catalogue de formations.",
        rightText: "Grâce à la flexibilité d'Odoo, le contenu se met à jour aussi bien du côté back-end que front-end, permettant une synchronisation totale. Le nouveau site, compatible avec différents appareils, favorise une expérience sans obstacle pour les utilisateurs."
      }
    },
    en: {
      slug: "numigi-website",
      metaDescriptionText: "Redesign of the Numigi site based on Odoo. The objective: to support Quebec businesses towards integrated business management.",
      title: "Standing out in ERP integration in Quebec.",
      location: "Longueuil, Canada",
      buttonLabel: "Voir le site web",
      descriptionTitle: "Five years and a digital shift",
      descriptionText: "On the cusp of the brand's fifth anniversary, Numigi wanted to professionalize its website to better reflect its Open Source approach, based on Konvergo and Odoo management systems. This redesign first focuses on simplifying navigation, while incorporating the new visual identity.",
      buttonText: "test",
      longDescription: {
        title: "A user-centered experience",
        leftText: "The navigation has been rethought to offer a clear path and to highlight what Numigi offers: solutions adapted to the service, distribution, construction, and manufacturing sectors. To better meet the needs of each company, the site clearly presents its turnkey packages, its personalized workshops, and its training catalog.",
        rightText: "Thanks to Odoo's flexibility, the content is updated on both the back-end and the front-end, allowing for total synchronization. The new site, compatible with different devices, promotes a seamless experience for users."
      }
    },

    // Images communes
    projectImages: {
      masonryBlocks: [
        {
          columns: 1,
          inContainer: true,
          images: ["/images/projects/numigi/website/arborescence-site-web-numigi.webp"]
        },
        {
          columns: 2,
          inContainer: true,
          images: [
            "/images/projects/numigi/website/wireframes-site-web-numigi.webp",
            "/images/projects/numigi/website/accueil-numigi-site-web-iphone.webp"
          ]
        },
        {
          columns: 2,
          inContainer: true,
          images: [
            "/images/projects/numigi/website/formations-site-web-numigi.webp",
            "/images/projects/numigi/website/apropos-site-web-numigi.webp"
          ]
        },
        {
          columns: 1,
          inContainer: false,
          images: ["/images/projects/numigi/website/mosaique-site-web-numigi.webp"]
        }
      ]
    }
  },

  {
    // OCA Days 2023
    id: "oca-days-2023",
    client: "OCA Days 2023",
    year: 2023,
    src: "projects/oca-days-2023/couverture-oca-days-2023.webp",
    color: "#C3CCB0",
    clientSite: "https://odoo-community.org",
    visible: true,
    showDescriptionButton: false,
    showButton: false,

    // Taxonomies centralisées
    expertiseId: EXPERTISES.BRANDING.id,
    categoryIds: [CATEGORIES.LOGO_DESIGN.id, CATEGORIES.SOCIAL_MEDIA.id, CATEGORIES.PRINT_MATERIALS.id, CATEGORIES.PRODUCT_DESIGN.id],
    industryId: INDUSTRIES.INFORMATION_TECHNOLOGY.id,
    technologyIds: [], // Pas de technologies spécifiques

    fr: {
      slug: "identite-oca-days-2023",
      metaDescriptionText: "OCA Days 2023 : un logo radial qui rassemble la communauté open-source et épargne du budget annuel. Une refonte par Kapreon.",
      title: "Rassembler la communauté Open Source.",
      location: "Liège, Belgique",
      buttonLabel: "",
      descriptionTitle: "Le nouveau logo des OCA Days 2023",
      descriptionText: "Pendant des années, l'OCA créait un logo différent pour chaque édition des OCA Days. Résultat : l'image de la marque se diluait d'une année à l'autre, et il fallait mobiliser des ressources de design à chaque nouvelle mouture.",
      buttonText: "test",
      longDescription: {
        title: "Un design qui parle à tous les projets.",
        leftText: "Kapreon a imaginé un symbole épuré : un disque évidé entouré d'arcs de cercle de tailles variées. Chaque arc représente un membre de la communauté ; ensemble, ils composent un cercle complet qui évoque la collaboration autour d'un même objectif. Le dessin radial s'anime facilement sans perdre en lisibilité, et une simple touche de couleur — vert pour 2023 — différencie chaque édition sans toucher à la forme. Notre agence à fourni toutes les déclinaisons du logo pour l'évènement : stickers, panneaux imprimés, bannières, badges, et plus encore.",
        rightText: "Grâce à la proposition de Kapreon, l'association dispose désormais d'un socle unique pour ses évènements annuels : un logo décliné uniquement par sa couleur de l'évènement. Plus besoin de relancer un chantier graphique tous les douze mois ; l'OCA profite d'une identité stable, d'actifs prêts à l'emploi et d'économies directes sur la ligne design."
      }
    },
    en: {
      slug: "oca-days-2023-identity",
      metaDescriptionText: "OCA Days 2023: a radial logo that brings together the open-source community and saves annual budget. A redesign by Kapreon.",
      title: "Bringing together the Open Source community.",
      location: "Liège, Belgium",
      buttonLabel: "",
      descriptionTitle: "The new OCA Days 2023 logo",
      descriptionText: "For years, the OCA created a different logo for each edition of OCA Days. Result: the brand image was diluted from year to year, and design resources had to be mobilized for each new version.",
      buttonText: "test",
      longDescription: {
        title: "A design that speaks to all projects.",
        leftText: "Kapreon imagined a refined symbol: a hollow disc surrounded by arcs of various sizes. Each arc represents a community member; together, they compose a complete circle that evokes collaboration around a common goal. The radial design animates easily without losing readability, and a simple touch of color — green for 2023 — differentiates each edition without touching the shape. Our agency provided all logo variations for the event: stickers, printed panels, banners, badges, and more.",
        rightText: "Thanks to Kapreon's proposal, the association now has a unique foundation for its annual events: a logo declined only by its event color. No more need to relaunch a graphic project every twelve months; the OCA benefits from a stable identity, ready-to-use assets and direct savings on the design line."
      }
    },

    projectImages: {
      masonryBlocks: [
        {
          columns: 2,
          inContainer: true,
          images: [
            "/images/projects/oca-days-2023/logo-oca-days-2023.webp",
            "/images/projects/oca-days-2023/stickers-macbook.webp"
          ]
        },
        {
          columns: 1,
          inContainer: false,
          images: ["/images/projects/oca-days-2023/stickers-mur-oca-days-2023.webp"]
        },
        {
          columns: 2,
          inContainer: true,
          images: [
            "/images/projects/oca-days-2023/camera-television-rtc-liege.webp",
            "/images/projects/oca-days-2023/simone-orsi-board-oca-days.webp",
            "/images/projects/oca-days-2023/participants-conference-oca-days.webp"
          ]
        },
        {
          columns: 1,
          inContainer: false,
          images: ["/images/projects/oca-days-2023/presentation-sponsors-oca-days-2023.webp"]
        },
        {
          columns: 2,
          inContainer: true,
          images: [
            "/images/projects/oca-days-2023/presentation-logo-oca-days-2023.webp",
            "/images/projects/oca-days-2023/stickers-oca-days-2023.webp",
            "/images/projects/oca-days-2023/accessoires-oca-days-2023.webp"
          ]
        }
      ]
    }
  },

  {
    // Konvergo Tour 2024
    id: "konvergo-tour-2024",
    client: "Konvergo Tour 2024",
    year: 2024,
    src: "projects/konvergo/konvergo-tour-2024/main-konvergo-tour-nantes.webp",
    color: "#242930",
    clientSite: "https://konvergo.com",
    visible: true,
    showDescriptionButton: false,
    showButton: false,

    // Taxonomies centralisées
    expertiseId: EXPERTISES.BRANDING.id,
    categoryIds: [CATEGORIES.SOCIAL_MEDIA.id, CATEGORIES.PRINT_MATERIALS.id, CATEGORIES.PRODUCT_DESIGN.id],
    industryId: INDUSTRIES.INFORMATION_TECHNOLOGY.id,
    technologyIds: [], // Pas de technologies spécifiques

    fr: {
      slug: "identite-konvergo-tour-2024",
      metaDescriptionText: "Konvergo Tour 2024 de Montréal à Nantes – Open-source, supports imprimés, accessoires et réseaux sociaux.",
      title: "Enraciner l'ERP Open Source de Montréal à Nantes.",
      location: "Montréal, Canada",
      buttonLabel: "",
      descriptionTitle: "Konvergo Tour 2024 : La marque débarque en France",
      descriptionText: "Début 2024, l'équipe Konvergo a traversé Paris, Nantes puis Lyon pour présenter son ERP et son chatbot conversationnel aux intégrateurs, PME et cabinets CPA de l'Hexagone. Trois dates, trois formats, un objectif : amorcer un réseau local de partenaires.",
      buttonText: "test",
      longDescription: {
        title: "Pensé pour durer au-delà de l'évènement",
        leftText: "Vêtements avec imprimés, bannières roll-up promotionnelles, stickers découpés sur-mesure, et landing page évènementielle. Kapreon a géré la direction artistique de bout en bout.",
        rightText: "À côté des classiques accessoires, l'agence a glissé un clin d'œil durable : des crayons plantables qui, une fois terminés, font pousser des tomates — clin d'œil au mot d'ordre « Grandir avec vous » entre les partenaires et Konvergo."
      }
    },
    en: {
      slug: "konvergo-tour-2024-identity",
      metaDescriptionText: "Konvergo Tour 2024 from Montreal to Nantes – Open-source, print materials, accessories and social media.",
      title: "Rooting Open Source ERP from Montreal to Nantes.",
      location: "Montreal, Canada",
      buttonLabel: "",
      descriptionTitle: "Konvergo Tour 2024: The brand lands in France",
      descriptionText: "Early 2024, the Konvergo team crossed Paris, Nantes then Lyon to present its ERP and conversational chatbot to integrators, SMEs and CPA firms in France. Three dates, three formats, one objective: initiate a local network of partners.",
      buttonText: "test",
      longDescription: {
        title: "Designed to last beyond the event",
        leftText: "Printed clothing, promotional roll-up banners, custom-cut stickers, and event landing page. Kapreon managed the artistic direction from end to end.",
        rightText: "Alongside the classic accessories, the agency slipped in a sustainable wink: plantable pencils that, once finished, grow tomatoes — a nod to the motto 'Growing with you' between partners and Konvergo."
      }
    },

    projectImages: {
      masonryBlocks: [
        {
          columns: 2,
          inContainer: true,
          images: [
            "/images/projects/konvergo/konvergo-tour-2024/t-shirt-konvergo-tour.webp",
            "/images/projects/konvergo/konvergo-tour-2024/t-shirt-blanc-konvergo-tour.webp",
            "/images/projects/konvergo/konvergo-tour-2024/t-shirt-proche-konvergo-tour.webp",
            "/images/projects/konvergo/konvergo-tour-2024/pull-konvergo-tour.webp"
          ]
        },
        {
          columns: 1,
          inContainer: false,
          images: ["/images/projects/konvergo/konvergo-tour-2024/pull-dos-konvergo-tour.webp"]
        },
        {
          columns: 2,
          inContainer: true,
          images: [
            "/images/projects/konvergo/konvergo-tour-2024/affiche-urbaine-konvergo-tour.webp",
            "/images/projects/konvergo/konvergo-tour-2024/mockup-stickers-konvergo-tour-macbook.webp"
          ]
        },
        {
          columns: 1,
          inContainer: true,
          images: ["/images/projects/konvergo/konvergo-tour-2024/ville-konvergo-tour-reseaux-sociaux.webp"]
        },
        {
          columns: 2,
          inContainer: true,
          images: [
            "/images/projects/konvergo/konvergo-tour-2024/kakemono-konvergo-tour.webp",
            "/images/projects/konvergo/konvergo-tour-2024/stickers-konvergo-singe.webp"
          ]
        }
      ]
    }
  },

  {
    // Ombres & Nuances
    id: "ombres-nuances",
    client: "Ombres & Nuances",
    year: 2024,
    src: "projects/ombres-nuances/main-ombres-nuances.webp",
    color: "#EFDECD",
    clientSite: "",
    visible: true,
    showDescriptionButton: false,
    showButton: false,

    // Taxonomies centralisées
    expertiseId: EXPERTISES.BRANDING.id,
    categoryIds: [CATEGORIES.LOGO_DESIGN.id, CATEGORIES.SOCIAL_MEDIA.id],
    industryId: INDUSTRIES.BEAUTY_WELLNESS.id,
    technologyIds: [], // Pas de technologies spécifiques

    fr: {
      slug: "identite-ombres-nuances",
      metaDescriptionText: "Une identité qui reflète la rencontre entre force et finesse. Typographie contrastée et palette organique.",
      title: "Un langage visuel entre fluidité et caractère.",
      location: "Nantes, France",
      buttonLabel: "",
      descriptionTitle: "Un équilibre subtil entre ombres et lumière",
      descriptionText: "Conçu comme une ode à la personnalisation, le logo d'Ombres & Nuances incarne l'harmonie entre audace et délicatesse. La typographie joue un rôle central : le contraste entre le mot « Ombres », fort et structuré, et « Nuances », fin et élégant, illustre la diversité des styles capillaires que le salon propose. Au cœur de cette composition, le « & » symbolise l'union des opposés.",
      buttonText: "test",
      longDescription: {
        title: "Des teintes naturelles inspirées de la chevelure.",
        leftText: "Pour refléter la variété des nuances capillaires, nous avons opté pour une palette chromatique riche et naturelle : un charbon doux évoquant l'élégance, un blanc cassé pour la simplicité, et un fauve chaleureux rappelant la lumière d'un coucher de soleil sur les cheveux.",
        rightText: "Les éléments visuels de la marque intègre des courbes et des lignes organiques, rappelant la fluidité et le mouvement des cheveux. Les formes douces et naturelles suggèrent à la fois la souplesse et la liberté, symbolisant le cheveux dans sa forme la plus vivante."
      }
    },
    en: {
      slug: "ombres-nuances-identity",
      metaDescriptionText: "An identity that reflects the meeting between strength and finesse. Contrasted typography and organic palette.",
      title: "A visual language between fluidity and character.",
      location: "Nantes, France",
      buttonLabel: "",
      descriptionTitle: "A subtle balance between shadows and light",
      descriptionText: "Designed as an ode to personalization, the Ombres & Nuances logo embodies the harmony between boldness and delicacy. Typography plays a central role: the contrast between the word 'Ombres', strong and structured, and 'Nuances', fine and elegant, illustrates the diversity of hair styles that the salon offers. At the heart of this composition, the '&' symbolizes the union of opposites.",
      buttonText: "test",
      longDescription: {
        title: "Natural tints inspired by hair.",
        leftText: "To reflect the variety of hair nuances, we opted for a rich and natural chromatic palette: a soft charcoal evoking elegance, an off-white for simplicity, and a warm tawny reminiscent of sunset light on hair.",
        rightText: "The brand's visual elements integrate curves and organic lines, recalling the fluidity and movement of hair. The soft and natural forms suggest both suppleness and freedom, symbolizing hair in its most living form."
      }
    },

    projectImages: {
      masonryBlocks: [
        {
          columns: 2,
          inContainer: true,
          images: [
            "/images/projects/ombres-nuances/logo-ombres-nuances.webp",
            "/images/projects/ombres-nuances/mosaique-coiffure-ombres-nuances.webp"
          ]
        },
        {
          columns: 1,
          inContainer: true,
          images: ["/images/projects/ombres-nuances/linkedin-ombres-nuances.webp"]
        },
        {
          columns: 2,
          inContainer: true,
          images: [
            "/images/projects/ombres-nuances/compte-instagram-ombres-nuances.webp",
            "/images/projects/ombres-nuances/enseigne-ombres-nuances.webp"
          ]
        },
        {
          columns: 1,
          inContainer: false,
          images: ["/images/projects/ombres-nuances/posts-instagram-ombres-nuances.webp"]
        }
      ]
    }
  },

  {
    // Konvergo Website (FR seulement)
    id: "konvergo-website",
    client: "Konvergo",
    year: 2023,
    src: "projects/konvergo/site-web/mockup-site-web-konvergo.webp",
    color: "#FDF8E8",
    clientSite: "https://konvergo.com",
    visible: true,
    showDescriptionButton: false,
    showButton: false,

    // Taxonomies centralisées
    expertiseId: EXPERTISES.WEB_DEVELOPMENT.id,
    categoryIds: [CATEGORIES.WEB_DEVELOPMENT.id, CATEGORIES.ODOO_DEVELOPMENT.id, CATEGORIES.PROTOTYPING.id],
    industryId: INDUSTRIES.INFORMATION_TECHNOLOGY.id,
    technologyIds: [], // Pas de technologies spécifiques

    fr: {
      slug: "site-web-konvergo",
      metaDescriptionText: "Étude de cas du site web Konvergo. Pages dédiées aux apps Open Source. Découvrez cette réalisation de l'agence Kapreon.",
      title: "Simplifier l'accès à l'Open Source pour les entreprises",
      location: "Montréal, Canada",
      buttonLabel: "",
      descriptionTitle: "Créer des parcours clairs pensés pour convertir",
      descriptionText: "Dès l'accueil, le site déploie l'écosystème de Konvergo à travers des parcours dédiés à chaque application : ERP, Allo, Cal, etc. Chaque application est structurée autour d'une page indépendante, articulée sur des sections claires : promesse produit, avantages compétitifs, démonstrations et appels à l'action. Nous avons conçu une navigation UX fluide et un système d'ancrages internes pour faciliter l'exploration. L'ensemble s'inscrit dans l'identité visuelle forte de Konvergo.",
      buttonText: "test",
      longDescription: {
        title: "Une structure de site web qui respire",
        leftText: "Le design a été construit autour d'une logique modulaire, avec des gabarits sur-mesure évolutifs. Le parcours utilisateur a été pensé pour maximiser la compréhension des offres : accès aux solutions, démonstrations, cas d'usages.",
        rightText: "Visuellement, le site suit la direction artistique de Konvergo : couleurs vives, typographie arrondie, illustrations sur-mesure. L'objectif n'était pas de produire un site « corporate » sans relief, mais de créer une expérience fidèle à l'approche humaine de Konvergo."
      }
    },
    en: {
      slug: "konvergo-website",
      metaDescriptionText: "Konvergo website case study. Pages dedicated to Open Source apps. Discover this achievement by Kapreon agency.",
      title: "Simplifying Open Source access for businesses",
      location: "Montreal, Canada",
      buttonLabel: "",
      descriptionTitle: "Creating clear conversion-focused pathways",
      descriptionText: "From the homepage, the site deploys Konvergo's ecosystem through dedicated pathways for each application: ERP, Allo, Cal, etc. Each application is structured around an independent page, articulated on clear sections: product promise, competitive advantages, demonstrations and calls to action. We designed fluid UX navigation and an internal anchor system to facilitate exploration. The whole fits into Konvergo's strong visual identity.",
      buttonText: "test",
      longDescription: {
        title: "A website structure that breathes",
        leftText: "The design was built around a modular logic, with evolving custom templates. The user journey was designed to maximize understanding of offers: access to solutions, demonstrations, use cases.",
        rightText: "Visually, the site follows Konvergo's artistic direction: bright colors, rounded typography, custom illustrations. The objective was not to produce a 'corporate' site without relief, but to create an experience faithful to Konvergo's human approach."
      }
    },

    projectImages: {
      masonryBlocks: [
        {
          columns: 2,
          inContainer: true,
          images: [
            "/images/projects/konvergo/site-web/liste-solutions-konvergo-erp.webp",
            "/images/projects/konvergo/site-web/comptabilite-konvergo-mobile.webp"
          ]
        },
        {
          columns: 2,
          inContainer: true,
          images: [
            "/images/projects/konvergo/site-web/securite-konvergo.webp",
            "/images/projects/konvergo/site-web/konvergo-allo-mockup-site-web.png",
            "/images/projects/konvergo/site-web/page-distribution-konvergo.webp"
          ]
        },
        {
          columns: 1,
          inContainer: false,
          images: ["/images/projects/konvergo/site-web/solutions-konvergo-erp.webp"]
        },
        {
          columns: 1,
          inContainer: true,
          images: ["/images/projects/konvergo/site-web/gamme-konvergo-erp.webp"]
        },
        {
          columns: 1,
          inContainer: false,
          images: ["/images/projects/konvergo/site-web/ventes-konvergo-erp.webp"]
        },
        {
          columns: 1,
          inContainer: true,
          images: ["/images/projects/konvergo/site-web/industries-konvergo.webp"]
        },
        {
          columns: 1,
          inContainer: false,
          images: ["/images/projects/konvergo/site-web/services-conseils-konvergo.webp"]
        }
      ]
    }
  },

  {
    // Odoo Community Association
    id: "odoo-community-association",
    client: "Odoo Community Association",
    year: 2023,
    src: "projects/odoo-community-association/main-oca.webp",
    color: "#F0F0F5",
    clientSite: "https://odoo-community.org",
    visible: true,
    showDescriptionButton: false,
    showButton: true,

    // Taxonomies centralisées
    expertiseId: EXPERTISES.BRANDING.id,
    categoryIds: [CATEGORIES.LOGO_DESIGN.id, CATEGORIES.SOCIAL_MEDIA.id],
    industryId: INDUSTRIES.INFORMATION_TECHNOLOGY.id,
    technologyIds: [TECHNOLOGIES.FIGMA.id, TECHNOLOGIES.PHOTOSHOP.id, TECHNOLOGIES.ILLUSTRATOR.id],

    fr: {
      slug: "identite-odoo-community-association",
      metaDescriptionText: "Un logo qui valorise l'esprit collaboratif. L'Odoo Community Association unit la communauté open source à travers une identité visuelle rassembleuse.",
      title: "Collaborer sans frontières : une identité qui rassemble.",
      location: "Liège, Belgique",
      buttonLabel: "Voir le site web",
      descriptionTitle: "Un symbole pour une communauté globale",
      descriptionText: "L'OCA (Odoo Community Association), pilier de l'innovation Open Source depuis 2013, incarne l'excellence collaborative dans le développement d'Odoo. Engagé à rendre cette plateforme toujours plus robuste, l'association a confié à Kapreon le soin de matérialiser sa mission et ses valeurs dans un nouveau logo, miroir de ses valeurs intrinsèques : croissance, énergie, diversité et solidarité. Fruit d'une réflexion approfondie et de créativité, ce logo n'est pas qu'un symbole ; il est le reflet d'une communauté dynamique, unie par la passion d'Odoo et la force de la collaboration. Découvrez comment, ensemble, nous avons façonné le nouveau visage de l'OCA.",
      buttonText: "test",
      longDescription: {
        title: "La vision collective au cœur du design.",
        leftText: "Ce logo a été conçu pour traduire la mission collaborative de l'OCA. Les lignes convergentes, sans jamais se toucher, symbolisent une ouverture et une diversité harmonieuse. Les variations d'épaisseur reflètent la singularité de chaque membre, soulignant que chaque contribution, grande ou petite, a de la valeur.",
        rightText: "Bien plus qu'un logo, cette identité visuelle se déploie dans chaque aspect de la communication de l'OCA. Des stickers circulaires aux affiches événementielles, en passant par des adaptations spécifiques pour les OCA Days 2023, chaque déclinaison conserve l'âme du design tout en apportant une touche unique."
      }
    },
    en: {
      slug: "odoo-community-association-identity",
      metaDescriptionText: "A logo that values collaborative spirit. The Odoo Community Association unites the open source community through a unifying visual identity.",
      title: "Collaborating without borders: an identity that brings together.",
      location: "Liège, Belgium",
      buttonLabel: "View website",
      descriptionTitle: "A symbol for a global community",
      descriptionText: "The OCA (Odoo Community Association), pillar of Open Source innovation since 2013, embodies collaborative excellence in Odoo development. Committed to making this platform ever more robust, the association entrusted Kapreon with materializing its mission and values in a new logo, mirror of its intrinsic values: growth, energy, diversity and solidarity. The fruit of deep reflection and creativity, this logo is not just a symbol; it is the reflection of a dynamic community, united by the passion for Odoo and the strength of collaboration. Discover how, together, we shaped the new face of the OCA.",
      buttonText: "test",
      longDescription: {
        title: "Collective vision at the heart of design.",
        leftText: "This logo was designed to translate the OCA's collaborative mission. The converging lines, never touching, symbolize harmonious openness and diversity. The variations in thickness reflect the uniqueness of each member, emphasizing that each contribution, large or small, has value.",
        rightText: "Much more than a logo, this visual identity unfolds in every aspect of OCA communication. From circular stickers to event posters, including specific adaptations for OCA Days 2023, each variation preserves the soul of the design while bringing a unique touch."
      }
    },

    projectImages: {
      masonryBlocks: [
        {
          columns: 2,
          inContainer: true,
          images: [
            "/images/projects/odoo-community-association/grille-logo-oca.webp",
            "/images/projects/odoo-community-association/stickers-oca.webp"
          ]
        },
        {
          columns: 1,
          inContainer: true,
          images: ["/images/projects/odoo-community-association/panneau-publicitaire-oca.webp"]
        },
        {
          columns: 2,
          inContainer: true,
          images: [
            "/images/projects/odoo-community-association/panneau-oca.webp",
            "/images/projects/odoo-community-association/carte-affaires-oca.webp"
          ]
        }
      ]
    }
  },

  {
    // Copilote (visible: false)
    id: "copilote",
    client: "Copilote",
    year: 2023,
    src: "projects/copilote/main-copilote.webp",
    color: "#213638",
    clientSite: "https://odoo-community.org",
    visible: false,
    showDescriptionButton: false,
    showButton: false,

    // Taxonomies centralisées
    expertiseId: EXPERTISES.UX_DESIGN.id,
    categoryIds: [CATEGORIES.LOGO_DESIGN.id, CATEGORIES.SOCIAL_MEDIA.id],
    industryId: INDUSTRIES.FINANCE.id,
    technologyIds: [TECHNOLOGIES.FIGMA.id, TECHNOLOGIES.ODOO.id, TECHNOLOGIES.PHOTOSHOP.id],

    fr: {
      slug: "identite-copilote",
      metaDescriptionText: "Une image rassurante. Copilote allie protection et confiance, porté par des couleurs toniques et du dynamisme.",
      title: "Guider les finances avec style et élégance.",
      location: "Brossard, Canada",
      buttonLabel: "",
      descriptionTitle: "Un logotype moderne qui relie et protège",
      descriptionText: "Le logotype de Copilote est conçu pour incarner l'essence même de leur mission : établir une connexion fluide entre les entreprises et leurs objectifs financiers. La demi-sphère évoque une protection bienveillante, tandis que la typographie moderne et épurée reflète une approche  rassurante. Les couleurs dynamiques — un orange tonifiant et un vert symbolisant la croissance — apportent une dimension optimiste et énergique à l'image de la marque.",
      buttonText: "test",
      longDescription: {
        title: "De la rondeur et du mouvement.",
        leftText: "Développée autour des formes circulaires, l'identité visuelle de Copilote crée une symbiose visuelle qui exprime mouvement et rondeur. Ces formes, utilisées de manière créative dans divers supports, apportent une richesse graphique dynamique : elles encadrent des images, émergent dans des animations, et établissent une continuité visuelle entre les supports.",
        rightText: "L'identité graphique va au-delà du logo. En collaboration avec le client, nous avons conçu un système de design de gabarits pour les réseaux sociaux. Ces outils permettent à l'équipe interne de Copilote de produire des contenus variés en toute autonomie. Par ailleurs, le site web, déployé sur Odoo, a été pensé pour offrir une expérience utilisateur fluide et accessible, respectant les principes d'ergonomie et d'efficacité."
      }
    },
    en: {
      slug: "copilote-identity",
      metaDescriptionText: "A reassuring image. Copilote combines protection and trust, carried by tonic colors and dynamism.",
      title: "Guiding finances with style and elegance.",
      location: "Brossard, Canada",
      buttonLabel: "",
      descriptionTitle: "A modern logotype that connects and protects",
      descriptionText: "The Copilote logotype is designed to embody the very essence of their mission: establishing a fluid connection between companies and their financial objectives. The half-sphere evokes benevolent protection, while the modern and refined typography reflects a reassuring approach. The dynamic colors — a toning orange and a green symbolizing growth — bring an optimistic and energetic dimension to the brand image.",
      buttonText: "test",
      longDescription: {
        title: "Roundness and movement.",
        leftText: "Developed around circular forms, Copilote's visual identity creates a visual symbiosis that expresses movement and roundness. These forms, used creatively in various supports, bring dynamic graphic richness: they frame images, emerge in animations, and establish visual continuity between supports.",
        rightText: "The graphic identity goes beyond the logo. In collaboration with the client, we designed a template design system for social networks. These tools allow Copilote's internal team to produce varied content autonomously. Furthermore, the website, deployed on Odoo, was designed to offer a fluid and accessible user experience, respecting the principles of ergonomics and efficiency."
      }
    },

    projectImages: {
      masonryBlocks: [
        {
          columns: 2,
          inContainer: true,
          images: [
            "/images/projects/copilote/logo-copilote.gif",
            "/images/projects/copilote/plaque-verre-copilote.webp"
          ]
        },
        {
          columns: 1,
          inContainer: true,
          images: ["/images/projects/copilote/wireframe-gabarit-reseaux-sociaux-copilote.webp"]
        },
        {
          columns: 2,
          inContainer: true,
          images: [
            "/images/projects/copilote/gabarit-reseaux-sociaux-copilote.webp",
            "/images/projects/copilote/gabarits-copilote.gif"
          ]
        }
      ]
    }
  },

  {
    // Kolabus
    id: "kolabus",
    client: "Kolabus",
    year: 2022,
    src: "projects/kolabus/main-kolabus.webp",
    color: "#17151C",
    clientSite: "https://kolabus.com",
    visible: true,
    showDescriptionButton: false,
    showButton: false,

    // Taxonomies centralisées
    expertiseId: EXPERTISES.WEB_DEVELOPMENT.id,
    categoryIds: [CATEGORIES.LOGO_DESIGN.id, CATEGORIES.SOCIAL_MEDIA.id],
    industryId: INDUSTRIES.INFORMATION_TECHNOLOGY.id,
    technologyIds: [TECHNOLOGIES.FIGMA.id, TECHNOLOGIES.ODOO.id, TECHNOLOGIES.HTML_SCSS_JS.id, TECHNOLOGIES.PHOTOSHOP.id],

    fr: {
      slug: "identite-kolabus",
      metaDescriptionText: "Une identité nourrie par l'art marocain. Kolabus marie rigueur et influences mauresques grâce à des motifs géométriques raffinés.",
      title: "Façonner une identité entre la rigueur et le mauresque.",
      location: "Marrakech, Maroc",
      buttonLabel: "",
      descriptionTitle: "Du concept au logo : une identité ancrée dans la culture",
      descriptionText: "Kolabus, acteur marocain de l'intégration logicielle, a choisi Kapreon pour incarner sa vision à travers une identité visuelle mêlant rigueur et style mauresque. Loin d'un simple exercice esthétique, ce projet a débuté par une immersion dans l'art marocain, revisité pour refléter l'ADN de la marque. Le résultat ? Un logo épuré, des couleurs chaudes inspirées des paysages marocains, et des motifs géométriques symbolisant précision.",
      buttonText: "test",
      longDescription: {
        title: "De la rigueur et de l'éclat.",
        leftText: "Notre agence a élaboré un guide de normes graphiques détaillant chaque élément de l'identité Kolabus. Typographies sobres pour la rigueur, doré lumineux évoquant le style mauresque, et usage maîtrisé des motifs géométriques : ce cadre permet une créativité cohérente, des présentations clients aux publications LinkedIn. Le guide inclut même des gabarits prêts à l'emploi, assurant que chaque support graphique renforce la reconnaissance de la marque.",
        rightText: "Le logo, cœur de l'identité, fusionne un carré avec des lignes dynamiques inspirées des entrelacs traditionnels. Cette dualité se prolonge dans le site web développé sur Odoo : animations subtiles, espaces aérés et jeux de lumière. Même les plus petits détails, comme les icônes ou les visuels de cas clients, suivent cette charte vivante, adapté à tous les supports."
      }
    },
    en: {
      slug: "kolabus-identity",
      metaDescriptionText: "An identity nourished by Moroccan art. Kolabus marries rigor and Moorish influences through refined geometric patterns.",
      title: "Shaping an identity between rigor and Moorish style.",
      location: "Marrakech, Morocco",
      buttonLabel: "",
      descriptionTitle: "From concept to logo: an identity rooted in culture",
      descriptionText: "Kolabus, Moroccan actor in software integration, chose Kapreon to embody its vision through a visual identity mixing rigor and Moorish style. Far from a simple aesthetic exercise, this project began with an immersion in Moroccan art, revisited to reflect the brand's DNA. The result? A refined logo, warm colors inspired by Moroccan landscapes, and geometric patterns symbolizing precision.",
      buttonText: "test",
      longDescription: {
        title: "Rigor and brilliance.",
        leftText: "Our agency developed a graphic standards guide detailing each element of Kolabus identity. Sober typography for rigor, luminous gold evoking Moorish style, and controlled use of geometric patterns: this framework allows coherent creativity, from client presentations to LinkedIn publications. The guide even includes ready-to-use templates, ensuring that each graphic support reinforces brand recognition.",
        rightText: "The logo, heart of the identity, merges a square with dynamic lines inspired by traditional interlacing. This duality extends into the website developed on Odoo: subtle animations, airy spaces and light play. Even the smallest details, like icons or client case visuals, follow this living charter, adapted to all supports."
      }
    },

    projectImages: {
      masonryBlocks: [
        {
          columns: 1,
          inContainer: false,
          images: ["/images/projects/kolabus/document-kolabus.webp"]
        },
        {
          columns: 1,
          inContainer: true,
          images: ["/images/projects/kolabus/reseaux-sociaux-kolabus.webp"]
        },
        {
          columns: 1,
          inContainer: false,
          images: ["/images/projects/kolabus/esquisse-kolabus.webp"]
        },
        {
          columns: 1,
          inContainer: true,
          images: ["/images/projects/kolabus/grille-photos-kolabus.webp"]
        },
        {
          columns: 2,
          inContainer: true,
          images: [
            "/images/projects/kolabus/logo-kolabus-1.webp",
            "/images/projects/kolabus/logo-kolabus-2.webp"
          ]
        },
        {
          columns: 1,
          inContainer: true,
          images: ["/images/projects/kolabus/alignement-logo-kolabus.webp"]
        },
        {
          columns: 2,
          inContainer: true,
          images: [
            "/images/projects/kolabus/guide-kolabus-1.webp",
            "/images/projects/kolabus/guide-kolabus-2.webp",
            "/images/projects/kolabus/guide-kolabus-3.webp",
            "/images/projects/kolabus/guide-kolabus-4.webp",
            "/images/projects/kolabus/guide-kolabus-5.webp",
            "/images/projects/kolabus/guide-kolabus-6.webp"
          ]
        },
        {
          columns: 1,
          inContainer: true,
          images: ["/images/projects/kolabus/capture-site-web-kolabus.gif"]
        },
        {
          columns: 2,
          inContainer: true,
          images: [
            "/images/projects/kolabus/capture-site-web-kolabus-2.gif",
            "/images/projects/kolabus/animation-site-web-kolabus.gif"
          ]
        }
      ]
    }
  },

  {
    // MDBank (visible: false)
    id: "mdbank",
    client: "MDBank",
    year: 2021,
    src: "projects/mdbank/main-mdbank.webp",
    color: "#c6c6e5",
    clientSite: "",
    visible: false,
    showDescriptionButton: false,
    showButton: false,

    // Taxonomies centralisées
    expertiseId: EXPERTISES.UX_DESIGN.id,
    categoryIds: [CATEGORIES.LOGO_DESIGN.id, CATEGORIES.MOBILE_DESIGN.id, CATEGORIES.PROTOTYPING.id, CATEGORIES.PRINT_MATERIALS.id, CATEGORIES.VEHICLE_LETTERING.id],
    industryId: INDUSTRIES.FINANCE.id,
    technologyIds: [TECHNOLOGIES.FIGMA.id, TECHNOLOGIES.ILLUSTRATOR.id],

    fr: {
      slug: "logo-mdbank",
      metaDescriptionText: "La solution en ligne dont l'identité visuelle exprime un écosystème financier vivant pensé pour la génération connectée.",
      title: "Centraliser la banque en ligne pour la génération connectée",
      location: "Montréal, Canada",
      buttonLabel: "",
      descriptionTitle: "Un design qui incarne la nouvelle ère bancaire",
      descriptionText: "Les trois cercles de MDBank ne sont pas juste un hasard : ils incarnent l'idée d'un écosystème financier complet. Chaque couleur distingue une facette qui converge pour simplifier la gestion bancaire. Superposés, ils dessinent un mouvement vers l'avant, symbole de progrès et d'enrichissement. Le tout, dans une esthétique minimaliste qui reflète la simplicité de la banque en ligne.",
      buttonText: "test",
      longDescription: {
        title: "Une interface visionnaire pensée pour les jeunes adultes",
        leftText: "La banque en ligne doit être synonyme de simplicité. Chaque détail de la plateforme MDBank a été pensé pour que gérer son argent devienne une expérience fluide et intuitive. Une navigation limpide et des visuels accessibles garantissent une utilisation efficace pour les utilisateurs.",
        rightText: "Gérer son argent ne devrait pas être un casse-tête. Avec MDBank, la complexité disparaît pour laisser place à l'essentiel : une navigation claire, des outils intelligents et un design qui inspire confiance. Chaque clic devient une action naturelle."
      }
    },
    en: {
      slug: "mdbank-logo",
      metaDescriptionText: "The online solution whose visual identity expresses a living financial ecosystem designed for the connected generation.",
      title: "Centralizing online banking for the connected generation",
      location: "Montreal, Canada",
      buttonLabel: "",
      descriptionTitle: "A design that embodies the new banking era",
      descriptionText: "MDBank's three circles are not just a coincidence: they embody the idea of a complete financial ecosystem. Each color distinguishes a facet that converges to simplify banking management. Superimposed, they draw a forward movement, symbol of progress and enrichment. All in a minimalist aesthetic that reflects the simplicity of online banking.",
      buttonText: "test",
      longDescription: {
        title: "A visionary interface designed for young adults",
        leftText: "Online banking must be synonymous with simplicity. Every detail of the MDBank platform has been designed so that managing your money becomes a fluid and intuitive experience. Clear navigation and accessible visuals guarantee efficient use for users.",
        rightText: "Managing your money shouldn't be a headache. With MDBank, complexity disappears to make way for the essential: clear navigation, intelligent tools and a design that inspires confidence. Every click becomes a natural action."
      }
    },

    projectImages: {
      masonryBlocks: [
        {
          columns: 2,
          inContainer: true,
          images: [
            "/images/projects/mdbank/alignement-logo-mdbank.webp",
            "/images/projects/mdbank/logo-mdbank.webp"
          ]
        },
        {
          columns: 1,
          inContainer: false,
          images: ["/images/projects/mdbank/affiches-mdbank.webp"]
        },
        {
          columns: 1,
          inContainer: true,
          images: ["/images/projects/mdbank/wireframe-mdbank.webp"]
        },
        {
          columns: 2,
          inContainer: true,
          images: [
            "/images/projects/mdbank/mobile-mdbank.webp",
            "/images/projects/mdbank/onboarding-mdbank.webp"
          ]
        }
      ]
    }
  },

    {
    id: "plaida",
    client: "Plaida",
    year: 2025,
    src: "projects/plaida/main-plaida.webp",
    color: "#17181A",
    clientSite: "",
    visible: true,
    showDescriptionButton: false,
    showButton: false,

    // Taxonomies centralisées
    expertiseId: EXPERTISES.WEB_DEVELOPMENT.id,
    categoryIds: [CATEGORIES.PROTOTYPING.id, CATEGORIES.MOBILE_DESIGN.id, CATEGORIES.LOGO_DESIGN.id],
    industryId: INDUSTRIES.INFORMATION_TECHNOLOGY.id,
    technologyIds: [TECHNOLOGIES.FIGMA.id, TECHNOLOGIES.NEXTJS.id, TECHNOLOGIES.GENERATIVE_AI.id],

    fr: {
      slug: "application-plaida",
      metaDescriptionText: "L’app qui convertit des clichés amateurs en portraits studio de qualité professionnelle en moins de 10 minutes.",
      title: "De selfie à portrait pro en 10 minutes.",
      location: "Longueuil, Canada",
      buttonLabel: "",
      descriptionTitle: "Un frein à la crédibilité",
      descriptionText: (
        <p>
          LinkedIn compte plus de 1 milliard de profils, mais la majorité des utilisateurs n’ont pas accès à un photographe professionnel. Résultat: leur photo de profil limite leur crédibilité. Kapreon a voulu lever ce frein au personal branding en créant <AnimatedLink href="https://plaida.com" standalone external>Plaida</AnimatedLink>. Permettre à n’importe qui d’obtenir un portrait studio de qualité professionnelle à partir de quelques photos prises au téléphone, livré en moins de dix minutes, sans compétence technique et pour quelques dollars seulement.
        </p>
      ),
      buttonText: "test",
      longDescription: {
        title: "Un onboarding IA ultra simple",
        leftText: "Nous avons commencé par étudier directement les personnes cibles de Plaida : cherchers d'emplois, recruteurs et entrepreneurs. Cela nous a permis d'identifier un point bloquant : la plupart n'ont ni le budget, ni le temps pour un vrai shooting, mais savent que leur photo influence leur crédibilité.",
        rightText: "Après réflexions, nous avons imaginé un onboarding ultra-optimisé : quatres photos d'échantillons qui entraîne un modèle d'IA, puis l'utilisateur peut générer des photos de ce modèle en quelques minutes."
      }
    },
    en: {
      slug: "plaida-application",
      metaDescriptionText: "The app that turns amateur snapshots into studio-quality professional headshots in under 10 minutes.",
      title: "From selfie to pro portrait in 10 minutes.",
      location: "Longueuil, Canada",
      buttonLabel: "",
      descriptionTitle: "A credibility gap",
      descriptionText: (
        <p>
          LinkedIn hosts over 1 billion profiles, yet most users don’t have access to a professional photographer. As a result, their profile photo undermines their credibility. Kapreon set out to remove this personal-branding hurdle by creating <AnimatedLink href="https://plaida.com" standalone external>Plaida</AnimatedLink>. The goal: let anyone obtain a studio-grade portrait from a few phone snapshots, delivered in under ten minutes, with zero technical skills and for just a few dollars.
        </p>
      ),
      buttonText: "test",
      longDescription: {
        title: "Ultra-simple AI onboarding",
        leftText: "We began by studying directly with Plaida’s target users: job seekers, recruiters and entrepreneurs. This revealed a clear pain point: most lack both the budget and the time for a real photo shoot, yet they know their picture shapes their credibility.",
        rightText: "From these insights, we designed an ultra-optimised onboarding: four sample photos train an AI model, then the user can generate studio-quality shots within minutes."
      }
    },


    projectImages: {
      masonryBlocks: [
        {
          columns: 2,
          inContainer: true,
          images: [
            "/images/projects/plaida/icone-iphone-plaida.webp",
            "/images/projects/plaida/mosaique-plaida.webp"
          ]
        },
        {
          columns: 1,
          inContainer: false,
          images: ["/images/projects/plaida/ordinateur-portable-plaida.webp"]
        },
        {
          columns: 1,
          inContainer: true,
          images: ["/images/projects/plaida/onboarding-plaida.webp"]
        },
        {
          columns: 2,
          inContainer: true,
          images: [
            "/images/projects/plaida/iphone-rocher-plaida.webp",
            "/images/projects/plaida/iphone-roches-plaida.webp"
          ]
        }
      ]
    }
  }
];

// Fonction pour générer les projets avec les taxonomies résolues
const projects = [];

projectsBase.forEach(project => {
  // Version française
  projects.push({
    ...project.fr,
    locale: 'fr',
    client: project.client,
    year: project.year,
    src: project.src,
    color: project.color,
    clientSite: project.clientSite,
    visible: project.visible,
    showDescriptionButton: project.showDescriptionButton,
    showButton: project.showButton,
    buttonLink: project.clientSite,
    projectImages: project.projectImages,
    // Résolution des taxonomies pour le français
    expertise: getExpertiseLabel(project.expertiseId, 'fr'),
    categories: project.categoryIds?.map(id => getCategoryLabel(id, 'fr')) || [],
    industry: getIndustryLabel(project.industryId, 'fr'),
    technologies: project.technologyIds?.map(id => getTechnologyLabel(id, 'fr')) || [],
    // Garder les IDs pour le filtrage
    expertiseId: project.expertiseId,
    categoryIds: project.categoryIds,
    industryId: project.industryId,
    technologyIds: project.technologyIds
  });

  // Version anglaise
  projects.push({
    ...project.en,
    locale: 'en',
    client: project.client,
    year: project.year,
    src: project.src,
    color: project.color,
    clientSite: project.clientSite,
    visible: project.visible,
    showDescriptionButton: project.showDescriptionButton,
    showButton: project.showButton,
    buttonLink: project.clientSite,
    projectImages: project.projectImages,
    // Résolution des taxonomies pour l'anglais
    expertise: getExpertiseLabel(project.expertiseId, 'en'),
    categories: project.categoryIds?.map(id => getCategoryLabel(id, 'en')) || [],
    industry: getIndustryLabel(project.industryId, 'en'),
    technologies: project.technologyIds?.map(id => getTechnologyLabel(id, 'en')) || [],
    // Garder les IDs pour le filtrage
    expertiseId: project.expertiseId,
    categoryIds: project.categoryIds,
    industryId: project.industryId,
    technologyIds: project.technologyIds
  });
});

export default projects;