// Taxonomies centralisées pour les projets
// Source unique de vérité pour les expertises et catégories

export const EXPERTISES = {
  BRANDING: {
    id: 'branding',
    fr: 'Image de marque',
    en: 'Brand Identity'
  },
  WEB_DEVELOPMENT: {
    id: 'web_development', 
    fr: 'Développement web',
    en: 'Web Development'
  },
  UX_DESIGN: {
    id: 'ux_design',
    fr: 'Design UX', 
    en: 'UX Design'
  }
};

export const CATEGORIES = {
  LOGO_DESIGN: {
    id: 'logo_design',
    fr: 'Conception de logo',
    en: 'Logo Design'
  },
  SOCIAL_MEDIA: {
    id: 'social_media',
    fr: 'Supports réseaux sociaux',
    en: 'Social Media Assets'
  },
  PRINT_MATERIALS: {
    id: 'print_materials',
    fr: 'Supports imprimés',
    en: 'Print Materials'
  },
  PRODUCT_DESIGN: {
    id: 'product_design',
    fr: 'Design de produits',
    en: 'Product Design'
  },
  WEB_DEVELOPMENT: {
    id: 'web_development',
    fr: 'Développement web',
    en: 'Web Development'
  },
  ODOO_DEVELOPMENT: {
    id: 'odoo_development',
    fr: 'Développement Odoo',
    en: 'Odoo Development'
  },
  PROTOTYPING: {
    id: 'prototyping',
    fr: 'Prototypage',
    en: 'Prototyping'
  },
  MOBILE_DESIGN: {
    id: 'mobile_design',
    fr: 'Design mobile',
    en: 'Mobile Design'
  },
  VEHICLE_LETTERING: {
    id: 'vehicle_lettering',
    fr: 'Lettrage automobile',
    en: 'Vehicle Lettering'
  }
};

export const INDUSTRIES = {
  FINANCE: {
    id: 'finance',
    fr: 'Finance',
    en: 'Finance'
  },
  FINANCIAL_SERVICES: {
    id: 'financial_services',
    fr: 'Services financiers',
    en: 'Financial Services'
  },
  INFORMATION_TECHNOLOGY: {
    id: 'information_technology',
    fr: 'Technologies de l\'information',
    en: 'Information Technology'
  },
  BEAUTY_WELLNESS: {
    id: 'beauty_wellness',
    fr: 'Beauté et bien-être',
    en: 'Beauty and Wellness'
  }
};

export const TECHNOLOGIES = {
  FIGMA: {
    id: 'figma',
    name: 'Figma'
  },
  HTML_CSS_JS: {
    id: 'html_css_js',
    name: 'HTML, CSS, JS'
  },
  ODOO: {
    id: 'odoo',
    name: 'Odoo'
  },
  PHOTOSHOP: {
    id: 'photoshop',
    name: 'Adobe Photoshop'
  },
  ILLUSTRATOR: {
    id: 'illustrator',
    name: 'Adobe Illustrator'
  },
  HTML_SCSS_JS: {
    id: 'html_scss_js',
    name: 'HTML, SCSS, JavaScript'
  },
  NEXTJS: {
    id: 'nextjs',
    name: 'Next.js'
  },
  GENERATIVE_AI: {
    id: 'generative_ai',
    name: 'Generative AI'
  }
};

// Fonctions utilitaires
export const getExpertiseLabel = (expertiseId, locale = 'fr') => {
  const expertise = Object.values(EXPERTISES).find(e => e.id === expertiseId);
  return expertise ? expertise[locale] : '';
};

export const getCategoryLabel = (categoryId, locale = 'fr') => {
  const category = Object.values(CATEGORIES).find(c => c.id === categoryId);
  return category ? category[locale] : '';
};

export const getIndustryLabel = (industryId, locale = 'fr') => {
  const industry = Object.values(INDUSTRIES).find(i => i.id === industryId);
  return industry ? industry[locale] : '';
};

export const getTechnologyLabel = (technologyId, locale = 'fr') => {
  const technology = Object.values(TECHNOLOGIES).find(t => t.id === technologyId);
  return technology ? technology.name : '';
};

export const getExpertiseId = (label, locale = 'fr') => {
  const expertise = Object.values(EXPERTISES).find(e => e[locale] === label);
  return expertise ? expertise.id : null;
};

export const getCategoryId = (label, locale = 'fr') => {
  const category = Object.values(CATEGORIES).find(c => c[locale] === label);
  return category ? category.id : null;
};

// Obtenir toutes les expertises pour les filtres
export const getAllExpertises = (locale = 'fr') => {
  return Object.values(EXPERTISES).map(expertise => ({
    id: expertise.id,
    label: expertise[locale]
  }));
};

// Obtenir toutes les catégories
export const getAllCategories = (locale = 'fr') => {
  return Object.values(CATEGORIES).map(category => ({
    id: category.id,
    label: category[locale]
  }));
};

// Obtenir toutes les industries
export const getAllIndustries = (locale = 'fr') => {
  return Object.values(INDUSTRIES).map(industry => ({
    id: industry.id,
    label: industry[locale]
  }));
};

// Obtenir toutes les technologies
export const getAllTechnologies = (locale = 'fr') => {
  return Object.values(TECHNOLOGIES).map(technology => ({
    id: technology.id,
    label: technology.name
  }));
};
