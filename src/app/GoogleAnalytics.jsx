'use client'
import { useEffect } from 'react'
import Script from 'next/script'
import { usePathname, useSearchParams } from 'next/navigation'
import * as gtag from '../../gtag'

export default function GoogleAnalytics() {
  const pathname = usePathname()
  const searchParams = useSearchParams()
  const url = pathname + (searchParams.toString() ? `?${searchParams.toString()}` : '')

  // à chaque changement de route on déclenche un pageview
  useEffect(() => {
    gtag.pageview(url)
  }, [url])

  // on n’injecte les scripts qu’en prod
  if (!gtag.GA_TRACKING_ID) {
    console.warn('no GA id')
    return null
  }
  
    
    console.log('GA ID:', gtag.GA_TRACKING_ID);

  return (
    <>
      <Script
        strategy="afterInteractive"
        src={`https://www.googletagmanager.com/gtag/js?id=${gtag.GA_TRACKING_ID}`}
      />
      <Script id="gtag-init" strategy="afterInteractive">
        {`
          window.dataLayer = window.dataLayer || [];
          function gtag(){dataLayer.push(arguments);}
          gtag('js', new Date());
          gtag('config', '${gtag.GA_TRACKING_ID}', {
            page_path: window.location.pathname,
          });
        `}
      </Script>
    </>
  )
}