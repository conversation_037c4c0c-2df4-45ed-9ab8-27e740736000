import ProjectDetailClient from './ProjectDetailClient';
import allProjects from '../../../../data/projectsData';
import { notFound } from 'next/navigation';
import { getTranslation } from '@/hooks/useTranslation';

export async function generateStaticParams() {
  return allProjects.map((project) => ({
    slug: project.slug,
  }));
}

export async function generateMetadata({ params }) {
  const { slug, locale = 'fr' } = params;
  const project = allProjects.find((p) => p.slug === slug);
  const t = getTranslation(locale, 'projects');

  if (!project) {
    return {
      title: t('project_not_found'),
      description: t('project_not_found_description'),
    };
  }

  const title = `${project.expertise} pour ${project.client} | Kapreon`;
  const description = project.metaDescriptionText;

  return {
    title,
    description,
    openGraph: {
      title,
      description,
      images: project.ogImage
        ? [{ url: project.ogImage }]
        : [{ url: "https://kapreon.com/og-default.jpg" }],
      url: `https://kapreon.com/${locale}/projets/${project.slug}`,
      locale: locale === 'fr' ? 'fr_CA' : 'en_CA',
    },
    twitter: {
      card: "summary_large_image",
      title,
      description,
      images: project.ogImage
        ? [project.ogImage]
        : ["https://kapreon.com/og-default.jpg"],
    },
  };
}

export default function ProjectDetailPage({ params }) {
  const { slug } = params;
  const projectExists = allProjects.some((p) => p.slug === slug);
  if (!projectExists) {
    notFound();
  }

  return <ProjectDetailClient params={params} />;
}
