'use client';
import React, { useEffect, useState } from 'react';
// Debug version 2
import { notFound } from 'next/navigation';
import Intro from '../../../../components/Case/Intro';
import Description from '../../../../components/Description';
import LongDescription from '../../../../components/LongDescription';
import MyMasonry from '../../../../components/Masonry';
import NextCase from '@/components/Case/NextCase';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import allProjects from '../../../../data/projectsData';

gsap.registerPlugin(ScrollTrigger);

export default function ProjectDetailClient({ params }) {
  const { slug, locale = 'fr' } = params;

  // Trouver le projet par slug (comme pour les articles de blog)
  const project = allProjects.find((p) => p.slug === slug);

  if (!project) {
    notFound();
  }

  return (
    <main className="main">
      <Intro project={project} locale={locale} />
      <Description 
        descriptionTitle={project.descriptionTitle} 
        descriptionText={project.descriptionText} 
        showButton={project.showDescriptionButton} 
        buttonText={project.buttonText} 
      />
      {project.projectImages?.masonryBlocks?.map((block, index) => {
        const masonryElement = (
          <MyMasonry
            images={block.images}
            columns={block.columns}
          />
        );
        return (
          <React.Fragment key={index}>
            {block.inContainer ? (
              <div className="container">
                {masonryElement}
              </div>
            ) : (
              masonryElement
            )}
            {index === 1 && (
              <LongDescription
                title={project.longDescription.title}
                leftText={project.longDescription.leftText}
                rightText={project.longDescription.rightText}
              />
            )}
          </React.Fragment>
        );
      })}
      <NextCase />
    </main>
  );
}
