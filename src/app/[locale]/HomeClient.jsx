'use client';
import styles from '../page.module.scss'
import { useEffect, useState } from 'react'
import { AnimatePresence } from 'framer-motion';
import Preloader from '../../components/Preloader';
import Landing from '../../components/Landing';
import Hero from '../../components/Heros/Hero';
import AnimatedLink from '../../components/AnimatedLink';

import Link from 'next/link';
import Projects from '../../components/Projects';

import Description from '../../components/Description';
import SlidingImages from '../../components/SlidingImages';
// import Contact from '../../components/Contact';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import Button from '../../common/CubertoButton'; // Ajuste le chemin si besoin

gsap.registerPlugin(ScrollTrigger);

export default function HomeClient({ params }) {
  const [isLoading, setIsLoading] = useState(true);
  const locale = params.locale || 'fr';

  return (
    <main className={styles.main}>
      {/* <AnimatePresence mode='wait'>
        {isLoading && <Preloader />}
      </AnimatePresence> */}
      {/* <Landing />
      <Description /> */}
      <Hero locale={locale} />
      <Projects isLoading={isLoading} locale={locale} />

      <SlidingImages />
      {/* <Contact /> */}
    </main>
  )
}
