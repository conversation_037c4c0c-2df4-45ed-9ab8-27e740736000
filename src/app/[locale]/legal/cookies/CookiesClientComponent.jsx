"use client";

import { allLegals } from 'contentlayer/generated';
import { notFound } from 'next/navigation';
import Hero from '@/components/Heros/Hero';
import { MDXComponent } from '@/components/MDXComponent';
import styles from './page.module.scss';

export default function CookiesClient({ params }) {
  const locale = params.locale || 'fr';
  const legal = allLegals.find((l) => l.slug === 'cookies' && l.locale === locale);

  if (!legal) notFound();

  return (
    <div className={styles.main}>
      <Hero
        title={legal.heroTitle}
        subtitle={legal.heroSubtitle}
        locale={locale}
        containerClass="container small"
      />
      <div className="container small">
        <div className={styles.legalContent}>
          <MDXComponent code={legal.body.code} />
        </div>
      </div>
    </div>
  );
}
