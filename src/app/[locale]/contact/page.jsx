import { getTranslation } from "@/hooks/useTranslation";
import ContactClient from './ContactClient';

export async function generateMetadata({ params }) {
  const locale = params.locale || 'fr';
  const t = getTranslation(locale, 'contact');

  return {
    title: t('title'),
    description: t('description'),
    openGraph: {
      title: t('title'),
      description: t('description'),
      images: [{ url: "https://kapreon.com/og-default.jpg" }],
      url: `https://kapreon.com/${locale}/contact`,
      locale: locale === 'fr' ? 'fr_CA' : 'en_CA',
    },
    twitter: {
      card: "summary_large_image",
      title: t('title'),
      description: t('description'),
      images: ["https://kapreon.com/og-default.jpg"],
    },
  };
}

export default function ContactPage({ params }) {
  return <ContactClient params={params} />;
}
