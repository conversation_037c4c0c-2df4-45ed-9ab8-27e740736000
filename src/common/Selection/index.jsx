"use client";

import { useEffect } from 'react';

const SelectionColorChanger = () => {
  useEffect(() => {
    const colors = ['#FFA3B2', '#19271B', '#FF413D'];
    let previousColor = null;
    let hasUpdated = false;

    const updateColor = () => {
      const selection = window.getSelection();
      if (!selection) return;
      const text = selection.toString();

      if (text && !hasUpdated) {
        hasUpdated = true;
        let newColor = colors[Math.floor(Math.random() * colors.length)];
        if (colors.length > 1) {
          while (newColor === previousColor) {
            newColor = colors[Math.floor(Math.random() * colors.length)];
          }
        }
        previousColor = newColor;
        document.documentElement.style.setProperty('--selection-color', newColor);
      }
      if (!text) {
        hasUpdated = false;
      }
    };

    document.addEventListener('selectionchange', updateColor);
    document.addEventListener('mousemove', updateColor);

    return () => {
      document.removeEventListener('selectionchange', updateColor);
      document.removeEventListener('mousemove', updateColor);
    };
  }, []);

  return null;
};

export default SelectionColorChanger;
