// src/components/CubertoButton/index.jsx

import React from 'react';
import styles from './style.module.scss';

const Button = ({ text, link, className }) => {
  return (
    <div className={`${styles.buttonWrapper} ${className}`}>
      <a className={`${styles.btnLink} ${styles.btnLinkCta} ${styles.xxl}`} href={link}>
        <span className={styles.btnBorder}></span>
        <span className={styles.btnRipple}>
          <span></span>
        </span>
        <span className={styles.btnTitle}>
          <span data-text={text}>{text}</span>
        </span>
      </a>
    </div>
  );
};

export default Button;
