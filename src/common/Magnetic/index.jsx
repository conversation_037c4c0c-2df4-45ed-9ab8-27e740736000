import React, { useEffect, useMemo, useRef } from "react";
import gsap from "gsap";

export default function Magnetic({ children, factor = 0.35, targetRef = null }) {
  const ref = useRef(null);

  // on considère mobile : pas de hover OU viewport < 1200 px
  const disable = useMemo(() => {
    if (typeof window === "undefined") return false; // SSR
    const noHover = window.matchMedia("(hover: none)").matches;
    const small   = window.matchMedia("(max-width: 1199px)").matches;
    return noHover || small;
  }, []);

  useEffect(() => {
    if (disable) return; // pas d’aimantation

    const tgt = targetRef?.current || ref.current;
    if (!tgt) return;

    const xTo = gsap.quickTo(ref.current, "x", { duration: 1.4, ease: "elastic.out(1,0.3)" });
    const yTo = gsap.quickTo(ref.current, "y", { duration: 1.4, ease: "elastic.out(1,0.3)" });

    const move = (e) => {
      const { left, top, width, height } = tgt.getBoundingClientRect();
      xTo((e.clientX - (left + width  / 2)) * factor);
      yTo((e.clientY - (top  + height / 2)) * factor);
    };
    const leave = () => { xTo(0); yTo(0); };

    tgt.addEventListener("mousemove", move);
    tgt.addEventListener("mouseleave", leave);
    return () => {
      tgt.removeEventListener("mousemove", move);
      tgt.removeEventListener("mouseleave", leave);
    };
  }, [disable, factor, targetRef]);

  return React.cloneElement(children, { ref });
}
