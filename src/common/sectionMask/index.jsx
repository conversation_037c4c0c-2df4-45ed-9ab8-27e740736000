import styles from './style.module.scss';

export default function SectionMask() {
  return (
    <picture className={styles.sectionMask}>
      <source
        media="(min-width: 1400px)"
        srcSet="/images/section-mask/mask-1400.svg"
      />
      <source
        media="(min-width: 1200px)"
        srcSet="/images/section-mask/mask-1200.svg"
      />
      <source
        media="(min-width: 992px)"
        srcSet="/images/section-mask/mask-992.svg"
      />
      <source
        media="(min-width: 768px)"
        srcSet="/images/section-mask/mask-768.svg"
      />
      <source
        media="(min-width: 480px)"
        srcSet="/images/section-mask/mask-480.svg"
      />
      {/* Fallback : s’il n’y a pas de CSS media-queries, on affiche celui pour 480 */}
      <img
        src="/images/section-mask/mask-480.svg"
        alt="Masque décoratif"
      />
    </picture>
  );
}
