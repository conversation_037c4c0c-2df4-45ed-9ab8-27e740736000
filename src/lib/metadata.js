import { getTranslation } from '@/hooks/useTranslation';

export function generatePageMetadata(locale, namespace, key, customData = {}) {
  const t = getTranslation(locale, namespace);
  
  const baseUrl = 'https://kapreon.com';
  const localePath = locale === 'en' ? '/en' : '';
  
  return {
    title: customData.title || t(`${key}.title`),
    description: customData.description || t(`${key}.description`),
    openGraph: {
      title: customData.title || t(`${key}.title`),
      description: customData.description || t(`${key}.description`),
      images: customData.images || [{ url: `${baseUrl}/og-default.jpg` }],
      url: `${baseUrl}${localePath}${customData.path || ''}`,
      locale: locale === 'fr' ? 'fr_CA' : 'en_CA',
      type: 'website',
      siteName: 'Kapreon',
    },
    twitter: {
      card: "summary_large_image",
      title: customData.title || t(`${key}.title`),
      description: customData.description || t(`${key}.description`),
      images: customData.images ? customData.images.map(img => img.url) : [`${baseUrl}/og-default.jpg`],
      creator: '@kapreon_',
      site: '@kapreon_',
    },
    alternates: {
      canonical: `${baseUrl}${localePath}${customData.path || ''}`,
      languages: {
        'fr-CA': `${baseUrl}${customData.path || ''}`,
        'en-CA': `${baseUrl}/en${customData.path || ''}`,
      },
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
  };
}

export const defaultMetadata = {
  metadataBase: new URL('https://kapreon.com'),
  verification: {
    google: '1XC-nn6NtCPE3WayReHxS2iSwBKwDk8o79nqknbwDMU',
  },
  category: 'technology',
  keywords: [
    'agence créative',
    'design web',
    'développement web',
    'UX/UI design',
    'identité visuelle',
    'Montréal',
    'creative agency',
    'web design',
    'web development',
    'visual identity',
    'Montreal'
  ],
};
