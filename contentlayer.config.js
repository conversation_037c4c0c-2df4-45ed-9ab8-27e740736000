// contentlayer.config.js
import { defineDocumentType, makeSource } from 'contentlayer/source-files';
import rehypePrism from 'rehype-prism-plus';
import remarkGfm from 'remark-gfm';

const Post = defineDocumentType(() => ({
  name: 'Post',
  filePathPattern: 'blog/**/*.mdx',
  contentType: 'mdx',
  fields: {
    title:   { type: 'string', required: true },
    excerpt: { type: 'string', required: true },
    date:    { type: 'date',   required: true },   // date de publication
    updated: { type: 'date' },                     // nouvelle: dernière MAJ
    category:{ type: 'string' },                   // nouvelle: 1 seule catégorie
    tags:    { type: 'list', of: { type: 'string' } },
    cover: { type: 'string' },
    published: { type: 'boolean', default: true },
  },
  computedFields: {
    locale: {
      type: 'string',
      resolve: (post) => {
        // Extraire la langue du chemin : blog/fr/article.mdx -> fr
        const pathParts = post._raw.flattenedPath.split('/');
        return pathParts[1] || 'fr'; // défaut français
      },
    },
    slug: {
      type: 'string',
      resolve: (post) => {
        // Extraire le slug : blog/fr/article.mdx -> article
        return post._raw.flattenedPath.split('/').pop();
      },
    },
  },
}));

const Legal = defineDocumentType(() => ({
  name: 'Legal',
  filePathPattern: 'legal/**/*.mdx',
  contentType: 'mdx',
  fields: {
    title: { type: 'string', required: true },
    description: { type: 'string', required: true },
    heroTitle: { type: 'string', required: true },
    heroSubtitle: { type: 'string', required: true },
    lastUpdated: { type: 'date', required: true },
  },
  computedFields: {
    locale: {
      type: 'string',
      resolve: (doc) => {
        // Extraire la langue du chemin : legal/fr/cookies.md -> fr
        const pathParts = doc._raw.flattenedPath.split('/');
        return pathParts[1] || 'fr';
      },
    },
    slug: {
      type: 'string',
      resolve: (doc) => {
        // Extraire le slug : legal/fr/cookies.md -> cookies
        return doc._raw.flattenedPath.split('/').pop();
      },
    },
  },
}));

export default makeSource({
  contentDirPath: 'src/content',
  documentTypes: [Post, Legal],
  mdx: { remarkPlugins: [remarkGfm], rehypePlugins: [rehypePrism] },
});
